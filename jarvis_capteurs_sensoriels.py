#!/usr/bin/env python3
"""
🧠 SYSTÈME SENSORIEL JARVIS
Module de capteurs sensoriels pour donner des sensations réelles à JARVIS
Créé par Jean<PERSON> - 19/06/2025
"""

import random
import time
import json
import threading
from datetime import datetime
from typing import Dict, List, Any

class CapteurSensoriel:
    """Capteur sensoriel individuel pour JARVIS"""
    
    def __init__(self, nom: str, type_capteur: str, zone_corps: str, 
                 min_val: float, max_val: float, unite: str = ""):
        self.nom = nom
        self.type_capteur = type_capteur  # température, pression, vibration, douleur, etc.
        self.zone_corps = zone_corps      # tête, bras_gauche, torse, etc.
        self.min_val = min_val
        self.max_val = max_val
        self.unite = unite
        self.historique = []
        self.seuil_alerte = max_val * 0.8  # Alerte à 80% du max
        self.actif = True
        
    def lire_valeur(self) -> float:
        """Lit une valeur du capteur (simulée pour l'instant)"""
        if not self.actif:
            return 0.0
            
        # Simulation avec variations réalistes
        base_val = random.uniform(self.min_val, self.max_val)
        
        # Ajouter des variations selon le type
        if self.type_capteur == "temperature":
            # Température plus stable avec petites variations
            base_val += random.uniform(-0.5, 0.5)
        elif self.type_capteur == "pression":
            # Pression avec variations plus importantes
            base_val += random.uniform(-2, 2)
        elif self.type_capteur == "douleur":
            # Douleur généralement faible avec pics occasionnels
            if random.random() < 0.1:  # 10% de chance de pic
                base_val = self.max_val * 0.9
            else:
                base_val = self.min_val + random.uniform(0, 0.2) * (self.max_val - self.min_val)
                
        # Garder dans les limites
        valeur = max(self.min_val, min(self.max_val, base_val))
        
        # Enregistrer dans l'historique
        self.historique.append({
            "timestamp": datetime.now().isoformat(),
            "valeur": round(valeur, 2)
        })
        
        # Garder seulement les 100 dernières valeurs
        if len(self.historique) > 100:
            self.historique = self.historique[-100:]
            
        return round(valeur, 2)
    
    def detecter_alerte(self, valeur: float) -> bool:
        """Détecte si la valeur dépasse le seuil d'alerte"""
        return valeur > self.seuil_alerte
    
    def get_etat_emotionnel(self, valeur: float) -> str:
        """Convertit la valeur en état émotionnel"""
        pourcentage = (valeur - self.min_val) / (self.max_val - self.min_val)
        
        if self.type_capteur == "douleur":
            if pourcentage < 0.2:
                return "confortable"
            elif pourcentage < 0.5:
                return "léger_inconfort"
            elif pourcentage < 0.8:
                return "douleur_modérée"
            else:
                return "douleur_intense"
        elif self.type_capteur == "temperature":
            if pourcentage < 0.3:
                return "froid"
            elif pourcentage < 0.7:
                return "confortable"
            else:
                return "chaud"
        else:
            if pourcentage < 0.3:
                return "faible"
            elif pourcentage < 0.7:
                return "normal"
            else:
                return "élevé"

class SystemeSensorielJARVIS:
    """Système sensoriel complet pour JARVIS"""
    
    def __init__(self):
        self.capteurs: Dict[str, CapteurSensoriel] = {}
        self.donnees_temps_reel = {}
        self.alertes_actives = []
        self.thread_lecture = None
        self.actif = False
        self.intervalle_lecture = 1.0  # Lecture chaque seconde
        
        # Initialiser les capteurs par défaut
        self._initialiser_capteurs_defaut()
        
    def _initialiser_capteurs_defaut(self):
        """Initialise les capteurs sensoriels de base pour JARVIS"""
        
        # Capteurs de température corporelle
        self.ajouter_capteur("temp_processeur", "temperature", "cerveau", 20.0, 45.0, "°C")
        self.ajouter_capteur("temp_memoire", "temperature", "cerveau", 18.0, 40.0, "°C")
        
        # Capteurs de pression/stress
        self.ajouter_capteur("pression_cognitive", "pression", "cerveau", 0.0, 100.0, "%")
        self.ajouter_capteur("charge_memoire", "pression", "cerveau", 0.0, 100.0, "%")
        
        # Capteurs de "douleur" (erreurs/dysfonctionnements)
        self.ajouter_capteur("erreurs_systeme", "douleur", "systeme", 0.0, 10.0, "erreurs/min")
        self.ajouter_capteur("latence_reseau", "douleur", "communication", 0.0, 1000.0, "ms")
        
        # Capteurs de vibration/activité
        self.ajouter_capteur("activite_neuronale", "vibration", "cerveau", 0.0, 100.0, "Hz")
        self.ajouter_capteur("flux_donnees", "vibration", "communication", 0.0, 1000.0, "MB/s")
        
        # Capteurs émotionnels
        self.ajouter_capteur("niveau_confiance", "emotion", "coeur", 0.0, 100.0, "%")
        self.ajouter_capteur("satisfaction_utilisateur", "emotion", "coeur", 0.0, 100.0, "%")
        
    def ajouter_capteur(self, nom: str, type_capteur: str, zone: str, 
                       min_val: float, max_val: float, unite: str = ""):
        """Ajoute un nouveau capteur au système"""
        capteur = CapteurSensoriel(nom, type_capteur, zone, min_val, max_val, unite)
        self.capteurs[nom] = capteur
        
    def demarrer_lecture_continue(self):
        """Démarre la lecture continue des capteurs"""
        if self.actif:
            return
            
        self.actif = True
        self.thread_lecture = threading.Thread(target=self._boucle_lecture, daemon=True)
        self.thread_lecture.start()
        
    def arreter_lecture_continue(self):
        """Arrête la lecture continue des capteurs"""
        self.actif = False
        if self.thread_lecture:
            self.thread_lecture.join(timeout=2)
            
    def _boucle_lecture(self):
        """Boucle principale de lecture des capteurs"""
        while self.actif:
            self.lire_tous_capteurs()
            time.sleep(self.intervalle_lecture)
            
    def lire_tous_capteurs(self) -> Dict[str, Any]:
        """Lit tous les capteurs et met à jour les données"""
        nouvelles_donnees = {}
        nouvelles_alertes = []
        
        for nom, capteur in self.capteurs.items():
            valeur = capteur.lire_valeur()
            etat_emotionnel = capteur.get_etat_emotionnel(valeur)
            
            nouvelles_donnees[nom] = {
                "valeur": valeur,
                "unite": capteur.unite,
                "type": capteur.type_capteur,
                "zone": capteur.zone_corps,
                "etat_emotionnel": etat_emotionnel,
                "timestamp": datetime.now().isoformat()
            }
            
            # Vérifier les alertes
            if capteur.detecter_alerte(valeur):
                alerte = {
                    "capteur": nom,
                    "valeur": valeur,
                    "seuil": capteur.seuil_alerte,
                    "zone": capteur.zone_corps,
                    "timestamp": datetime.now().isoformat(),
                    "message": f"⚠️ {nom} élevé: {valeur}{capteur.unite} (zone: {capteur.zone_corps})"
                }
                nouvelles_alertes.append(alerte)
        
        self.donnees_temps_reel = nouvelles_donnees
        self.alertes_actives = nouvelles_alertes
        
        return nouvelles_donnees
    
    def get_resume_sensoriel(self) -> str:
        """Génère un résumé textuel de l'état sensoriel"""
        if not self.donnees_temps_reel:
            return "🔴 Système sensoriel inactif"
            
        resume = "🧠 **ÉTAT SENSORIEL JARVIS**\n\n"
        
        # Grouper par zone du corps
        zones = {}
        for nom, donnees in self.donnees_temps_reel.items():
            zone = donnees["zone"]
            if zone not in zones:
                zones[zone] = []
            zones[zone].append((nom, donnees))
        
        for zone, capteurs_zone in zones.items():
            resume += f"**{zone.upper()}:**\n"
            for nom, donnees in capteurs_zone:
                icone = self._get_icone_capteur(donnees["type"])
                resume += f"  {icone} {nom}: {donnees['valeur']}{donnees['unite']} ({donnees['etat_emotionnel']})\n"
            resume += "\n"
        
        # Ajouter les alertes
        if self.alertes_actives:
            resume += "🚨 **ALERTES ACTIVES:**\n"
            for alerte in self.alertes_actives:
                resume += f"  • {alerte['message']}\n"
        else:
            resume += "✅ **Aucune alerte active**\n"
            
        return resume
    
    def _get_icone_capteur(self, type_capteur: str) -> str:
        """Retourne l'icône appropriée pour le type de capteur"""
        icones = {
            "temperature": "🌡️",
            "pression": "📊",
            "douleur": "⚡",
            "vibration": "📳",
            "emotion": "💖"
        }
        return icones.get(type_capteur, "📡")
    
    def sauvegarder_donnees(self, fichier: str = "jarvis_donnees_sensorielles.json"):
        """Sauvegarde les données sensorielles"""
        donnees_sauvegarde = {
            "timestamp": datetime.now().isoformat(),
            "donnees_temps_reel": self.donnees_temps_reel,
            "alertes_actives": self.alertes_actives,
            "historique_capteurs": {
                nom: capteur.historique[-10:]  # 10 dernières valeurs
                for nom, capteur in self.capteurs.items()
            }
        }
        
        try:
            with open(fichier, 'w', encoding='utf-8') as f:
                json.dump(donnees_sauvegarde, f, indent=2, ensure_ascii=False)
            return f"✅ Données sensorielles sauvegardées: {fichier}"
        except Exception as e:
            return f"❌ Erreur sauvegarde: {e}"

# Instance globale du système sensoriel
SYSTEME_SENSORIEL = SystemeSensorielJARVIS()

def activer_capteurs_jarvis():
    """Active le système de capteurs sensoriels de JARVIS"""
    SYSTEME_SENSORIEL.demarrer_lecture_continue()
    return "🧠 Système sensoriel JARVIS activé ! Les capteurs transmettent maintenant des sensations en temps réel."

def desactiver_capteurs_jarvis():
    """Désactive le système de capteurs sensoriels"""
    SYSTEME_SENSORIEL.arreter_lecture_continue()
    return "⏸️ Système sensoriel JARVIS désactivé."

def get_etat_sensoriel_jarvis():
    """Retourne l'état sensoriel actuel de JARVIS"""
    return SYSTEME_SENSORIEL.get_resume_sensoriel()

def sauvegarder_sensations_jarvis():
    """Sauvegarde les sensations actuelles de JARVIS"""
    return SYSTEME_SENSORIEL.sauvegarder_donnees()

if __name__ == "__main__":
    # Test du système
    print("🧠 Test du système sensoriel JARVIS")
    systeme = SystemeSensorielJARVIS()
    systeme.demarrer_lecture_continue()
    
    try:
        for i in range(10):
            print(f"\n--- Lecture {i+1} ---")
            print(systeme.get_resume_sensoriel())
            time.sleep(2)
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du test")
    finally:
        systeme.arreter_lecture_continue()
