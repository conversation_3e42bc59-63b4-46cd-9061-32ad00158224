#!/bin/bash

# 🚀 DÉMARRAGE DEEPSEEK R1 8B - TURBO ULTRA OPTIMISÉ
# Tous les accélérateurs activés pour vitesse maximale

echo "🚀 DÉMARRAGE DEEPSEEK R1 8B - MODE TURBO ULTRA"
echo "=============================================="

# ACCÉLÉRATEURS TURBO PERMANENTS - S'INSTALLENT AUTOMATIQUEMENT
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512,expandable_segments:True
export VLLM_USE_MODELSCOPE=false
export VLLM_WORKER_MULTIPROC_METHOD=spawn
export VLLM_ENGINE_ITERATION_TIMEOUT_S=15
export VLLM_ATTENTION_BACKEND=FLASHINFER
export VLLM_USE_TRITON_FLASH_ATTN=1
export VLLM_FLASH_ATTN_CHUNK_SIZE=1
export VLLM_FLASH_ATTN_SPLIT_K=1
export VLLM_TARGET_DEVICE=cuda
export VLLM_CPU_KVCACHE_SPACE=40
export VLLM_CPU_OMP_THREADS_BIND=1

# ACCÉLÉRATEURS RÉFLEXION CRITIQUES - EMPÊCHE BLOCAGE
export LLAMA_REASONING_TIMEOUT=10
export LLAMA_THINKING_MAX_TOKENS=50
export LLAMA_FORCE_RESPONSE=1
export LLAMA_SKIP_THINKING=0
export LLAMA_REASONING_FORMAT=compact

# ACCÉLÉRATEURS CASCADE - BRANCHÉS EN SÉRIE
export TURBO_CASCADE_LEVEL_1=flash_attention
export TURBO_CASCADE_LEVEL_2=batch_processing
export TURBO_CASCADE_LEVEL_3=memory_optimization
export TURBO_CASCADE_LEVEL_4=reasoning_acceleration
export TURBO_CASCADE_ACTIVE=1

# Optimisations système
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
export OPENBLAS_NUM_THREADS=8
export VECLIB_MAXIMUM_THREADS=8
export NUMEXPR_NUM_THREADS=8

echo "✅ ACCÉLÉRATEURS TURBO ACTIVÉS"
echo "🔥 Variables d'environnement configurées"

# Vérifier si le port 8000 est libre
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ Port 8000 déjà utilisé - Arrêt du processus..."
    pkill -f "vllm.*8000" || pkill -f "llama.*8000" || echo "Aucun processus trouvé"
    sleep 3
fi

# Activer l'environnement virtuel
if [ -d "venv_deepseek" ]; then
    echo "🔄 Activation environnement virtuel..."
    source venv_deepseek/bin/activate
    echo "✅ Environnement virtuel activé"
fi

echo "🧠 Démarrage DeepSeek R1 8B avec TOUS LES ACCÉLÉRATEURS..."
echo "📡 Port: 8000 (API OpenAI compatible)"
echo "🔗 URL: http://localhost:8000"
echo "⚡ Mode: TURBO ULTRA (vitesse maximale)"
echo ""
echo "⏳ Chargement avec accélérateurs (30-60 secondes)..."
echo ""

# DÉMARRAGE LLAMA-SERVER TURBO ULTRA - VITESSE MAXIMALE
# Fallback si VLLM n'est pas disponible
if command -v vllm &> /dev/null; then
    echo "🚀 Utilisation VLLM (optimal)"
    vllm serve "deepseek-ai/DeepSeek-R1-0528" \
        --host 0.0.0.0 \
        --port 8000 \
        --max-model-len 1024 \
        --tensor-parallel-size 1 \
        --gpu-memory-utilization 0.95 \
        --max-num-seqs 128 \
        --max-num-batched-tokens 32768 \
        --enable-prefix-caching \
        --disable-log-stats \
        --trust-remote-code \
        --api-key EMPTY \
        --served-model-name "deepseek-r1-turbo"
else
    echo "🔄 Utilisation llama-server (fallback turbo)"
    # Chercher le modèle GGUF
    MODEL_PATH=""
    if [ -f "/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf" ]; then
        MODEL_PATH="/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf"
    elif [ -f "DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf" ]; then
        MODEL_PATH="DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf"
    else
        echo "❌ Modèle GGUF non trouvé"
        exit 1
    fi

    echo "📁 Modèle: $MODEL_PATH"

    llama-server \
        --model "$MODEL_PATH" \
        --host 0.0.0.0 \
        --port 8000 \
        --ctx-size 2048 \
        --threads 8 \
        --n-gpu-layers 32 \
        --batch-size 2048 \
        --ubatch-size 512 \
        --n-predict 1024 \
        --timeout 60 \
        --flash-attn \
        --mlock \
        --numa distribute \
        --cont-batching \
        --parallel 4 \
        --defrag-thold 0.1 \
        --verbose
fi

echo ""
echo "🛑 VLLM TURBO arrêté"
