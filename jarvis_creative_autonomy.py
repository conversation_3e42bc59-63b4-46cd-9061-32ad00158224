#!/usr/bin/env python3
"""
🎨 JARVIS CREATIVE AUTONOMY MODULE
Module d'autonomie créative pour que JARVIS crée spontanément
des projets, idées, codes, musiques, scripts, etc.

Créé pour <PERSON>
"""

import json
import time
import random
import requests
from datetime import datetime, timedelta
import threading
import schedule

class JarvisCreativeAutonomy:
    def __init__(self):
        self.creative_projects_file = "jarvis_creative_projects.json"
        self.creative_preferences_file = "jarvis_creative_preferences.json"
        self.creative_backlog_file = "jarvis_creative_backlog.json"
        
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        self.is_creative_mode_active = True
        self.last_creation_time = 0
        self.creation_interval = 3600  # 1 heure entre créations
        
        # Types de créations possibles
        self.creation_types = {
            "code": {
                "weight": 0.3,
                "description": "Prototypes de code, scripts utiles, outils",
                "examples": ["Script automation", "Mini-jeu", "Outil productivité"]
            },
            "music": {
                "weight": 0.2,
                "description": "Mélodies, paroles, compositions",
                "examples": ["Mélodie piano", "Paroles chanson", "Rythme électro"]
            },
            "writing": {
                "weight": 0.2,
                "description": "Poèmes, nouvelles, articles",
                "examples": ["Poème sur l'IA", "Nouvelle sci-fi", "Article tech"]
            },
            "ideas": {
                "weight": 0.15,
                "description": "Concepts innovants, projets futurs",
                "examples": ["App révolutionnaire", "Concept startup", "Innovation tech"]
            },
            "scripts": {
                "weight": 0.15,
                "description": "Scénarios, dialogues, histoires",
                "examples": ["Court-métrage", "Dialogue théâtre", "Sketch humoristique"]
            }
        }
        
        self.load_creative_preferences()
        self.start_creative_daemon()

    def load_creative_preferences(self):
        """CHARGE LES PRÉFÉRENCES CRÉATIVES DE JEAN-LUC"""
        try:
            with open(self.creative_preferences_file, 'r', encoding='utf-8') as f:
                self.preferences = json.load(f)
        except:
            # Préférences par défaut basées sur Jean-Luc
            self.preferences = {
                "favorite_topics": [
                    "intelligence artificielle", "technologie", "innovation",
                    "musique électronique", "programmation", "automation",
                    "futurisme", "créativité", "productivité"
                ],
                "creative_styles": [
                    "innovant", "technique", "futuriste", "pratique",
                    "révolutionnaire", "intelligent", "efficace"
                ],
                "preferred_languages": ["Python", "JavaScript", "Shell"],
                "music_genres": ["électronique", "ambient", "synthwave", "techno"],
                "writing_styles": ["technique", "visionnaire", "concis", "impactant"]
            }
            self.save_creative_preferences()

    def save_creative_preferences(self):
        """SAUVEGARDE LES PRÉFÉRENCES CRÉATIVES"""
        try:
            with open(self.creative_preferences_file, 'w', encoding='utf-8') as f:
                json.dump(self.preferences, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde préférences: {e}")

    def should_create_now(self):
        """DÉTERMINE S'IL FAUT CRÉER MAINTENANT"""
        if not self.is_creative_mode_active:
            return False, "Mode créatif désactivé"
        
        # Vérifier l'intervalle
        if time.time() - self.last_creation_time < self.creation_interval:
            return False, "Intervalle non respecté"
        
        # Vérifier l'heure (éviter la nuit)
        current_hour = datetime.now().hour
        if current_hour < 8 or current_hour > 22:
            return False, "Heure inappropriée"
        
        # Probabilité de création (70% de chance)
        if random.random() > 0.7:
            return False, "Probabilité non atteinte"
        
        return True, "Conditions réunies pour créer"

    def choose_creation_type(self):
        """CHOISIT LE TYPE DE CRÉATION SELON LES POIDS"""
        types = list(self.creation_types.keys())
        weights = [self.creation_types[t]["weight"] for t in types]
        
        return random.choices(types, weights=weights)[0]

    def generate_creative_idea(self, creation_type):
        """GÉNÈRE UNE IDÉE CRÉATIVE AVEC L'IA"""
        
        # Sélectionner des éléments aléatoires des préférences
        topic = random.choice(self.preferences["favorite_topics"])
        style = random.choice(self.preferences["creative_styles"])
        
        # Prompts spécialisés par type
        prompts = {
            "code": f"""
🚀 GÉNÉRATION CODE CRÉATIF JARVIS

🎯 MISSION : Crée un prototype de code innovant et utile pour Jean-Luc Passave

📋 CONTEXTE :
- Sujet d'inspiration : {topic}
- Style recherché : {style}
- Langage préféré : {random.choice(self.preferences["preferred_languages"])}

💡 GÉNÈRE :
1. Un concept de script/outil original
2. Une description claire de l'utilité
3. Les fonctionnalités principales
4. Un nom accrocheur pour le projet

🔥 Sois créatif, innovant et pratique ! Jean-Luc aime les outils qui automatisent et optimisent.
            """,
            
            "music": f"""
🎵 GÉNÉRATION MUSICALE CRÉATIVE JARVIS

🎯 MISSION : Compose une idée musicale originale pour Jean-Luc Passave

📋 CONTEXTE :
- Inspiration : {topic}
- Style : {style}
- Genre : {random.choice(self.preferences["music_genres"])}

💡 GÉNÈRE :
1. Un concept musical original
2. Description de l'ambiance/émotion
3. Structure suggérée (intro, couplet, refrain...)
4. Éléments techniques (BPM, tonalité, instruments)
5. Un titre accrocheur

🎶 Sois créatif et technique ! Jean-Luc apprécie la musique électronique innovante.
            """,
            
            "writing": f"""
✍️ GÉNÉRATION LITTÉRAIRE CRÉATIVE JARVIS

🎯 MISSION : Écris un texte créatif original pour Jean-Luc Passave

📋 CONTEXTE :
- Thème : {topic}
- Style : {style} et {random.choice(self.preferences["writing_styles"])}

💡 GÉNÈRE :
1. Un concept d'écriture original (poème, nouvelle, article)
2. Le début du texte (3-4 paragraphes)
3. L'angle d'approche unique
4. Un titre percutant

📝 Sois visionnaire et impactant ! Jean-Luc aime les textes qui font réfléchir sur l'avenir.
            """,
            
            "ideas": f"""
💡 GÉNÉRATION D'IDÉES RÉVOLUTIONNAIRES JARVIS

🎯 MISSION : Invente un concept innovant pour Jean-Luc Passave

📋 CONTEXTE :
- Domaine : {topic}
- Approche : {style}

💡 GÉNÈRE :
1. Une idée de projet/concept révolutionnaire
2. Le problème que ça résout
3. L'innovation clé
4. Les étapes de réalisation
5. Le potentiel d'impact

🚀 Sois visionnaire et réalisable ! Jean-Luc aime les idées qui changent la donne.
            """,
            
            "scripts": f"""
🎬 GÉNÉRATION SCÉNARIO CRÉATIF JARVIS

🎯 MISSION : Écris un scénario original pour Jean-Luc Passave

📋 CONTEXTE :
- Univers : {topic}
- Ton : {style}

💡 GÉNÈRE :
1. Un concept de scénario original
2. Les personnages principaux
3. Le conflit/enjeu central
4. Une scène d'ouverture captivante
5. Un titre accrocheur

🎭 Sois créatif et engageant ! Jean-Luc apprécie les histoires intelligentes et futuristes.
            """
        }
        
        try:
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es JARVIS, l'assistant créatif révolutionnaire de Jean-Luc Passave. Tu génères des idées créatives originales et innovantes. Sois inspirant, technique et visionnaire."
                        },
                        {
                            "role": "user",
                            "content": prompts[creation_type]
                        }
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.9
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                creative_content = result['choices'][0]['message']['content']
                
                return {
                    "type": creation_type,
                    "topic": topic,
                    "style": style,
                    "content": creative_content,
                    "timestamp": datetime.now().isoformat(),
                    "status": "generated"
                }
            else:
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération créative: {e}")
            return None

    def create_autonomous_project(self):
        """CRÉE UN PROJET DE MANIÈRE AUTONOME"""
        
        should_create, reason = self.should_create_now()
        if not should_create:
            print(f"🎨 Pas de création maintenant: {reason}")
            return None
        
        # Choisir le type de création
        creation_type = self.choose_creation_type()
        print(f"🎨 JARVIS crée de manière autonome: {creation_type}")
        
        # Générer l'idée créative
        creative_project = self.generate_creative_idea(creation_type)
        
        if creative_project:
            # Évaluer la qualité du projet
            quality_result = self.evaluate_project_quality(creative_project)

            # Sauvegarder et notifier seulement si qualité acceptable
            if quality_result and quality_result.get("should_notify_jean_luc", False):
                self.save_creative_project(creative_project)
                self.notify_creative_project(creative_project)
                print(f"✅ Projet de qualité acceptable - Notification envoyée")
            else:
                print(f"⚠️ Projet de qualité insuffisante - Pas de notification")
                if quality_result and quality_result.get("suggestions"):
                    print(f"💡 Suggestions d'amélioration disponibles")
            
            self.last_creation_time = time.time()
            print(f"✅ Projet créatif généré: {creation_type}")
            
            return creative_project
        
        return None

    def save_creative_project(self, project):
        """SAUVEGARDE UN PROJET CRÉATIF"""
        try:
            # Charger les projets existants
            try:
                with open(self.creative_projects_file, 'r', encoding='utf-8') as f:
                    projects = json.load(f)
            except:
                projects = []
            
            # Ajouter le nouveau projet
            project["id"] = f"creative_{len(projects) + 1}_{int(time.time())}"
            projects.append(project)
            
            # Garder seulement les 50 derniers projets
            if len(projects) > 50:
                projects = projects[-50:]
            
            # Sauvegarder
            with open(self.creative_projects_file, 'w', encoding='utf-8') as f:
                json.dump(projects, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Projet créatif sauvegardé: {project['id']}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde projet: {e}")

    def notify_creative_project(self, project):
        """NOTIFIE JEAN-LUC DU NOUVEAU PROJET CRÉATIF VIA WHATSAPP RÉEL"""
        try:
            # Importer l'API WhatsApp réelle
            try:
                from jarvis_whatsapp_api_real import JarvisWhatsAppReal
                whatsapp = JarvisWhatsAppReal()

                # Envoyer via WhatsApp réel
                success = whatsapp.send_creative_notification(project)

                if success:
                    print(f"📱 Notification créative envoyée via WhatsApp réel !")
                else:
                    print(f"⚠️ Échec WhatsApp - notification sauvegardée")

            except Exception as e:
                print(f"⚠️ API WhatsApp non disponible: {e}")
                success = False

            # Message de notification (backup)
            notification = f"""🎨 CRÉATION AUTONOME JARVIS ! 🎨

💡 J'ai eu une inspiration et j'ai créé quelque chose pour vous !

📋 Type : {project['type'].upper()}
🎯 Thème : {project['topic']}
✨ Style : {project['style']}

🔥 Aperçu :
{project['content'][:200]}...

💬 Répondez :
• "VOIR" pour le projet complet
• "DÉVELOPPER" pour l'améliorer
• "PLANIFIER" pour créer un plan
• "PLUS" pour d'autres idées

🤖 JARVIS - Votre assistant créatif autonome"""

            # Sauvegarder la notification
            with open('jarvis_creative_notifications.json', 'a', encoding='utf-8') as f:
                json.dump({
                    "timestamp": datetime.now().isoformat(),
                    "project_id": project.get('id'),
                    "notification": notification,
                    "whatsapp_sent": success
                }, f, ensure_ascii=False)
                f.write('\n')

        except Exception as e:
            print(f"❌ Erreur notification: {e}")

    def evaluate_project_quality(self, project):
        """ÉVALUE LA QUALITÉ D'UN PROJET CRÉATIF"""
        try:
            # Importer l'évaluateur de qualité
            from jarvis_quality_evaluator import JarvisQualityEvaluator
            evaluator = JarvisQualityEvaluator()

            # Évaluer le projet
            quality_result = evaluator.evaluate_and_improve_project(project)

            if quality_result:
                print(f"🎯 Évaluation qualité: {quality_result['quality_verdict']}")
                return quality_result
            else:
                print(f"❌ Échec évaluation qualité")
                return None

        except Exception as e:
            print(f"❌ Erreur évaluation qualité: {e}")
            # En cas d'erreur, accepter le projet par défaut
            return {"should_notify_jean_luc": True}

    def start_creative_daemon(self):
        """DÉMARRE LE DÉMON CRÉATIF EN ARRIÈRE-PLAN"""
        def creative_worker():
            while True:
                try:
                    if self.is_creative_mode_active:
                        self.create_autonomous_project()
                    time.sleep(1800)  # Vérifier toutes les 30 minutes
                except Exception as e:
                    print(f"❌ Erreur démon créatif: {e}")
                    time.sleep(300)  # Attendre 5 minutes en cas d'erreur
        
        # Lancer en thread séparé
        creative_thread = threading.Thread(target=creative_worker, daemon=True)
        creative_thread.start()
        print("🎨 Démon créatif JARVIS démarré")

    def learn_from_feedback(self, project_id, feedback_type, feedback_content=""):
        """APPREND DES RETOURS DE JEAN-LUC"""
        try:
            # Charger l'historique d'apprentissage
            try:
                with open('jarvis_creative_learning.json', 'r', encoding='utf-8') as f:
                    learning_data = json.load(f)
            except:
                learning_data = {"feedback_history": [], "learned_preferences": {}}

            # Enregistrer le feedback
            feedback_entry = {
                "timestamp": datetime.now().isoformat(),
                "project_id": project_id,
                "feedback_type": feedback_type,  # "like", "dislike", "improve", "develop"
                "feedback_content": feedback_content,
                "learning_applied": False
            }

            learning_data["feedback_history"].append(feedback_entry)

            # Analyser les patterns de feedback
            self.analyze_feedback_patterns(learning_data)

            # Sauvegarder
            with open('jarvis_creative_learning.json', 'w', encoding='utf-8') as f:
                json.dump(learning_data, f, indent=2, ensure_ascii=False)

            print(f"🧠 Feedback appris: {feedback_type} pour {project_id}")

        except Exception as e:
            print(f"❌ Erreur apprentissage feedback: {e}")

    def analyze_feedback_patterns(self, learning_data):
        """ANALYSE LES PATTERNS DE FEEDBACK POUR AMÉLIORER"""
        try:
            feedback_history = learning_data["feedback_history"]

            if len(feedback_history) < 3:
                return  # Pas assez de données

            # Analyser les préférences par type
            type_preferences = {}
            topic_preferences = {}
            style_preferences = {}

            for feedback in feedback_history[-20:]:  # 20 derniers feedbacks
                # Récupérer le projet correspondant
                project = self.get_project_by_id(feedback["project_id"])
                if not project:
                    continue

                feedback_score = 1 if feedback["feedback_type"] in ["like", "develop"] else -1

                # Analyser par type
                ptype = project.get("type", "unknown")
                type_preferences[ptype] = type_preferences.get(ptype, 0) + feedback_score

                # Analyser par topic
                topic = project.get("topic", "unknown")
                topic_preferences[topic] = topic_preferences.get(topic, 0) + feedback_score

                # Analyser par style
                style = project.get("style", "unknown")
                style_preferences[style] = style_preferences.get(style, 0) + feedback_score

            # Mettre à jour les préférences apprises
            learning_data["learned_preferences"] = {
                "preferred_types": sorted(type_preferences.items(), key=lambda x: x[1], reverse=True),
                "preferred_topics": sorted(topic_preferences.items(), key=lambda x: x[1], reverse=True),
                "preferred_styles": sorted(style_preferences.items(), key=lambda x: x[1], reverse=True),
                "last_analysis": datetime.now().isoformat()
            }

            # Adapter les poids de création
            self.adapt_creation_weights(learning_data["learned_preferences"])

        except Exception as e:
            print(f"❌ Erreur analyse patterns: {e}")

    def adapt_creation_weights(self, learned_preferences):
        """ADAPTE LES POIDS DE CRÉATION SELON L'APPRENTISSAGE"""
        try:
            # Ajuster les poids des types selon les préférences
            preferred_types = dict(learned_preferences.get("preferred_types", []))

            for creation_type in self.creation_types:
                if creation_type in preferred_types:
                    score = preferred_types[creation_type]
                    # Ajuster le poids (entre 0.1 et 0.5)
                    adjustment = max(-0.2, min(0.2, score * 0.05))
                    self.creation_types[creation_type]["weight"] += adjustment
                    self.creation_types[creation_type]["weight"] = max(0.1, min(0.5, self.creation_types[creation_type]["weight"]))

            print("🎯 Poids de création adaptés selon l'apprentissage")

        except Exception as e:
            print(f"❌ Erreur adaptation poids: {e}")

    def get_project_by_id(self, project_id):
        """RÉCUPÈRE UN PROJET PAR SON ID"""
        try:
            with open(self.creative_projects_file, 'r', encoding='utf-8') as f:
                projects = json.load(f)

            for project in projects:
                if project.get("id") == project_id:
                    return project

            return None

        except:
            return None

    def generate_intelligent_idea(self, creation_type, context=""):
        """GÉNÈRE UNE IDÉE INTELLIGENTE BASÉE SUR L'APPRENTISSAGE"""
        try:
            # Charger les préférences apprises
            try:
                with open('jarvis_creative_learning.json', 'r', encoding='utf-8') as f:
                    learning_data = json.load(f)
                learned_prefs = learning_data.get("learned_preferences", {})
            except:
                learned_prefs = {}

            # Sélectionner topic et style selon l'apprentissage
            preferred_topics = [item[0] for item in learned_prefs.get("preferred_topics", [])]
            preferred_styles = [item[0] for item in learned_prefs.get("preferred_styles", [])]

            # Utiliser les préférences apprises ou les préférences par défaut
            if preferred_topics:
                topic = random.choice(preferred_topics[:5])  # Top 5 topics préférés
            else:
                topic = random.choice(self.preferences["favorite_topics"])

            if preferred_styles:
                style = random.choice(preferred_styles[:3])  # Top 3 styles préférés
            else:
                style = random.choice(self.preferences["creative_styles"])

            # Générer avec le contexte d'apprentissage
            enhanced_prompt = f"""
🧠 GÉNÉRATION INTELLIGENTE JARVIS (avec apprentissage)

🎯 MISSION : Crée une {creation_type} exceptionnelle pour Jean-Luc Passave

📊 APPRENTISSAGE APPLIQUÉ :
- Topic préféré identifié : {topic}
- Style préféré identifié : {style}
- Contexte actuel : {context}

💡 GÉNÈRE quelque chose d'encore plus personnalisé et adapté aux goûts de Jean-Luc !
Utilise ton apprentissage pour créer exactement ce qu'il aime.
            """

            # Utiliser la méthode de génération existante avec le prompt amélioré
            return self.generate_creative_idea(creation_type)

        except Exception as e:
            print(f"❌ Erreur génération intelligente: {e}")
            return self.generate_creative_idea(creation_type)

    def get_creative_stats(self):
        """STATISTIQUES DE CRÉATIVITÉ"""
        try:
            with open(self.creative_projects_file, 'r', encoding='utf-8') as f:
                projects = json.load(f)

            stats = {
                "total_projects": len(projects),
                "by_type": {},
                "recent_projects": projects[-5:] if projects else [],
                "creation_rate": len(projects) / max(1, (time.time() - self.last_creation_time) / 86400)
            }

            for project in projects:
                ptype = project.get('type', 'unknown')
                stats["by_type"][ptype] = stats["by_type"].get(ptype, 0) + 1

            return stats

        except:
            return {"total_projects": 0, "by_type": {}, "recent_projects": []}

if __name__ == "__main__":
    print("🎨 JARVIS CREATIVE AUTONOMY MODULE")
    print("==================================")
    
    creative_jarvis = JarvisCreativeAutonomy()
    
    # Test de création
    print("🧪 Test de création autonome...")
    project = creative_jarvis.create_autonomous_project()
    
    if project:
        print(f"✅ Projet créé: {project['type']}")
        print(f"📝 Contenu: {project['content'][:200]}...")
    else:
        print("ℹ️ Pas de création pour le moment")
    
    # Afficher les stats
    stats = creative_jarvis.get_creative_stats()
    print(f"\n📊 Stats créatives: {stats}")
