#!/usr/bin/env python3
"""
🔐 JARVIS VPN SECURITY MODULE
Protection VPN pour toutes les connexions Internet de JARVIS

Créé pour <PERSON>
"""

import requests
import json
import time
import subprocess
import os
from datetime import datetime
import socket
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class JarvisVPNSecurity:
    def __init__(self):
        self.vpn_config = {
            "enabled": True,
            "auto_connect": True,
            "preferred_servers": ["France", "Switzerland", "Netherlands"],
            "protocols": ["OpenVPN", "WireGuard", "IKEv2"],
            "encryption": "AES-256",
            "kill_switch": True,
            "dns_leak_protection": True,
            "ipv6_leak_protection": True
        }
        
        self.security_log = []
        self.connection_status = "disconnected"
        self.current_ip = None
        self.vpn_ip = None
        
    def check_vpn_status(self):
        """VÉRIFIER LE STATUT VPN"""
        try:
            # Vérifier l'IP publique
            response = requests.get("https://httpbin.org/ip", timeout=10)
            current_ip = response.json().get("origin", "")
            
            # Vérifier si on utilise un VPN (IP différente de l'IP locale)
            local_ip = socket.gethostbyname(socket.gethostname())
            
            if current_ip != local_ip and current_ip:
                self.connection_status = "connected"
                self.vpn_ip = current_ip
                return {
                    "status": "🟢 VPN Actif",
                    "ip": current_ip,
                    "protected": True
                }
            else:
                self.connection_status = "disconnected"
                self.current_ip = current_ip
                return {
                    "status": "🔴 VPN Inactif",
                    "ip": current_ip,
                    "protected": False
                }
                
        except Exception as e:
            return {
                "status": "🟡 Erreur vérification",
                "error": str(e),
                "protected": False
            }
    
    def get_secure_session(self):
        """CRÉER SESSION SÉCURISÉE POUR REQUÊTES"""
        session = requests.Session()
        
        # Configuration sécurisée
        session.headers.update({
            'User-Agent': 'JARVIS-SecureBot/1.0 (Privacy-Focused AI Assistant)',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',  # Do Not Track
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        })
        
        # Retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Désactiver les warnings SSL si nécessaire
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        return session
    
    def secure_web_request(self, url, method="GET", data=None, headers=None):
        """REQUÊTE WEB SÉCURISÉE AVEC VPN"""
        try:
            # Vérifier le VPN avant la requête
            vpn_status = self.check_vpn_status()
            
            if not vpn_status.get("protected", False) and self.vpn_config["enabled"]:
                self.log_security_event("warning", f"Requête sans VPN vers {url}")
                
                # Tentative de connexion VPN automatique
                if self.vpn_config["auto_connect"]:
                    self.attempt_vpn_connection()
            
            # Créer session sécurisée
            session = self.get_secure_session()
            
            # Headers additionnels si fournis
            if headers:
                session.headers.update(headers)
            
            # Effectuer la requête
            if method.upper() == "GET":
                response = session.get(url, timeout=30)
            elif method.upper() == "POST":
                response = session.post(url, json=data, timeout=30)
            else:
                response = session.request(method, url, json=data, timeout=30)
            
            # Log de sécurité
            self.log_security_event("info", f"Requête sécurisée vers {url} - Status: {response.status_code}")
            
            return response
            
        except Exception as e:
            self.log_security_event("error", f"Erreur requête sécurisée vers {url}: {e}")
            raise
    
    def attempt_vpn_connection(self):
        """TENTATIVE DE CONNEXION VPN AUTOMATIQUE"""
        try:
            # Vérifier les VPN disponibles sur macOS
            vpn_commands = [
                # NordVPN
                "nordvpn connect",
                # ExpressVPN  
                "expressvpn connect",
                # Surfshark
                "surfshark-vpn attack",
                # ProtonVPN
                "protonvpn connect --fastest",
                # Tunnelblick (OpenVPN)
                "osascript -e 'tell application \"Tunnelblick\" to connect \"config-name\"'"
            ]
            
            for cmd in vpn_commands:
                try:
                    result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        self.log_security_event("success", f"VPN connecté via: {cmd}")
                        time.sleep(5)  # Attendre la connexion
                        return True
                except:
                    continue
            
            self.log_security_event("warning", "Aucun VPN automatique disponible")
            return False
            
        except Exception as e:
            self.log_security_event("error", f"Erreur connexion VPN: {e}")
            return False
    
    def log_security_event(self, level, message):
        """ENREGISTRER ÉVÉNEMENT DE SÉCURITÉ"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "message": message,
            "vpn_status": self.connection_status,
            "ip": self.vpn_ip or self.current_ip
        }
        
        self.security_log.append(event)
        
        # Garder seulement les 100 derniers événements
        if len(self.security_log) > 100:
            self.security_log = self.security_log[-100:]
        
        # Sauvegarder dans un fichier
        try:
            with open("jarvis_security_log.json", "w") as f:
                json.dump(self.security_log, f, indent=2)
        except:
            pass
        
        # Affichage console avec couleurs
        colors = {
            "info": "\033[94m",      # Bleu
            "success": "\033[92m",   # Vert
            "warning": "\033[93m",   # Jaune
            "error": "\033[91m",     # Rouge
            "end": "\033[0m"         # Reset
        }
        
        color = colors.get(level, colors["info"])
        print(f"{color}🔐 JARVIS Security [{level.upper()}]: {message}{colors['end']}")
    
    def get_security_report(self):
        """RAPPORT DE SÉCURITÉ COMPLET"""
        vpn_status = self.check_vpn_status()
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "vpn_status": vpn_status,
            "configuration": self.vpn_config,
            "recent_events": self.security_log[-10:],  # 10 derniers événements
            "recommendations": []
        }
        
        # Recommandations de sécurité
        if not vpn_status.get("protected", False):
            report["recommendations"].append("🔴 Activer un VPN pour protéger les connexions")
        
        if not self.vpn_config["kill_switch"]:
            report["recommendations"].append("🟡 Activer le Kill Switch VPN")
        
        if not self.vpn_config["dns_leak_protection"]:
            report["recommendations"].append("🟡 Activer la protection DNS Leak")
        
        if not report["recommendations"]:
            report["recommendations"].append("✅ Configuration de sécurité optimale")
        
        return report

# Instance globale
jarvis_vpn = JarvisVPNSecurity()

def secure_request(url, method="GET", data=None, headers=None):
    """FONCTION GLOBALE POUR REQUÊTES SÉCURISÉES"""
    return jarvis_vpn.secure_web_request(url, method, data, headers)

def get_vpn_status():
    """FONCTION GLOBALE POUR STATUT VPN"""
    return jarvis_vpn.check_vpn_status()

def get_security_report():
    """FONCTION GLOBALE POUR RAPPORT SÉCURITÉ"""
    return jarvis_vpn.get_security_report()

if __name__ == "__main__":
    # Test du module
    print("🔐 Test du module de sécurité VPN JARVIS")
    
    # Vérifier le statut VPN
    status = get_vpn_status()
    print(f"Statut VPN: {status}")
    
    # Test de requête sécurisée
    try:
        response = secure_request("https://httpbin.org/ip")
        print(f"Test requête sécurisée: {response.json()}")
    except Exception as e:
        print(f"Erreur test: {e}")
    
    # Rapport de sécurité
    report = get_security_report()
    print(f"Rapport sécurité: {json.dumps(report, indent=2)}")
