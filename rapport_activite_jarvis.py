#!/usr/bin/env python3
"""
📊 RAPPORT D'ACTIVITÉ JARVIS
Analyse de l'activité entre midi et 16h30
Créé pour Jean-Luc <PERSON>ave
"""

import json
import os
from datetime import datetime, timedelta
import subprocess

def analyser_activite_jarvis(heure_debut="12:00", heure_fin="16:30"):
    """Analyser l'activité de JARVIS dans une période donnée"""
    
    print("📊 ================================")
    print("🤖 RAPPORT D'ACTIVITÉ JARVIS")
    print(f"⏰ Période: {heure_debut} - {heure_fin}")
    print("📊 ================================")
    print()
    
    # 1. Analyser la mémoire thermique
    print("🧠 ANALYSE MÉMOIRE THERMIQUE:")
    try:
        with open("thermal_memory_persistent.json", 'r') as f:
            thermal_data = json.load(f)
        
        conversations_today = thermal_data.get("conversations_by_date", {}).get("2025-06-19", [])
        
        # Filtrer par heure
        conversations_periode = []
        for conv in conversations_today:
            timestamp = conv.get("timestamp", "")
            if timestamp:
                try:
                    # Extraire l'heure
                    time_part = timestamp.split("T")[1].split(".")[0] if "T" in timestamp else timestamp
                    heure = time_part.split(":")[0] + ":" + time_part.split(":")[1]
                    
                    # Vérifier si dans la période
                    if heure_debut <= heure <= heure_fin:
                        conversations_periode.append(conv)
                except:
                    pass
        
        print(f"  📝 Conversations totales aujourd'hui: {len(conversations_today)}")
        print(f"  ⏰ Conversations {heure_debut}-{heure_fin}: {len(conversations_periode)}")
        
        if conversations_periode:
            print("\n  📋 DÉTAIL DES CONVERSATIONS:")
            for i, conv in enumerate(conversations_periode, 1):
                timestamp = conv.get("timestamp", "")
                message = conv.get("user_message", "")[:50] + "..." if len(conv.get("user_message", "")) > 50 else conv.get("user_message", "")
                print(f"    {i}. {timestamp.split('T')[1][:8] if 'T' in timestamp else timestamp} - {message}")
        else:
            print("  ❌ Aucune conversation enregistrée dans cette période")
            
    except FileNotFoundError:
        print("  ❌ Fichier mémoire thermique non trouvé")
    except Exception as e:
        print(f"  ❌ Erreur lecture mémoire: {e}")
    
    print()
    
    # 2. Analyser l'état des accélérateurs
    print("⚡ ÉTAT DES ACCÉLÉRATEURS:")
    try:
        with open("jarvis_accelerateurs_cascade_persistant.json", 'r') as f:
            accelerateurs = json.load(f)
        
        actifs = sum(1 for acc in accelerateurs.values() if acc.get("active", False))
        total = len(accelerateurs)
        
        print(f"  🟢 Accélérateurs actifs: {actifs}/{total}")
        print("  📋 DÉTAIL:")
        for nom, config in accelerateurs.items():
            statut = "🟢 ACTIF" if config.get("active", False) else "🔴 INACTIF"
            priority = config.get("priority", "N/A")
            description = config.get("description", "")
            print(f"    • {nom}: {statut} (Priorité: {priority}) - {description}")
            
    except FileNotFoundError:
        print("  ❌ Fichier accélérateurs non trouvé")
    except Exception as e:
        print(f"  ❌ Erreur lecture accélérateurs: {e}")
    
    print()
    
    # 3. Analyser les processus système
    print("🔧 PROCESSUS SYSTÈME:")
    try:
        # Vérifier processus JARVIS
        result = subprocess.run(["pgrep", "-f", "jarvis_interface_propre.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"  🟢 JARVIS actif (PID: {', '.join(pids)})")
        else:
            print("  🔴 JARVIS non actif")
        
        # Vérifier processus Electron
        result = subprocess.run(["pgrep", "-f", "jarvis_electron_force.js"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"  🟢 Electron actif (PID: {', '.join(pids)})")
        else:
            print("  🔴 Electron non actif")
            
    except Exception as e:
        print(f"  ❌ Erreur vérification processus: {e}")
    
    print()
    
    # 4. Analyser les fichiers de configuration
    print("📁 FICHIERS DE CONFIGURATION:")
    fichiers_config = [
        "jarvis_adaptive_config.json",
        "jarvis_auto_update_config.json",
        "thermal_memory_persistent.json"
    ]
    
    for fichier in fichiers_config:
        if os.path.exists(fichier):
            try:
                stat = os.stat(fichier)
                taille = stat.st_size
                modif = datetime.fromtimestamp(stat.st_mtime)
                print(f"  ✅ {fichier}: {taille} bytes, modifié {modif.strftime('%H:%M:%S')}")
            except Exception as e:
                print(f"  ⚠️ {fichier}: Erreur lecture - {e}")
        else:
            print(f"  ❌ {fichier}: Non trouvé")
    
    print()
    
    # 5. Résumé d'activité
    print("📊 RÉSUMÉ D'ACTIVITÉ:")
    
    # Calculer le temps d'activité estimé
    if conversations_periode:
        premiere = conversations_periode[0].get("timestamp", "")
        derniere = conversations_periode[-1].get("timestamp", "")
        print(f"  ⏰ Première activité: {premiere.split('T')[1][:8] if 'T' in premiere else premiere}")
        print(f"  ⏰ Dernière activité: {derniere.split('T')[1][:8] if 'T' in derniere else derniere}")
        print(f"  📊 Nombre d'interactions: {len(conversations_periode)}")
    else:
        print("  😴 JARVIS était en veille ou inactif pendant cette période")
        print("  💡 Possible raisons:")
        print("    • Interface fermée")
        print("    • Aucune interaction utilisateur")
        print("    • Processus en arrière-plan seulement")
    
    # État actuel
    print(f"\n🔄 ÉTAT ACTUEL ({datetime.now().strftime('%H:%M:%S')}):")
    try:
        import requests
        response = requests.get("http://127.0.0.1:7867", timeout=3)
        if response.status_code == 200:
            print("  🟢 JARVIS accessible et fonctionnel")
        else:
            print(f"  🟡 JARVIS répond mais code {response.status_code}")
    except:
        print("  🔴 JARVIS non accessible")
    
    print("\n" + "="*50)
    print("📋 Rapport généré avec succès !")
    print("💡 Pour plus de détails, consultez les logs individuels")

if __name__ == "__main__":
    analyser_activite_jarvis("12:00", "16:30")
