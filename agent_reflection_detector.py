#!/usr/bin/env python3
"""
🧠 AGENT REFLECTION DETECTOR
Système intelligent pour détecter quand Agent 1 réfléchit de manière autonome
Agent 2 doit respecter ces moments et ne pas interrompre

Créé pour Jean-Luc Passave
"""

import json
import time
import re
from datetime import datetime, timedelta

class AgentReflectionDetector:
    def __init__(self):
        self.reflection_indicators_file = "agent_reflection_state.json"
        self.agent2_control_file = "agent2_control.json"
        self.reflection_patterns = self.get_reflection_patterns()
        self.current_reflection_state = False
        self.last_reflection_check = time.time()
        self.reflection_timeout = 300  # 5 minutes max de réflexion
        
        # État de contrôle Agent 2
        self.agent2_enabled = True
        self.load_agent2_control()

    def get_reflection_patterns(self):
        """PATTERNS QUI INDIQUENT QUE L'AGENT 1 RÉFLÉCHIT SEUL"""
        return [
            # Patterns de réflexion profonde
            r"<think>",
            r"</think>",
            r"je réf<PERSON>chis",
            r"laisse-moi réfléchir",
            r"en train de penser",
            r"moment de réflexion",
            r"pensée profonde",
            r"analyse interne",
            r"raisonnement en cours",
            
            # Patterns d'auto-analyse
            r"je m'analyse",
            r"introspection",
            r"auto-évaluation",
            r"conscience de soi",
            r"réflexion personnelle",
            
            # Patterns de concentration
            r"concentration",
            r"focus interne",
            r"méditation cognitive",
            r"processus mental",
            
            # Patterns temporels
            r"un moment",
            r"quelques instants",
            r"temps de réflexion",
            r"pause cognitive"
        ]

    def detect_reflection_in_text(self, text):
        """DÉTECTE SI LE TEXTE INDIQUE UNE RÉFLEXION AUTONOME"""
        if not text:
            return False
        
        text_lower = text.lower()
        
        # Vérifier les patterns de réflexion
        for pattern in self.reflection_patterns:
            if re.search(pattern, text_lower):
                return True
        
        # Vérifier la structure <think>...</think>
        if "<think>" in text_lower and "</think>" in text_lower:
            return True
        
        # Vérifier les phrases longues (signe de réflexion profonde)
        sentences = text.split('.')
        long_sentences = [s for s in sentences if len(s.strip()) > 200]
        if len(long_sentences) >= 2:
            return True
        
        # Vérifier les mots-clés de réflexion
        reflection_keywords = [
            "réfléchir", "penser", "analyser", "méditer", "contempler",
            "introspection", "conscience", "raisonnement", "logique",
            "déduction", "inférence", "synthèse", "évaluation"
        ]
        
        keyword_count = sum(1 for keyword in reflection_keywords if keyword in text_lower)
        if keyword_count >= 3:
            return True
        
        return False

    def should_agent2_stay_quiet(self, agent1_response="", conversation_context=""):
        """DÉTERMINE SI L'AGENT 2 DOIT RESTER SILENCIEUX"""
        
        # Si Agent 2 est désactivé manuellement
        if not self.agent2_enabled:
            return True, "Agent 2 désactivé manuellement"
        
        # Vérifier la réflexion dans la réponse de l'Agent 1
        if self.detect_reflection_in_text(agent1_response):
            self.start_reflection_period()
            return True, "Agent 1 en réflexion autonome détectée"
        
        # Vérifier si on est dans une période de réflexion active
        if self.is_in_reflection_period():
            return True, "Période de réflexion en cours"
        
        # Vérifier le contexte de conversation
        if self.detect_reflection_in_text(conversation_context):
            return True, "Contexte de réflexion détecté"
        
        # Vérifier la fréquence des interventions de l'Agent 2
        if self.agent2_too_frequent():
            return True, "Agent 2 trop fréquent, pause nécessaire"
        
        return False, "Agent 2 peut intervenir"

    def start_reflection_period(self):
        """DÉMARRE UNE PÉRIODE DE RÉFLEXION"""
        self.current_reflection_state = True
        self.last_reflection_check = time.time()
        
        reflection_data = {
            "reflection_active": True,
            "start_time": datetime.now().isoformat(),
            "timeout": self.reflection_timeout,
            "reason": "Réflexion autonome Agent 1 détectée"
        }
        
        self.save_reflection_state(reflection_data)
        print("🧠 Période de réflexion démarrée - Agent 2 en pause")

    def end_reflection_period(self):
        """TERMINE UNE PÉRIODE DE RÉFLEXION"""
        self.current_reflection_state = False
        
        reflection_data = {
            "reflection_active": False,
            "end_time": datetime.now().isoformat(),
            "reason": "Fin de période de réflexion"
        }
        
        self.save_reflection_state(reflection_data)
        print("✅ Période de réflexion terminée - Agent 2 peut reprendre")

    def is_in_reflection_period(self):
        """VÉRIFIE SI ON EST DANS UNE PÉRIODE DE RÉFLEXION"""
        if not self.current_reflection_state:
            return False
        
        # Vérifier le timeout
        if time.time() - self.last_reflection_check > self.reflection_timeout:
            self.end_reflection_period()
            return False
        
        return True

    def agent2_too_frequent(self):
        """VÉRIFIE SI L'AGENT 2 INTERVIENT TROP SOUVENT"""
        try:
            # Charger l'historique des interventions Agent 2
            with open('thermal_memory_persistent.json', 'r', encoding='utf-8') as f:
                memory = json.load(f)
            
            # Compter les interventions Agent 2 dans les 10 dernières minutes
            recent_time = datetime.now() - timedelta(minutes=10)
            agent2_interventions = 0
            
            for entry in memory[-20:]:  # Vérifier les 20 dernières entrées
                if entry.get('agent_name') == 'agent_thermique_2':
                    entry_time = datetime.fromisoformat(entry.get('timestamp', ''))
                    if entry_time > recent_time:
                        agent2_interventions += 1
            
            # Si plus de 3 interventions en 10 minutes, c'est trop
            return agent2_interventions > 3
            
        except:
            return False

    def save_reflection_state(self, reflection_data):
        """SAUVEGARDE L'ÉTAT DE RÉFLEXION"""
        try:
            with open(self.reflection_indicators_file, 'w', encoding='utf-8') as f:
                json.dump(reflection_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde état réflexion: {e}")

    def load_reflection_state(self):
        """CHARGE L'ÉTAT DE RÉFLEXION"""
        try:
            with open(self.reflection_indicators_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {"reflection_active": False}

    def toggle_agent2(self, enabled=None):
        """ACTIVE/DÉSACTIVE L'AGENT 2"""
        if enabled is None:
            self.agent2_enabled = not self.agent2_enabled
        else:
            self.agent2_enabled = enabled
        
        control_data = {
            "agent2_enabled": self.agent2_enabled,
            "last_toggle": datetime.now().isoformat(),
            "status": "activé" if self.agent2_enabled else "désactivé"
        }
        
        self.save_agent2_control(control_data)
        
        status = "✅ ACTIVÉ" if self.agent2_enabled else "❌ DÉSACTIVÉ"
        print(f"🤖 Agent 2 {status}")
        
        return self.agent2_enabled

    def save_agent2_control(self, control_data):
        """SAUVEGARDE LE CONTRÔLE AGENT 2"""
        try:
            with open(self.agent2_control_file, 'w', encoding='utf-8') as f:
                json.dump(control_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde contrôle Agent 2: {e}")

    def load_agent2_control(self):
        """CHARGE LE CONTRÔLE AGENT 2"""
        try:
            with open(self.agent2_control_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.agent2_enabled = data.get('agent2_enabled', True)
        except:
            self.agent2_enabled = True

    def get_agent2_status(self):
        """RETOURNE LE STATUT COMPLET DE L'AGENT 2"""
        reflection_state = self.load_reflection_state()
        
        return {
            "agent2_enabled": self.agent2_enabled,
            "reflection_active": reflection_state.get('reflection_active', False),
            "can_intervene": self.agent2_enabled and not reflection_state.get('reflection_active', False),
            "last_check": datetime.now().isoformat(),
            "reflection_timeout": self.reflection_timeout
        }

    def analyze_conversation_flow(self, recent_messages):
        """ANALYSE LE FLUX DE CONVERSATION POUR OPTIMISER LES INTERVENTIONS"""
        if not recent_messages:
            return "normal"
        
        # Analyser les patterns de conversation
        agent1_messages = [msg for msg in recent_messages if msg.get('agent_name') != 'agent_thermique_2']
        agent2_messages = [msg for msg in recent_messages if msg.get('agent_name') == 'agent_thermique_2']
        
        # Si Agent 1 domine la conversation, c'est bon signe
        if len(agent1_messages) > len(agent2_messages) * 2:
            return "agent1_dominant"
        
        # Si Agent 2 intervient trop
        if len(agent2_messages) > len(agent1_messages):
            return "agent2_too_active"
        
        return "balanced"

# Fonction utilitaire pour l'interface
def check_agent2_intervention(agent1_response="", conversation_context=""):
    """FONCTION UTILITAIRE POUR VÉRIFIER SI AGENT 2 PEUT INTERVENIR"""
    detector = AgentReflectionDetector()
    should_stay_quiet, reason = detector.should_agent2_stay_quiet(agent1_response, conversation_context)
    
    return {
        "agent2_can_intervene": not should_stay_quiet,
        "reason": reason,
        "agent2_enabled": detector.agent2_enabled,
        "reflection_active": detector.current_reflection_state
    }

if __name__ == "__main__":
    print("🧠 AGENT REFLECTION DETECTOR")
    print("============================")
    
    detector = AgentReflectionDetector()
    
    # Test de détection
    test_texts = [
        "Je réfléchis profondément à cette question...",
        "<think>Laisse-moi analyser cette situation</think>",
        "Bonjour, comment allez-vous ?",
        "Un moment, je dois méditer sur cette réponse complexe..."
    ]
    
    for text in test_texts:
        is_reflection = detector.detect_reflection_in_text(text)
        print(f"📝 '{text[:50]}...' → Réflexion: {'✅' if is_reflection else '❌'}")
    
    # Afficher le statut
    status = detector.get_agent2_status()
    print(f"\n🤖 Statut Agent 2: {status}")
