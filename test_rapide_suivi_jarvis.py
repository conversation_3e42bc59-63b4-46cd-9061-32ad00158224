#!/usr/bin/env python3
"""
🧪 TEST RAPIDE SYSTÈME DE SUIVI JARVIS
Validation rapide de toutes les fonctionnalités
Créé pour Jean-Luc Passave
"""

import json
import os
import requests
import subprocess
from datetime import datetime

def print_test_header(test_name):
    """Afficher l'en-tête d'un test"""
    print(f"\n🧪 ================================")
    print(f"🤖 TEST: {test_name}")
    print(f"🧪 ================================")

def print_result(test_name, success, details=""):
    """Afficher le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   {details}")

def main():
    """Test rapide complet"""
    print("🧪 ================================")
    print("🤖 TEST RAPIDE SYSTÈME SUIVI JARVIS")
    print("🧪 ================================")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    resultats = {}
    
    # Test 1: Fichier de suivi
    print_test_header("FICHIER DE SUIVI")
    try:
        fichier_existe = os.path.exists("jarvis_feuille_suivi_24h.json")
        print_result("Fichier existe", fichier_existe)
        
        if fichier_existe:
            with open("jarvis_feuille_suivi_24h.json", 'r') as f:
                data = json.load(f)
            
            nb_activites = len(data.get("activites_temps_reel", []))
            print_result("Données JSON valides", True, f"{nb_activites} activités")
            
            # Afficher les dernières activités
            activites_recentes = data.get("activites_temps_reel", [])[-3:]
            print("   📋 3 dernières activités:")
            for act in activites_recentes:
                timestamp = act["timestamp"].split("T")[1][:8]
                type_act = act["type"]
                print(f"     {timestamp} - {type_act}")
            
            resultats["Fichier de suivi"] = True
        else:
            resultats["Fichier de suivi"] = False
    except Exception as e:
        print_result("Erreur fichier", False, str(e))
        resultats["Fichier de suivi"] = False
    
    # Test 2: Interface JARVIS
    print_test_header("INTERFACE JARVIS")
    try:
        response = requests.get("http://127.0.0.1:7867", timeout=5)
        accessible = response.status_code == 200
        print_result("Interface accessible", accessible, f"Code: {response.status_code}")
        resultats["Interface JARVIS"] = accessible
    except Exception as e:
        print_result("Connexion échouée", False, str(e))
        resultats["Interface JARVIS"] = False
    
    # Test 3: Processus
    print_test_header("PROCESSUS SYSTÈME")
    try:
        result = subprocess.run(["pgrep", "-f", "jarvis_interface_propre.py"], 
                              capture_output=True, text=True)
        jarvis_actif = result.returncode == 0
        if jarvis_actif:
            pids = result.stdout.strip().split('\n')
            print_result("JARVIS actif", True, f"PID: {', '.join(pids)}")
        else:
            print_result("JARVIS actif", False)
        resultats["Processus JARVIS"] = jarvis_actif
    except Exception as e:
        print_result("Erreur processus", False, str(e))
        resultats["Processus JARVIS"] = False
    
    # Test 4: Visualiseur
    print_test_header("VISUALISEUR SUIVI")
    try:
        result = subprocess.run([
            "python", "jarvis_visualiseur_suivi.py", "--resume"
        ], capture_output=True, text=True, timeout=10)
        
        visualiseur_ok = result.returncode == 0 and "RÉSUMÉ GLOBAL JARVIS" in result.stdout
        print_result("Visualiseur fonctionne", visualiseur_ok)
        
        if visualiseur_ok:
            # Extraire quelques infos du résumé
            lines = result.stdout.split('\n')
            for line in lines:
                if "Dernière MAJ:" in line:
                    print(f"   {line.strip()}")
                elif "Mode sommeil:" in line:
                    print(f"   {line.strip()}")
        
        resultats["Visualiseur"] = visualiseur_ok
    except Exception as e:
        print_result("Erreur visualiseur", False, str(e))
        resultats["Visualiseur"] = False
    
    # Test 5: Fichiers système
    print_test_header("FICHIERS SYSTÈME")
    fichiers_requis = [
        "jarvis_feuille_suivi_complet.py",
        "jarvis_visualiseur_suivi.py",
        "demarrer_suivi_jarvis.sh"
    ]
    
    tous_fichiers_ok = True
    for fichier in fichiers_requis:
        existe = os.path.exists(fichier)
        print_result(f"Fichier {fichier}", existe)
        if not existe:
            tous_fichiers_ok = False
    
    resultats["Fichiers système"] = tous_fichiers_ok
    
    # Test 6: Données de suivi détaillées
    print_test_header("DONNÉES DE SUIVI")
    try:
        with open("jarvis_feuille_suivi_24h.json", 'r') as f:
            data = json.load(f)
        
        # Vérifier la structure complète
        structure_complete = all(key in data for key in [
            "date_creation", "derniere_mise_a_jour", "mode_sommeil_actif",
            "statistiques_globales", "suivi_par_jour", "activites_temps_reel"
        ])
        print_result("Structure complète", structure_complete)
        
        # Vérifier les statistiques
        stats = data.get("statistiques_globales", {})
        print_result("Statistiques présentes", len(stats) > 0, 
                    f"Réveils: {stats.get('nombre_reveils', 0)}, Endormissements: {stats.get('nombre_endormissements', 0)}")
        
        # Vérifier le suivi par jour
        suivi_jour = data.get("suivi_par_jour", {})
        date_aujourd_hui = datetime.now().strftime("%Y-%m-%d")
        jour_present = date_aujourd_hui in suivi_jour
        print_result("Suivi aujourd'hui", jour_present)
        
        if jour_present:
            resume_jour = suivi_jour[date_aujourd_hui].get("resume_journee", {})
            nb_activites_jour = resume_jour.get("nombre_activites", 0)
            print_result("Activités du jour", nb_activites_jour > 0, f"{nb_activites_jour} activités")
        
        resultats["Données de suivi"] = structure_complete and jour_present
        
    except Exception as e:
        print_result("Erreur données", False, str(e))
        resultats["Données de suivi"] = False
    
    # Rapport final
    print(f"\n🏁 ================================")
    print(f"📊 RAPPORT FINAL TEST RAPIDE")
    print(f"🏁 ================================")
    
    total_tests = len(resultats)
    tests_reussis = sum(resultats.values())
    pourcentage = (tests_reussis / total_tests) * 100
    
    print(f"📊 Tests réussis: {tests_reussis}/{total_tests} ({pourcentage:.1f}%)")
    
    if pourcentage >= 80:
        print(f"🎉 SYSTÈME PARFAITEMENT FONCTIONNEL !")
        print(f"✅ Le système de suivi JARVIS 24h/24 est opérationnel")
        print(f"📊 Surveillance active, données en temps réel")
        print(f"🔍 Interface accessible, visualiseur fonctionnel")
        print(f"😴 Mode sommeil prêt à l'emploi")
    elif pourcentage >= 60:
        print(f"⚠️ SYSTÈME PARTIELLEMENT FONCTIONNEL")
        print(f"Quelques ajustements peuvent être nécessaires")
    else:
        print(f"❌ PROBLÈMES DÉTECTÉS")
        print(f"Le système nécessite des corrections")
    
    print(f"\n📋 DÉTAIL DES TESTS:")
    for test_name, success in resultats.items():
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n💡 UTILISATION:")
    print(f"  • Interface web: http://127.0.0.1:7867")
    print(f"  • Boutons suivi: 📊 Résumé, 📅 Aujourd'hui, ⏰ Récentes, 😴 Sommeil")
    print(f"  • Ligne de commande: python jarvis_visualiseur_suivi.py --interactif")
    print(f"  • Feuille de suivi: jarvis_feuille_suivi_24h.json")
    
    print(f"\n" + "="*50)

if __name__ == "__main__":
    main()
