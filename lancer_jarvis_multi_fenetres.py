#!/usr/bin/env python3
"""
🚀 LANCEUR JARVIS MULTI-FENÊTRES
Script de lancement simplifié pour <PERSON>-Luc
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.append('.')

try:
    from jarvis_architecture_multi_fenetres import launch_all_windows
    
    print("🤖 DÉMARRAGE JARVIS MULTI-FENÊTRES")
    print("=" * 50)
    print("🏠 Interface organisée et professionnelle")
    print("🔧 Chaque fonction dans sa propre fenêtre")
    print("✨ Fini le désordre sur une seule page !")
    print("=" * 50)
    
    launch_all_windows()
    
except ImportError as e:
    print(f"❌ Erreur d'importation: {e}")
    print("Vérifiez que jarvis_architecture_multi_fenetres.py existe")
except Exception as e:
    print(f"❌ Erreur: {e}")
