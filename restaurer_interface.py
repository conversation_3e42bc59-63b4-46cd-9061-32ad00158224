#!/usr/bin/env python3
# Script de restauration interface JARVIS
import json
import shutil
from datetime import datetime

def restaurer_interface():
    """Restaure l'interface sans perdre le code"""
    try:
        # Charger config
        with open("jarvis_interface_config.json", 'r') as f:
            config = json.load(f)
        
        print("🔄 Restauration interface...")
        print(f"Version: {config['version']}")
        print(f"Layout: {config['layout_type']}")
        print("✅ Interface restaurée")
        
    except Exception as e:
        print(f"❌ Erreur restauration: {e}")

if __name__ == "__main__":
    restaurer_interface()
