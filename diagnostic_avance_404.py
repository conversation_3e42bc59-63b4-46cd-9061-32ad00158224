#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DIAGNOSTIC AVANCÉ ERREUR 404 - JEAN-LUC PASSAVE
Diagnostic approfondi pour comprendre l'erreur 404 persistante
"""

import requests
import subprocess
import time
import os
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔍 [{timestamp}] {message}")

def test_tous_ports():
    """Teste tous les ports possibles"""
    log("🔍 TEST COMPLET TOUS PORTS")
    print("=" * 40)
    
    ports_a_tester = range(7860, 7870)
    ports_actifs = []
    
    for port in ports_a_tester:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=2)
            
            status = f"Port {port}: "
            if response.status_code == 200:
                if "JARVIS" in response.text:
                    status += "✅ JARVIS ACTIF"
                    ports_actifs.append((port, "JARVIS"))
                else:
                    status += "⚠️  AUTRE APPLICATION"
                    ports_actifs.append((port, "AUTRE"))
            elif response.status_code == 404:
                status += "🚨 ERREUR 404"
                ports_actifs.append((port, "404"))
            else:
                status += f"❌ ERREUR {response.status_code}"
                ports_actifs.append((port, f"ERREUR_{response.status_code}"))
            
            print(status)
            
        except requests.exceptions.ConnectionError:
            print(f"Port {port}: 🔌 FERMÉ")
        except Exception as e:
            print(f"Port {port}: ❌ ERREUR {e}")
    
    return ports_actifs

def verifier_processus_jarvis():
    """Vérifie les processus JARVIS en cours"""
    log("🔍 VÉRIFICATION PROCESSUS JARVIS")
    
    try:
        # Chercher processus Python avec jarvis
        result = subprocess.run([
            "ps", "aux"
        ], capture_output=True, text=True)
        
        processus_jarvis = []
        for ligne in result.stdout.split('\n'):
            if 'jarvis_interface_propre.py' in ligne.lower():
                processus_jarvis.append(ligne.strip())
        
        if processus_jarvis:
            print(f"✅ {len(processus_jarvis)} processus JARVIS trouvé(s):")
            for i, proc in enumerate(processus_jarvis, 1):
                print(f"   {i}. {proc}")
        else:
            print("❌ Aucun processus JARVIS trouvé")
        
        return processus_jarvis
        
    except Exception as e:
        print(f"❌ Erreur vérification processus: {e}")
        return []

def verifier_contenu_page():
    """Vérifie le contenu de la page sur port 7860"""
    log("🔍 VÉRIFICATION CONTENU PAGE")
    
    try:
        response = requests.get("http://localhost:7860", timeout=5)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Non défini')}")
        print(f"Taille contenu: {len(response.text)} caractères")
        
        # Vérifier contenu JARVIS
        if "JARVIS" in response.text:
            print("✅ Contenu JARVIS détecté")
            
            # Chercher éléments spécifiques
            elements = [
                "Conversation avec JARVIS",
                "gradio",
                "chatbot",
                "Interface JARVIS",
                "DeepSeek"
            ]
            
            for element in elements:
                if element in response.text:
                    print(f"   ✅ {element} trouvé")
                else:
                    print(f"   ❌ {element} manquant")
        else:
            print("❌ Contenu JARVIS non détecté")
            
            # Afficher début du contenu
            print("📄 DÉBUT DU CONTENU:")
            print(response.text[:500])
            print("...")
        
        return response.status_code == 200 and "JARVIS" in response.text
        
    except Exception as e:
        print(f"❌ Erreur vérification contenu: {e}")
        return False

def tester_depuis_navigateur():
    """Simule un test depuis navigateur"""
    log("🌐 TEST SIMULATION NAVIGATEUR")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    try:
        response = requests.get("http://localhost:7860", headers=headers, timeout=5)
        
        print(f"Status avec headers navigateur: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Réponse OK avec headers navigateur")
            return True
        elif response.status_code == 404:
            print("🚨 ERREUR 404 avec headers navigateur")
            return False
        else:
            print(f"⚠️  Status {response.status_code} avec headers navigateur")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test navigateur: {e}")
        return False

def diagnostic_complet():
    """Diagnostic complet de l'erreur 404"""
    log("🔍 DIAGNOSTIC COMPLET ERREUR 404")
    print("=" * 50)
    
    # 1. Test tous ports
    ports_actifs = test_tous_ports()
    
    print("\n" + "=" * 30)
    
    # 2. Vérifier processus
    processus = verifier_processus_jarvis()
    
    print("\n" + "=" * 30)
    
    # 3. Vérifier contenu
    contenu_ok = verifier_contenu_page()
    
    print("\n" + "=" * 30)
    
    # 4. Test navigateur
    navigateur_ok = tester_depuis_navigateur()
    
    # 5. Résumé diagnostic
    print("\n" + "=" * 50)
    log("📊 RÉSUMÉ DIAGNOSTIC")
    print("=" * 50)
    
    print(f"🔌 Ports actifs: {len(ports_actifs)}")
    for port, status in ports_actifs:
        print(f"   Port {port}: {status}")
    
    print(f"⚙️  Processus JARVIS: {len(processus)}")
    print(f"📄 Contenu JARVIS: {'✅ OK' if contenu_ok else '❌ KO'}")
    print(f"🌐 Test navigateur: {'✅ OK' if navigateur_ok else '❌ KO'}")
    
    # 6. Recommandations
    print("\n💡 RECOMMANDATIONS:")
    
    if not ports_actifs:
        print("   🚨 AUCUN PORT ACTIF - Redémarrer JARVIS")
    elif not any(status == "JARVIS" for port, status in ports_actifs):
        print("   🚨 JARVIS NON DÉTECTÉ - Vérifier processus")
    elif not contenu_ok:
        print("   🚨 CONTENU CORROMPU - Redémarrer JARVIS")
    elif not navigateur_ok:
        print("   🚨 PROBLÈME NAVIGATEUR - Vider cache/cookies")
    else:
        print("   ✅ SYSTÈME SEMBLE OK - Problème peut-être côté navigateur")
    
    return {
        'ports_actifs': ports_actifs,
        'processus': len(processus),
        'contenu_ok': contenu_ok,
        'navigateur_ok': navigateur_ok
    }

def corriger_selon_diagnostic():
    """Corrige selon le diagnostic"""
    log("🔧 CORRECTION SELON DIAGNOSTIC")
    
    diagnostic = diagnostic_complet()
    
    if not diagnostic['ports_actifs']:
        log("🚀 Redémarrage JARVIS (aucun port actif)")
        subprocess.run(["python3", "super_correcteur_automatique_jarvis.py"])
    
    elif not diagnostic['contenu_ok']:
        log("🔄 Redémarrage JARVIS (contenu corrompu)")
        subprocess.run(["pkill", "-f", "jarvis_interface_propre.py"])
        time.sleep(2)
        subprocess.Popen(["python3", "jarvis_interface_propre.py"])
    
    else:
        log("✅ Diagnostic OK - Problème peut-être côté navigateur")
        print("💡 Essayez:")
        print("   1. Vider le cache du navigateur")
        print("   2. Ouvrir en navigation privée")
        print("   3. Essayer un autre navigateur")

if __name__ == "__main__":
    print("🔍 DIAGNOSTIC AVANCÉ ERREUR 404")
    print("1. Diagnostic complet")
    print("2. Test tous ports seulement")
    print("3. Vérifier contenu page seulement")
    print("4. Correction automatique")
    
    choix = input("\nVotre choix (1-4): ").strip()
    
    if choix == "1":
        diagnostic_complet()
    elif choix == "2":
        test_tous_ports()
    elif choix == "3":
        verifier_contenu_page()
    elif choix == "4":
        corriger_selon_diagnostic()
    else:
        print("❌ Choix invalide - diagnostic complet")
        diagnostic_complet()
