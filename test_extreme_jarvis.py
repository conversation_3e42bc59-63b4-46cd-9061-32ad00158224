#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST EXTRÊME JARVIS - JEAN-LUC PASSAVE
Test poussé au maximum de toutes les capacités de JARVIS
"""

import requests
import json
import time
import threading
import random
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🧪 [{timestamp}] {message}")

class TestExtremeJarvis:
    def __init__(self):
        self.base_url = "http://localhost:8000/v1/chat/completions"
        self.resultats = {}
        self.tests_reussis = 0
        self.tests_totaux = 0
        
    def test_connexion_base(self):
        """Test 1: Connexion de base"""
        log("TEST 1: Connexion de base")
        try:
            response = requests.get("http://localhost:7866", timeout=5)
            if response.status_code == 200:
                self.tests_reussis += 1
                log("✅ Interface accessible")
                return True
            else:
                log(f"❌ Interface inaccessible: {response.status_code}")
                return False
        except Exception as e:
            log(f"❌ Erreur connexion: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_deepseek_direct(self):
        """Test 2: Communication directe avec DeepSeek"""
        log("TEST 2: Communication directe DeepSeek")
        try:
            payload = {
                "model": "deepseek-ai/DeepSeek-R1-0528",
                "messages": [
                    {"role": "user", "content": "Test de connexion JARVIS. Réponds juste 'CONNEXION OK'"}
                ],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            response = requests.post(self.base_url, json=payload, timeout=10)
            if response.status_code == 200:
                data = response.json()
                content = data["choices"][0]["message"]["content"]
                if "CONNEXION OK" in content or "connexion" in content.lower():
                    self.tests_reussis += 1
                    log("✅ DeepSeek répond correctement")
                    return True
                else:
                    log(f"❌ Réponse inattendue: {content}")
                    return False
            else:
                log(f"❌ Erreur DeepSeek: {response.status_code}")
                return False
        except Exception as e:
            log(f"❌ Erreur communication: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_memoire_thermique_stress(self):
        """Test 3: Stress test mémoire thermique"""
        log("TEST 3: Stress test mémoire thermique")
        try:
            # Envoyer 10 messages rapidement
            messages_test = [
                "Calcule 2+2",
                "Quelle est la capitale de la France ?",
                "Explique la mémoire thermique",
                "Combien de neurones as-tu ?",
                "Quel est ton QI ?",
                "Parle-moi de gestion BTP",
                "Calcule les charges URSSAF pour 3000€",
                "Que penses-tu de Jean-Luc ?",
                "Raconte-moi une blague",
                "Résume cette conversation"
            ]
            
            reponses_recues = 0
            for i, message in enumerate(messages_test):
                payload = {
                    "model": "deepseek-ai/DeepSeek-R1-0528",
                    "messages": [
                        {"role": "user", "content": message}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 200
                }
                
                try:
                    response = requests.post(self.base_url, json=payload, timeout=15)
                    if response.status_code == 200:
                        reponses_recues += 1
                        log(f"✅ Message {i+1}/10 traité")
                    else:
                        log(f"❌ Échec message {i+1}: {response.status_code}")
                    
                    time.sleep(0.5)  # Petit délai entre messages
                    
                except Exception as e:
                    log(f"❌ Erreur message {i+1}: {e}")
            
            if reponses_recues >= 8:  # 80% de réussite
                self.tests_reussis += 1
                log(f"✅ Stress test réussi: {reponses_recues}/10 messages")
                return True
            else:
                log(f"❌ Stress test échoué: {reponses_recues}/10 messages")
                return False
                
        except Exception as e:
            log(f"❌ Erreur stress test: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_capacites_avancees(self):
        """Test 4: Capacités avancées (BTP, URSSAF, etc.)"""
        log("TEST 4: Capacités avancées")
        try:
            questions_avancees = [
                {
                    "question": "Calcule les charges sociales pour un salaire de 2500€ en BTP",
                    "mots_cles": ["charges", "sociales", "2500", "urssaf", "btp"]
                },
                {
                    "question": "Explique la gestion de chantier avec planning Gantt",
                    "mots_cles": ["gantt", "planning", "chantier", "gestion"]
                },
                {
                    "question": "Combien de neurones as-tu exactement ?",
                    "mots_cles": ["89", "milliards", "neurones"]
                },
                {
                    "question": "Quel est ton coefficient intellectuel ?",
                    "mots_cles": ["qi", "coefficient", "intellectuel"]
                }
            ]
            
            tests_avances_reussis = 0
            for i, test in enumerate(questions_avancees):
                payload = {
                    "model": "deepseek-ai/DeepSeek-R1-0528",
                    "messages": [
                        {"role": "user", "content": test["question"]}
                    ],
                    "temperature": 0.3,
                    "max_tokens": 500
                }
                
                try:
                    response = requests.post(self.base_url, json=payload, timeout=20)
                    if response.status_code == 200:
                        data = response.json()
                        content = data["choices"][0]["message"]["content"].lower()
                        
                        # Vérifier si la réponse contient les mots-clés attendus
                        mots_trouves = sum(1 for mot in test["mots_cles"] if mot in content)
                        if mots_trouves >= len(test["mots_cles"]) * 0.6:  # 60% des mots-clés
                            tests_avances_reussis += 1
                            log(f"✅ Test avancé {i+1}: {mots_trouves}/{len(test['mots_cles'])} mots-clés")
                        else:
                            log(f"❌ Test avancé {i+1}: {mots_trouves}/{len(test['mots_cles'])} mots-clés")
                    else:
                        log(f"❌ Erreur test avancé {i+1}: {response.status_code}")
                        
                except Exception as e:
                    log(f"❌ Erreur test avancé {i+1}: {e}")
                
                time.sleep(1)
            
            if tests_avances_reussis >= 3:  # 75% de réussite
                self.tests_reussis += 1
                log(f"✅ Capacités avancées: {tests_avances_reussis}/4 tests")
                return True
            else:
                log(f"❌ Capacités avancées: {tests_avances_reussis}/4 tests")
                return False
                
        except Exception as e:
            log(f"❌ Erreur capacités avancées: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_performance_extreme(self):
        """Test 5: Performance extrême - messages longs et complexes"""
        log("TEST 5: Performance extrême")
        try:
            message_complexe = """
            JARVIS, je veux que tu analyses cette situation complexe d'entreprise BTP :
            
            Entreprise: SARL BATIMENT PASSAVE
            - CA annuel: 1,200,000€
            - 8 salariés
            - 12 chantiers en cours
            - Trésorerie: 45,000€
            - Retards sur 3 chantiers
            - Factures impayées: 180,000€
            
            Questions:
            1. Calcule TOUS les ratios financiers
            2. Analyse la situation de trésorerie
            3. Propose 5 solutions concrètes
            4. Calcule les charges sociales totales
            5. Évalue les risques
            6. Donne un plan d'action sur 6 mois
            
            Réponds de manière structurée et complète avec tous les calculs.
            """
            
            payload = {
                "model": "deepseek-ai/DeepSeek-R1-0528",
                "messages": [
                    {"role": "user", "content": message_complexe}
                ],
                "temperature": 0.5,
                "max_tokens": 2000
            }
            
            start_time = time.time()
            response = requests.post(self.base_url, json=payload, timeout=60)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                content = data["choices"][0]["message"]["content"]
                
                # Vérifier la qualité de la réponse
                criteres_qualite = [
                    "ratio" in content.lower(),
                    "trésorerie" in content.lower() or "tresorerie" in content.lower(),
                    "charges" in content.lower(),
                    "solution" in content.lower(),
                    len(content) > 1000,  # Réponse substantielle
                    "1." in content or "2." in content,  # Structure numérotée
                ]
                
                score_qualite = sum(criteres_qualite)
                temps_reponse = end_time - start_time
                
                if score_qualite >= 4 and temps_reponse < 45:
                    self.tests_reussis += 1
                    log(f"✅ Performance extrême: {score_qualite}/6 critères, {temps_reponse:.1f}s")
                    return True
                else:
                    log(f"❌ Performance extrême: {score_qualite}/6 critères, {temps_reponse:.1f}s")
                    return False
            else:
                log(f"❌ Erreur performance: {response.status_code}")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test performance: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_concurrence_extreme(self):
        """Test 6: Test de concurrence extrême - 5 requêtes simultanées"""
        log("TEST 6: Test de concurrence extrême")
        try:
            def requete_simultanee(numero):
                payload = {
                    "model": "deepseek-ai/DeepSeek-R1-0528",
                    "messages": [
                        {"role": "user", "content": f"Test concurrent {numero}: Calcule {numero} x {numero} et explique brièvement"}
                    ],
                    "temperature": 0.3,
                    "max_tokens": 100
                }
                
                try:
                    response = requests.post(self.base_url, json=payload, timeout=30)
                    return response.status_code == 200
                except:
                    return False
            
            # Lancer 5 requêtes simultanées
            threads = []
            resultats = []
            
            for i in range(5):
                thread = threading.Thread(
                    target=lambda n=i: resultats.append(requete_simultanee(n+1))
                )
                threads.append(thread)
                thread.start()
            
            # Attendre toutes les réponses
            for thread in threads:
                thread.join()
            
            reussites = sum(resultats)
            if reussites >= 4:  # 80% de réussite
                self.tests_reussis += 1
                log(f"✅ Concurrence extrême: {reussites}/5 requêtes")
                return True
            else:
                log(f"❌ Concurrence extrême: {reussites}/5 requêtes")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test concurrence: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def executer_tous_les_tests(self):
        """Exécute tous les tests extrêmes"""
        log("🚀 DÉBUT TEST EXTRÊME JARVIS")
        print("=" * 60)
        
        # Liste des tests
        tests = [
            ("Connexion de base", self.test_connexion_base),
            ("Communication DeepSeek", self.test_deepseek_direct),
            ("Stress mémoire thermique", self.test_memoire_thermique_stress),
            ("Capacités avancées", self.test_capacites_avancees),
            ("Performance extrême", self.test_performance_extreme),
            ("Concurrence extrême", self.test_concurrence_extreme)
        ]
        
        # Exécuter chaque test
        for nom_test, fonction_test in tests:
            log(f"🧪 EXÉCUTION: {nom_test}")
            try:
                resultat = fonction_test()
                self.resultats[nom_test] = resultat
                if resultat:
                    log(f"✅ RÉUSSI: {nom_test}")
                else:
                    log(f"❌ ÉCHOUÉ: {nom_test}")
            except Exception as e:
                log(f"💥 ERREUR: {nom_test} - {e}")
                self.resultats[nom_test] = False
            
            print("-" * 40)
            time.sleep(2)  # Pause entre tests
        
        # Résultats finaux
        self.afficher_resultats_finaux()
    
    def afficher_resultats_finaux(self):
        """Affiche les résultats finaux du test extrême"""
        print("\n" + "=" * 60)
        log("📊 RÉSULTATS FINAUX TEST EXTRÊME")
        print("=" * 60)
        
        pourcentage = (self.tests_reussis / self.tests_totaux) * 100 if self.tests_totaux > 0 else 0
        
        print(f"🎯 SCORE GLOBAL: {self.tests_reussis}/{self.tests_totaux} ({pourcentage:.1f}%)")
        print()
        
        # Détail par test
        for nom_test, resultat in self.resultats.items():
            statut = "✅ RÉUSSI" if resultat else "❌ ÉCHOUÉ"
            print(f"  {statut}: {nom_test}")
        
        print()
        
        # Évaluation finale
        if pourcentage >= 90:
            print("🏆 JARVIS EST EXCEPTIONNEL ! Performance maximale !")
        elif pourcentage >= 75:
            print("🥇 JARVIS EST EXCELLENT ! Très bonnes performances !")
        elif pourcentage >= 60:
            print("🥈 JARVIS EST BON ! Performances correctes !")
        elif pourcentage >= 40:
            print("🥉 JARVIS EST MOYEN ! Améliorations nécessaires !")
        else:
            print("💥 JARVIS A DES PROBLÈMES ! Intervention urgente !")
        
        print("=" * 60)

def main():
    """Fonction principale"""
    print("🧪 TEST EXTRÊME JARVIS - JEAN-LUC PASSAVE")
    print("Test poussé au maximum de toutes les capacités")
    print("=" * 50)
    
    # Créer et lancer le test
    testeur = TestExtremeJarvis()
    testeur.executer_tous_les_tests()

if __name__ == "__main__":
    main()
