#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 NETTOYEUR COMPLET JARVIS - JEAN-LUC PASSAVE
Nettoie tous les processus JARVIS en double et redémarre proprement
"""

import subprocess
import time
import requests
import os
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🧹 [{timestamp}] {message}")

def arreter_tous_processus_jarvis():
    """Arrête TOUS les processus JARVIS"""
    log("🛑 ARRÊT COMPLET TOUS PROCESSUS JARVIS")
    print("=" * 50)
    
    processus_arretes = 0
    
    try:
        # 1. Arrêt par nom de fichier
        log("🔫 Arrêt par nom de fichier...")
        result = subprocess.run([
            "pkill", "-f", "jarvis_interface_propre.py"
        ], capture_output=True)
        
        time.sleep(2)
        
        # 2. Arrêt par ports
        log("🔫 Arrêt par ports...")
        for port in range(7860, 7880):
            try:
                result = subprocess.run([
                    'lsof', '-ti', f':{port}'
                ], capture_output=True, text=True)
                
                if result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    for pid in pids:
                        if pid:
                            subprocess.run(['kill', '-9', pid], capture_output=True)
                            processus_arretes += 1
                            log(f"🔫 PID {pid} arrêté (port {port})")
            except:
                pass
        
        # 3. Arrêt forcé Python JARVIS
        log("🔫 Arrêt forcé processus Python...")
        try:
            result = subprocess.run([
                "ps", "aux"
            ], capture_output=True, text=True)
            
            for ligne in result.stdout.split('\n'):
                if 'jarvis_interface_propre.py' in ligne.lower():
                    parts = ligne.split()
                    if len(parts) > 1:
                        pid = parts[1]
                        try:
                            subprocess.run(['kill', '-9', pid], capture_output=True)
                            processus_arretes += 1
                            log(f"🔫 PID {pid} forcé")
                        except:
                            pass
        except:
            pass
        
        # 4. Attendre nettoyage
        log("⏳ Attente nettoyage complet...")
        time.sleep(5)
        
        log(f"✅ {processus_arretes} processus arrêtés")
        return True
        
    except Exception as e:
        log(f"❌ Erreur arrêt processus: {e}")
        return False

def verifier_nettoyage():
    """Vérifie que le nettoyage est complet"""
    log("🔍 VÉRIFICATION NETTOYAGE")
    
    # Vérifier processus restants
    try:
        result = subprocess.run([
            "ps", "aux"
        ], capture_output=True, text=True)
        
        processus_restants = []
        for ligne in result.stdout.split('\n'):
            if 'jarvis_interface_propre.py' in ligne.lower():
                processus_restants.append(ligne.strip())
        
        if processus_restants:
            log(f"⚠️  {len(processus_restants)} processus restants")
            return False
        else:
            log("✅ Aucun processus JARVIS restant")
    except:
        pass
    
    # Vérifier ports libres
    ports_occupes = []
    for port in range(7860, 7870):
        try:
            response = requests.get(f"http://localhost:{port}", timeout=1)
            ports_occupes.append(port)
        except:
            pass
    
    if ports_occupes:
        log(f"⚠️  Ports encore occupés: {ports_occupes}")
        return False
    else:
        log("✅ Tous les ports libérés")
    
    return True

def redemarrer_jarvis_proprement():
    """Redémarre JARVIS proprement sur un seul port"""
    log("🚀 REDÉMARRAGE PROPRE JARVIS")
    
    try:
        # Vérifier environnement virtuel
        if os.path.exists("venv_deepseek/bin/activate"):
            cmd = "source venv_deepseek/bin/activate && python jarvis_interface_propre.py"
        else:
            cmd = "python3 jarvis_interface_propre.py"
        
        log(f"💻 Commande: {cmd}")
        
        # Lancer UNE SEULE instance
        process = subprocess.Popen(
            cmd,
            shell=True,
            cwd="."
        )
        
        log(f"🚀 JARVIS lancé (PID: {process.pid})")
        
        # Attendre démarrage
        for i in range(30):
            time.sleep(1)
            try:
                response = requests.get("http://localhost:7860", timeout=2)
                if response.status_code == 200 and "JARVIS" in response.text:
                    log("✅ JARVIS opérationnel sur port 7860")
                    return True
            except:
                pass
        
        log("⚠️  JARVIS prend du temps à démarrer")
        return True
        
    except Exception as e:
        log(f"❌ Erreur redémarrage: {e}")
        return False

def nettoyage_complet():
    """Nettoyage complet et redémarrage propre"""
    log("🧹 NETTOYAGE COMPLET JARVIS")
    print("=" * 60)
    
    # 1. Arrêter tous les processus
    log("ÉTAPE 1: Arrêt de tous les processus")
    if not arreter_tous_processus_jarvis():
        log("❌ Échec arrêt processus")
        return False
    
    # 2. Vérifier nettoyage
    log("ÉTAPE 2: Vérification nettoyage")
    tentatives = 0
    while not verifier_nettoyage() and tentatives < 3:
        log(f"⚠️  Nettoyage incomplet - tentative {tentatives + 1}/3")
        arreter_tous_processus_jarvis()
        time.sleep(3)
        tentatives += 1
    
    if not verifier_nettoyage():
        log("⚠️  Nettoyage partiel - continuation")
    
    # 3. Redémarrage propre
    log("ÉTAPE 3: Redémarrage propre")
    if redemarrer_jarvis_proprement():
        log("✅ JARVIS redémarré proprement")
    else:
        log("❌ Échec redémarrage")
        return False
    
    # 4. Vérification finale
    log("ÉTAPE 4: Vérification finale")
    time.sleep(5)
    
    try:
        response = requests.get("http://localhost:7860", timeout=5)
        if response.status_code == 200 and "JARVIS" in response.text:
            log("🎉 NETTOYAGE COMPLET RÉUSSI !")
            log("🌐 JARVIS accessible: http://localhost:7860")
            
            # Vérifier qu'il n'y a qu'un seul processus
            result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
            processus_jarvis = [l for l in result.stdout.split('\n') if 'jarvis_interface_propre.py' in l.lower()]
            log(f"✅ {len(processus_jarvis)} processus JARVIS (optimal: 1-2)")
            
            return True
        else:
            log("❌ JARVIS non accessible après redémarrage")
            return False
    except Exception as e:
        log(f"❌ Erreur vérification finale: {e}")
        return False

def status_systeme():
    """Affiche le statut actuel du système"""
    log("📊 STATUT SYSTÈME ACTUEL")
    print("=" * 40)
    
    # Processus
    try:
        result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
        processus_jarvis = [l for l in result.stdout.split('\n') if 'jarvis_interface_propre.py' in l.lower()]
        print(f"⚙️  Processus JARVIS: {len(processus_jarvis)}")
        
        if len(processus_jarvis) > 5:
            print("🚨 TROP DE PROCESSUS - Nettoyage nécessaire")
        elif len(processus_jarvis) == 0:
            print("❌ AUCUN PROCESSUS - Redémarrage nécessaire")
        else:
            print("✅ Nombre de processus acceptable")
    except:
        print("❌ Impossible de vérifier les processus")
    
    # Ports
    ports_actifs = []
    for port in range(7860, 7870):
        try:
            response = requests.get(f"http://localhost:{port}", timeout=1)
            if response.status_code == 200:
                ports_actifs.append(port)
        except:
            pass
    
    print(f"🔌 Ports actifs: {len(ports_actifs)} {ports_actifs}")
    
    if len(ports_actifs) > 1:
        print("🚨 TROP DE PORTS - Nettoyage nécessaire")
    elif len(ports_actifs) == 0:
        print("❌ AUCUN PORT - Redémarrage nécessaire")
    else:
        print("✅ Un seul port actif (optimal)")

if __name__ == "__main__":
    print("🧹 NETTOYEUR COMPLET JARVIS")
    print("Résout le problème des 68 processus en double")
    print("=" * 50)
    print("1. Nettoyage complet et redémarrage")
    print("2. Arrêt de tous les processus seulement")
    print("3. Statut système actuel")
    print("4. Redémarrage propre seulement")
    
    choix = input("\nVotre choix (1-4): ").strip()
    
    if choix == "1":
        if nettoyage_complet():
            print("\n🎉 SUCCÈS ! JARVIS nettoyé et redémarré")
            print("🌐 Interface: http://localhost:7860")
        else:
            print("\n❌ ÉCHEC du nettoyage complet")
    
    elif choix == "2":
        arreter_tous_processus_jarvis()
        verifier_nettoyage()
    
    elif choix == "3":
        status_systeme()
    
    elif choix == "4":
        redemarrer_jarvis_proprement()
    
    else:
        print("❌ Choix invalide")
        status_systeme()
