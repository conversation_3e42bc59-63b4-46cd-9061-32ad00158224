#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR PORT JARVIS - JEAN-LUC PASSAVE
Corrige le problème de port déjà utilisé
"""

import socket
import re

def trouver_port_libre(port_debut=7860):
    """Trouve un port libre à partir de port_debut"""
    for port in range(port_debut, port_debut + 20):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def corriger_port_jarvis():
    """Corrige le port dans JARVIS"""
    fichier = "jarvis_interface_propre.py"
    
    print("🔧 CORRECTION PORT JARVIS")
    print("=" * 30)
    
    # Trouver un port libre
    port_libre = trouver_port_libre(7860)
    if not port_libre:
        print("❌ Aucun port libre trouvé")
        return False
    
    print(f"✅ Port libre trouvé: {port_libre}")
    
    # Lire le fichier
    with open(fichier, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    # Corriger le port
    patterns = [
        (r'server_port=\d+', f'server_port={port_libre}'),
        (r'localhost:\d+', f'localhost:{port_libre}'),
        (r'http://localhost:\d+', f'http://localhost:{port_libre}')
    ]
    
    corrections = 0
    for pattern, replacement in patterns:
        if re.search(pattern, contenu):
            contenu = re.sub(pattern, replacement, contenu)
            corrections += 1
    
    # Sauvegarder
    with open(fichier, 'w', encoding='utf-8') as f:
        f.write(contenu)
    
    print(f"✅ {corrections} corrections de port appliquées")
    print(f"🌐 Nouveau port: {port_libre}")
    
    return port_libre

def tuer_processus_port():
    """Tue les processus qui utilisent les ports JARVIS"""
    import subprocess
    import os
    
    ports = [7860, 7861, 7862, 7863, 7864, 7865, 7866, 7867]
    
    print("🔫 NETTOYAGE PORTS...")
    
    for port in ports:
        try:
            # Trouver le processus qui utilise le port
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True
            )
            
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        print(f"🔫 Arrêt processus {pid} sur port {port}")
                        os.kill(int(pid), 9)
                        
        except Exception as e:
            pass
    
    print("✅ Nettoyage terminé")

if __name__ == "__main__":
    print("🔧 CORRECTEUR PORT JARVIS")
    print("1. Corriger le port automatiquement")
    print("2. Tuer les processus sur les ports")
    print("3. Les deux")
    
    choix = input("\nVotre choix (1, 2 ou 3): ").strip()
    
    if choix in ["2", "3"]:
        tuer_processus_port()
    
    if choix in ["1", "3"]:
        port = corriger_port_jarvis()
        if port:
            print(f"\n🚀 JARVIS prêt sur port {port}")
            print(f"🌐 URL: http://localhost:{port}")
    
    print("\n✅ Correction terminée")
