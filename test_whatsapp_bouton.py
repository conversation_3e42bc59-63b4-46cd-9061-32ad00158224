#!/usr/bin/env python3
"""
🔧 TESTEUR BOUTON WHATSAPP JARVIS
Test spécifique du bouton WhatsApp pour Jean-Luc
"""

import sys
import os

def test_whatsapp_functions():
    """Teste toutes les fonctions WhatsApp"""
    print("🔧 ================================")
    print("📱 TESTEUR BOUTON WHATSAPP JARVIS")
    print("🔧 ================================")
    
    try:
        # Importer les fonctions WhatsApp
        sys.path.append('.')
        from jarvis_interface_propre import (
            activate_whatsapp_integration,
            start_whatsapp_service,
            get_whatsapp_status,
            test_whatsapp_real,
            whatsapp_interface
        )
        
        print("✅ Fonctions WhatsApp importées avec succès")
        
        # Test 1: Interface WhatsApp
        print("\n🔍 TEST 1: Interface WhatsApp")
        try:
            result = whatsapp_interface()
            print("✅ whatsapp_interface() fonctionne")
            print(f"📊 Longueur résultat: {len(result)} caractères")
        except Exception as e:
            print(f"❌ Erreur whatsapp_interface: {e}")
        
        # Test 2: Activation WhatsApp
        print("\n🔍 TEST 2: Activation WhatsApp")
        try:
            result = activate_whatsapp_integration()
            print("✅ activate_whatsapp_integration() fonctionne")
            print(f"📊 Longueur résultat: {len(result)} caractères")
        except Exception as e:
            print(f"❌ Erreur activate_whatsapp_integration: {e}")
        
        # Test 3: Statut WhatsApp
        print("\n🔍 TEST 3: Statut WhatsApp")
        try:
            result = get_whatsapp_status()
            print("✅ get_whatsapp_status() fonctionne")
            print(f"📊 Longueur résultat: {len(result)} caractères")
        except Exception as e:
            print(f"❌ Erreur get_whatsapp_status: {e}")
        
        # Test 4: Test WhatsApp réel
        print("\n🔍 TEST 4: Test WhatsApp réel")
        try:
            result = test_whatsapp_real()
            print("✅ test_whatsapp_real() fonctionne")
            print(f"📊 Longueur résultat: {len(result)} caractères")
        except Exception as e:
            print(f"❌ Erreur test_whatsapp_real: {e}")
        
        # Test 5: Démarrage service
        print("\n🔍 TEST 5: Démarrage service WhatsApp")
        try:
            result = start_whatsapp_service()
            print("✅ start_whatsapp_service() fonctionne")
            print(f"📊 Longueur résultat: {len(result)} caractères")
        except Exception as e:
            print(f"❌ Erreur start_whatsapp_service: {e}")
        
        print("\n🎉 TOUS LES TESTS WHATSAPP TERMINÉS")
        
    except ImportError as e:
        print(f"❌ Erreur d'importation: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return False
    
    return True

def check_whatsapp_files():
    """Vérifie les fichiers WhatsApp nécessaires"""
    print("\n📁 VÉRIFICATION FICHIERS WHATSAPP:")
    
    files_to_check = [
        'jarvis_whatsapp_api_real.py',
        'jarvis_whatsapp_integration.js',
        'jarvis_whatsapp_config.json',
        'node_modules/whatsapp-web.js',
        'package.json'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MANQUANT")

def check_node_dependencies():
    """Vérifie les dépendances Node.js"""
    print("\n📦 VÉRIFICATION DÉPENDANCES NODE.JS:")
    
    try:
        import subprocess
        
        # Vérifier Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js: {result.stdout.strip()}")
            else:
                print("❌ Node.js non installé")
        except:
            print("❌ Node.js non trouvé")
        
        # Vérifier npm
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ npm: {result.stdout.strip()}")
            else:
                print("❌ npm non installé")
        except:
            print("❌ npm non trouvé")
        
        # Vérifier les packages installés
        if os.path.exists('node_modules'):
            print("✅ node_modules existe")
            
            packages = ['whatsapp-web.js', 'qrcode-terminal', 'axios']
            for package in packages:
                package_path = f'node_modules/{package}'
                if os.path.exists(package_path):
                    print(f"✅ {package}")
                else:
                    print(f"❌ {package} - MANQUANT")
        else:
            print("❌ node_modules n'existe pas")
            
    except Exception as e:
        print(f"❌ Erreur vérification Node.js: {e}")

def main():
    """Fonction principale"""
    print("📱 TESTEUR COMPLET WHATSAPP JARVIS")
    print("=" * 50)
    
    # Vérifier les fichiers
    check_whatsapp_files()
    
    # Vérifier Node.js
    check_node_dependencies()
    
    # Tester les fonctions
    success = test_whatsapp_functions()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 TESTS WHATSAPP RÉUSSIS !")
        print("✅ Toutes les fonctions WhatsApp sont opérationnelles")
    else:
        print("❌ ÉCHEC DES TESTS WHATSAPP")
        print("Vérifiez les erreurs ci-dessus")
    
    print("\n💡 SOLUTIONS POSSIBLES:")
    print("1. Installer Node.js: https://nodejs.org/")
    print("2. Installer les dépendances: npm install whatsapp-web.js qrcode-terminal axios")
    print("3. Créer le fichier jarvis_whatsapp_integration.js")
    print("4. Configurer jarvis_whatsapp_config.json avec vos identifiants Twilio")

if __name__ == "__main__":
    main()
