#!/bin/bash

# 🚨 CORRECTIF URGENCE DEEPSEEK R1 8B
# Résout le bug DISCONTINUITY et les boucles disc10/disc26

echo "🚨 CORRECTIF URGENCE DEEPSEEK R1 8B"
echo "🔧 Résolution bug DISCONTINUITY"
echo "=============================================="

# ARRÊT FORCÉ TOUS PROCESSUS DÉFAILLANTS
echo "🛑 Arrêt forcé processus défaillants..."
pkill -f "llama" 2>/dev/null || true
pkill -f "deepseek" 2>/dev/null || true
pkill -f "vllm" 2>/dev/null || true
pkill -f ":8000" 2>/dev/null || true
sleep 5

# ACCÉLÉRATEURS ANTI-BUG CRITIQUES
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,expandable_segments:False

# CORRECTIFS DISCONTINUITY
export LLAMA_DISABLE_REASONING=1
export LLAMA_NO_THINKING_TAGS=1
export LLAMA_FORCE_SIMPLE_RESPONSE=1
export LLAMA_MAX_REASONING_TOKENS=0
export LLAMA_SKIP_REFLECTION=1

# ACCÉLÉRATEURS STABILITÉ
export LLAMA_TEMPERATURE_OVERRIDE=0.3
export LLAMA_TOP_P_OVERRIDE=0.8
export LLAMA_REPETITION_PENALTY=1.2
export LLAMA_FREQUENCY_PENALTY=0.5

# ACCÉLÉRATEURS PERFORMANCE
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4
export OPENBLAS_NUM_THREADS=4

echo "✅ CORRECTIFS ANTI-BUG ACTIVÉS"
echo "🔥 Mode stabilité forcée"

# Activer l'environnement virtuel
if [ -d "venv_deepseek" ]; then
    echo "🔄 Activation environnement virtuel..."
    source venv_deepseek/bin/activate
    echo "✅ Environnement virtuel activé"
fi

# Chercher le modèle GGUF
MODEL_PATH=""
if [ -f "/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf" ]; then
    MODEL_PATH="/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf"
elif [ -f "DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf" ]; then
    MODEL_PATH="DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf"
else
    echo "❌ Modèle GGUF non trouvé"
    exit 1
fi

echo "📁 Modèle: $MODEL_PATH"
echo "🧠 Démarrage DeepSeek R1 8B - MODE CORRECTIF STABILITÉ"
echo "📡 Port: 8000 (API OpenAI compatible)"
echo "🔧 Correctifs: DISCONTINUITY, boucles, réflexion"
echo ""
echo "⏳ Chargement avec correctifs (30 secondes)..."
echo ""

# DÉMARRAGE LLAMA-SERVER MODE CORRECTIF STABILITÉ
llama-server \
    --model "$MODEL_PATH" \
    --host 0.0.0.0 \
    --port 8000 \
    --ctx-size 1024 \
    --threads 4 \
    --n-gpu-layers 28 \
    --batch-size 512 \
    --ubatch-size 128 \
    --n-predict 256 \
    --timeout 30 \
    --temp 0.3 \
    --top-p 0.8 \
    --repeat-penalty 1.2 \
    --mlock \
    --cont-batching \
    --parallel 2 \
    --verbose

echo ""
echo "🛑 DeepSeek CORRECTIF arrêté"
