#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST INTERFACE JARVIS CORRIGÉE
Teste l'interface avec une configuration simple pour éviter les problèmes d'affichage horizontal
"""

import gradio as gr
import requests
import json
import time

def test_chat_simple(message, history):
    """Test simple de chat sans complexité"""
    if not message.strip():
        return history, ""
    
    # Ajouter le message utilisateur
    history = history or []
    history.append([message, None])
    
    try:
        # Test avec une réponse simple
        response = f"✅ Message reçu: {message}\n\n🤖 JARVIS répond correctement !\n\nTest d'affichage:\n- Ligne 1\n- Ligne 2\n- Ligne 3"
        
        # Mettre à jour avec la réponse
        history[-1][1] = response
        
        return history, ""
        
    except Exception as e:
        history[-1][1] = f"❌ Erreur: {str(e)}"
        return history, ""

def create_test_interface():
    """Interface de test simplifiée"""
    
    # CSS minimal et fonctionnel
    css = """
    .gradio-container {
        max-width: 1200px !important;
        margin: 0 auto !important;
    }
    
    .chatbot {
        font-family: 'Segoe UI', sans-serif !important;
    }
    
    .message {
        margin: 8px 0 !important;
        padding: 12px !important;
        border-radius: 12px !important;
        max-width: 80% !important;
        word-wrap: break-word !important;
        white-space: pre-wrap !important;
    }
    
    .user {
        background: #007bff !important;
        color: white !important;
        margin-left: auto !important;
    }
    
    .bot {
        background: #f8f9fa !important;
        color: #333 !important;
        border: 1px solid #dee2e6 !important;
        margin-right: auto !important;
    }
    """
    
    with gr.Blocks(title="🧪 Test Interface JARVIS", theme=gr.themes.Default(), css=css) as demo:
        
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(135deg, #007bff, #0056b3); 
                    color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
            <h1>🧪 TEST INTERFACE JARVIS CORRIGÉE</h1>
            <p>Test pour vérifier que les messages s'affichent correctement (verticalement)</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=4):
                # Interface de chat simple
                chatbot = gr.Chatbot(
                    label="💬 Test Conversation",
                    height=400,
                    show_label=True,
                    show_copy_button=True
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="Message de test",
                        placeholder="Tapez votre message ici...",
                        scale=4,
                        lines=2
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
                    test_btn = gr.Button("🧪 Test Auto", variant="secondary")
            
            with gr.Column(scale=1):
                gr.HTML("""
                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                    <h4>📋 Tests à effectuer:</h4>
                    <ul style="font-size: 12px;">
                        <li>✅ Messages verticaux</li>
                        <li>✅ Pas d'affichage horizontal</li>
                        <li>✅ Bulles bien alignées</li>
                        <li>✅ Texte lisible</li>
                        <li>✅ Retours à la ligne</li>
                    </ul>
                </div>
                """)
        
        # Événements
        def test_auto():
            return [
                ["Test message 1", "✅ Réponse 1: Affichage vertical OK"],
                ["Test message 2 avec du texte plus long pour vérifier le retour à la ligne", "✅ Réponse 2: Le texte long s'affiche correctement sur plusieurs lignes"],
                ["Test avec code:\nprint('hello')\nfor i in range(3):\n    print(i)", "✅ Réponse 3: Code formaté correctement:\n\n```python\ndef test():\n    return 'OK'\n```"]
            ]
        
        send_btn.click(test_chat_simple, [msg, chatbot], [chatbot, msg])
        msg.submit(test_chat_simple, [msg, chatbot], [chatbot, msg])
        clear_btn.click(lambda: ([], ""), outputs=[chatbot, msg])
        test_btn.click(test_auto, outputs=chatbot)
    
    return demo

if __name__ == "__main__":
    print("🧪 LANCEMENT TEST INTERFACE JARVIS")
    print("URL: http://localhost:7865")
    
    demo = create_test_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7865,
        share=False,
        quiet=False
    )
