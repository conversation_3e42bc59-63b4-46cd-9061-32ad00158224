#!/usr/bin/env python3
"""
🚀 LANCEUR JARVIS COMMUNICATION COMPLÈTE
Lance l'interface de communication principale + toutes les fenêtres
Créé pour Jean-Luc Passave
"""

import os
import sys
import time
import webbrowser
import threading

def main():
    """Lance JARVIS avec l'interface de communication complète"""
    
    print("🤖 =" * 60)
    print("🤖 LANCEMENT JARVIS COMMUNICATION COMPLÈTE")
    print("🤖 Interface moderne comme Claude/ChatGPT")
    print("🤖 =" * 60)
    
    # Lancer l'architecture multi-fenêtres
    print("🚀 Lancement de l'architecture multi-fenêtres...")
    
    try:
        from jarvis_architecture_multi_fenetres import launch_all_windows
        
        # Lancer toutes les fenêtres en arrière-plan
        launch_thread = threading.Thread(target=launch_all_windows, daemon=True)
        launch_thread.start()
        
        # Attendre que les serveurs démarrent
        print("⏳ Démarrage des serveurs...")
        time.sleep(8)
        
        # Ouvrir automatiquement la fenêtre de communication principale
        print("💬 Ouverture de l'interface de communication principale...")
        webbrowser.open("http://localhost:7865")
        
        print("\n🎉 JARVIS COMMUNICATION COMPLÈTE LANCÉ !")
        print("=" * 70)
        print("💬 INTERFACE PRINCIPALE:")
        print("   💬 Communication: http://localhost:7865")
        print("   ✅ Chat complet avec JARVIS")
        print("   ✅ Micro 🎤 Haut-parleur 🔊 Caméra 📹")
        print("   ✅ Pensées en temps réel 🧠")
        print("   ✅ Recherche web 🌐")
        print("   ✅ Copier-coller avancé 📋")
        print("   ✅ Indicateur de travail ⏳")
        print("\n🏠 AUTRES FENÊTRES:")
        print("   🏠 Dashboard: http://localhost:7867")
        print("   💻 Code: http://localhost:7868")
        print("   🧠 Pensées: http://localhost:7869")
        print("   ⚙️ Config: http://localhost:7870")
        print("   📱 WhatsApp: http://localhost:7871")
        print("   🔐 Sécurité: http://localhost:7872")
        print("   📊 Monitoring: http://localhost:7873")
        print("   💾 Mémoire: http://localhost:7874")
        print("   🎨 Créativité: http://localhost:7875")
        print("   🎵 Musique: http://localhost:7876")
        print("   📊 Système: http://localhost:7877")
        print("=" * 70)
        print("\n🎯 FONCTIONNALITÉS UNIVERSELLES:")
        print("   ✅ JARVIS intégré dans chaque fenêtre")
        print("   ✅ Boutons retour accueil partout")
        print("   ✅ Contrôles multimédia universels")
        print("   ✅ Statut système en temps réel")
        print("   ✅ Indicateur de travail JARVIS")
        print("   ✅ Navigation rapide entre fenêtres")
        print("=" * 70)
        print("\n💡 UTILISATION:")
        print("   1. Utilisez l'interface de communication (port 7865) comme principale")
        print("   2. Naviguez vers les autres fenêtres selon vos besoins")
        print("   3. JARVIS est disponible partout pour vous aider")
        print("   4. Tous les contrôles sont accessibles depuis chaque fenêtre")
        print("\n🔄 Appuyez sur Ctrl+C pour arrêter tous les serveurs")
        
        # Garder le programme en vie
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de JARVIS...")
            print("👋 Au revoir Jean-Luc !")
            
    except ImportError as e:
        print(f"❌ Erreur d'importation: {e}")
        print("🔧 Vérifiez que tous les fichiers sont présents")
        return 1
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
