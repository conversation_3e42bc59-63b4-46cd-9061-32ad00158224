#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 DÉMARREUR AUTOMATIQUE DEEPSEEK - JEAN-LUC PASSAVE
Démarre automatiquement DeepSeek R1 8B si nécessaire
"""

import subprocess
import time
import requests
import os

def verifier_deepseek():
    """Vérifie si DeepSeek fonctionne"""
    try:
        response = requests.get("http://localhost:8000/v1/models", timeout=3)
        return response.status_code == 200
    except:
        return False

def demarrer_deepseek():
    """Démarre DeepSeek R1 8B"""
    print("🚀 DÉMARRAGE DEEPSEEK R1 8B")
    print("=" * 30)
    
    # Vérifier si déjà en cours
    if verifier_deepseek():
        print("✅ DeepSeek déjà en cours sur localhost:8000")
        return True
    
    print("🔍 Recherche script de démarrage...")
    
    # Scripts possibles
    scripts = [
        "demarrer_deepseek_optimise.sh",
        "demarrer_deepseek.sh", 
        "start_deepseek.sh"
    ]
    
    script_trouve = None
    for script in scripts:
        if os.path.exists(script):
            script_trouve = script
            print(f"✅ Script trouvé: {script}")
            break
    
    if not script_trouve:
        print("❌ Aucun script de démarrage trouvé")
        print("💡 Démarrage manuel nécessaire:")
        print("   vllm serve deepseek-ai/DeepSeek-R1-0528 --port 8000")
        return False
    
    # Démarrer DeepSeek
    print(f"🚀 Lancement: {script_trouve}")
    
    try:
        # Lancer en arrière-plan
        process = subprocess.Popen(
            ["bash", script_trouve],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="/Volumes/seagate/Louna_Electron_Latest"
        )
        
        print("⏳ Attente démarrage DeepSeek...")
        
        # Attendre que DeepSeek soit prêt (max 60 secondes)
        for i in range(60):
            time.sleep(1)
            if verifier_deepseek():
                print(f"✅ DeepSeek prêt après {i+1}s !")
                return True
            
            if i % 10 == 0:
                print(f"   Tentative {i+1}/60...")
        
        print("❌ Timeout - DeepSeek n'a pas démarré en 60s")
        return False
        
    except Exception as e:
        print(f"❌ Erreur démarrage: {e}")
        return False

def demarrer_jarvis_avec_deepseek():
    """Démarre DeepSeek puis JARVIS"""
    print("🤖 DÉMARRAGE COMPLET JARVIS + DEEPSEEK")
    print("=" * 50)
    
    # 1. Démarrer DeepSeek
    deepseek_ok = demarrer_deepseek()
    
    if not deepseek_ok:
        print("⚠️  DeepSeek non disponible - JARVIS fonctionnera en mode dégradé")
    
    # 2. Démarrer JARVIS
    print("\n🧠 DÉMARRAGE JARVIS...")
    
    try:
        # Vérifier l'environnement virtuel
        if os.path.exists("venv_deepseek/bin/activate"):
            cmd = "source venv_deepseek/bin/activate && python jarvis_interface_propre.py"
        else:
            cmd = "python3 jarvis_interface_propre.py"
        
        print(f"💻 Commande: {cmd}")
        print("📱 Interface: http://localhost:7860")
        print("🔧 DeepSeek: http://localhost:8000")
        
        # Lancer JARVIS
        process = subprocess.Popen(
            cmd,
            shell=True,
            cwd="/Volumes/seagate/Louna_Electron_Latest"
        )
        
        print("\n✅ JARVIS LANCÉ AVEC SUCCÈS !")
        print("🌐 Ouvrez http://localhost:7860 dans votre navigateur")
        
        if deepseek_ok:
            print("🤖 DeepSeek R1 8B opérationnel")
        else:
            print("⚠️  Mode dégradé (sans DeepSeek)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lancement JARVIS: {e}")
        return False

def status_systeme():
    """Affiche le statut du système"""
    print("📊 STATUT SYSTÈME")
    print("=" * 20)
    
    # DeepSeek
    if verifier_deepseek():
        print("🤖 DeepSeek R1 8B: ✅ Opérationnel")
    else:
        print("🤖 DeepSeek R1 8B: ❌ Arrêté")
    
    # JARVIS
    try:
        response = requests.get("http://localhost:7860", timeout=2)
        if response.status_code == 200:
            print("🧠 JARVIS Interface: ✅ Active")
        else:
            print("🧠 JARVIS Interface: ❌ Erreur")
    except:
        print("🧠 JARVIS Interface: ❌ Arrêtée")
    
    # Fichiers
    if os.path.exists("jarvis_interface_propre.py"):
        print("📁 Fichier JARVIS: ✅ Présent")
    else:
        print("📁 Fichier JARVIS: ❌ Manquant")

if __name__ == "__main__":
    print("🚀 DÉMARREUR AUTOMATIQUE DEEPSEEK + JARVIS")
    print("1. Démarrer DeepSeek seulement")
    print("2. Démarrer JARVIS seulement") 
    print("3. Démarrer les deux")
    print("4. Statut système")
    
    choix = input("\nVotre choix (1-4): ").strip()
    
    if choix == "1":
        demarrer_deepseek()
    elif choix == "2":
        demarrer_jarvis_avec_deepseek()
    elif choix == "3":
        demarrer_jarvis_avec_deepseek()
    elif choix == "4":
        status_systeme()
    else:
        print("❌ Choix invalide")
        status_systeme()
