#!/usr/bin/env python3
"""
🔐 JARVIS SECURITY BIOMETRIC SYSTEM
Système de sécurité biométrique complet pour JARVIS
- Reconnaissance vocale de Jean-Luc uniquement
- Reconnaissance faciale sécurisée
- Contrôle d'accès intelligent avec WhatsApp
- Chiffrement des données biométriques

Créé pour Jean-Luc Passave
"""

import os
import json
import time
import hashlib
import base64
import numpy as np
from cryptography.fernet import Fernet
import speech_recognition as sr
import cv2
import face_recognition
import threading
import requests
from datetime import datetime

class JarvisSecuritySystem:
    def __init__(self):
        self.security_config_file = "jarvis_security_config.encrypted"
        self.voice_profile_file = "jean_luc_voice_profile.encrypted"
        self.face_profile_file = "jean_luc_face_profile.encrypted"
        self.access_log_file = "jarvis_access_log.json"
        
        # Générer ou charger la clé de chiffrement
        self.encryption_key = self.get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # État de sécurité
        self.is_authenticated = False
        self.current_user = None
        self.security_level = "HIGH"  # HIGH, MEDIUM, LOW
        self.failed_attempts = 0
        self.max_failed_attempts = 3
        self.lockout_time = 300  # 5 minutes
        self.last_failed_attempt = 0
        
        # Profils biométriques
        self.jean_luc_voice_profile = None
        self.jean_luc_face_encoding = None
        
        # Initialisation
        self.load_security_profiles()
        self.setup_recognition_systems()

    def get_or_create_encryption_key(self):
        """Génère ou charge la clé de chiffrement"""
        key_file = "jarvis_security.key"
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            os.chmod(key_file, 0o600)  # Lecture seule pour le propriétaire
            return key

    def encrypt_data(self, data):
        """Chiffre les données sensibles"""
        if isinstance(data, str):
            data = data.encode()
        elif isinstance(data, (dict, list)):
            data = json.dumps(data).encode()
        return self.cipher.encrypt(data)

    def decrypt_data(self, encrypted_data):
        """Déchiffre les données"""
        try:
            decrypted = self.cipher.decrypt(encrypted_data)
            return decrypted.decode()
        except:
            return None

    def setup_recognition_systems(self):
        """Initialise les systèmes de reconnaissance"""
        try:
            # Reconnaissance vocale
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Reconnaissance faciale
            self.video_capture = cv2.VideoCapture(0)
            
            print("✅ Systèmes de reconnaissance initialisés")
        except Exception as e:
            print(f"❌ Erreur initialisation reconnaissance: {e}")

    def create_jean_luc_voice_profile(self):
        """Crée le profil vocal de Jean-Luc"""
        print("🎤 CRÉATION PROFIL VOCAL JEAN-LUC")
        print("Veuillez prononcer les phrases suivantes clairement:")
        
        phrases = [
            "Bonjour JARVIS, c'est Jean-Luc Passave",
            "JARVIS, active-toi et reconnais ma voix",
            "Je suis ton créateur, Jean-Luc Passave",
            "JARVIS, système de sécurité vocal activé",
            "Reconnaissance vocale Jean-Luc Passave confirmée"
        ]
        
        voice_samples = []
        
        for i, phrase in enumerate(phrases, 1):
            print(f"\n{i}/5: Dites: '{phrase}'")
            input("Appuyez sur Entrée quand vous êtes prêt...")
            
            try:
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source)
                    print("🎤 Enregistrement...")
                    audio = self.recognizer.listen(source, timeout=10)
                    
                    # Convertir en données pour analyse
                    audio_data = audio.get_wav_data()
                    voice_samples.append({
                        'phrase': phrase,
                        'audio_data': base64.b64encode(audio_data).decode(),
                        'timestamp': datetime.now().isoformat()
                    })
                    print("✅ Échantillon enregistré")
                    
            except Exception as e:
                print(f"❌ Erreur enregistrement: {e}")
                return False
        
        # Sauvegarder le profil vocal chiffré
        voice_profile = {
            'owner': 'Jean-Luc Passave',
            'created': datetime.now().isoformat(),
            'samples': voice_samples,
            'voice_signature': self.generate_voice_signature(voice_samples)
        }
        
        encrypted_profile = self.encrypt_data(voice_profile)
        with open(self.voice_profile_file, 'wb') as f:
            f.write(encrypted_profile)
        
        self.jean_luc_voice_profile = voice_profile
        print("✅ Profil vocal Jean-Luc créé et chiffré")
        return True

    def create_jean_luc_face_profile(self):
        """Crée le profil facial de Jean-Luc"""
        print("📹 CRÉATION PROFIL FACIAL JEAN-LUC")
        print("Regardez la caméra et appuyez sur ESPACE pour capturer votre visage")
        print("Appuyez sur Q pour terminer")
        
        face_encodings = []
        capture_count = 0
        min_captures = 5
        
        while capture_count < min_captures:
            ret, frame = self.video_capture.read()
            if not ret:
                continue
                
            # Détecter les visages
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            face_locations = face_recognition.face_locations(rgb_frame)
            
            # Afficher le frame avec les visages détectés
            for (top, right, bottom, left) in face_locations:
                cv2.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 2)
                cv2.putText(frame, f"Captures: {capture_count}/{min_captures}", 
                           (left, top-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            cv2.imshow('JARVIS - Création Profil Facial Jean-Luc', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' ') and len(face_locations) == 1:  # ESPACE et un seul visage
                # Encoder le visage
                face_encodings_frame = face_recognition.face_encodings(rgb_frame, face_locations)
                if face_encodings_frame:
                    face_encodings.append(face_encodings_frame[0])
                    capture_count += 1
                    print(f"✅ Capture {capture_count}/{min_captures} réussie")
                    
            elif key == ord('q'):
                break
        
        cv2.destroyAllWindows()
        
        if len(face_encodings) >= min_captures:
            # Calculer l'encoding moyen
            average_encoding = np.mean(face_encodings, axis=0)
            
            face_profile = {
                'owner': 'Jean-Luc Passave',
                'created': datetime.now().isoformat(),
                'encoding': average_encoding.tolist(),
                'samples_count': len(face_encodings),
                'confidence_threshold': 0.6
            }
            
            encrypted_profile = self.encrypt_data(face_profile)
            with open(self.face_profile_file, 'wb') as f:
                f.write(encrypted_profile)
            
            self.jean_luc_face_encoding = average_encoding
            print("✅ Profil facial Jean-Luc créé et chiffré")
            return True
        else:
            print("❌ Pas assez d'échantillons faciaux")
            return False

    def generate_voice_signature(self, voice_samples):
        """Génère une signature vocale unique"""
        # Simplification : hash des données audio
        combined_audio = ''.join([sample['audio_data'] for sample in voice_samples])
        return hashlib.sha256(combined_audio.encode()).hexdigest()

    def verify_voice(self, timeout=10):
        """Vérifie la voix de l'utilisateur"""
        if not self.jean_luc_voice_profile:
            return False, "Profil vocal non configuré"
        
        try:
            print("🎤 Vérification vocale - Dites: 'Bonjour JARVIS, c'est Jean-Luc'")
            
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
                audio = self.recognizer.listen(source, timeout=timeout)
                
                # Reconnaissance vocale basique
                try:
                    text = self.recognizer.recognize_google(audio, language="fr-FR")
                    print(f"Texte reconnu: {text}")
                    
                    # Vérifications simples
                    keywords = ['jarvis', 'jean-luc', 'jean', 'luc']
                    text_lower = text.lower()
                    
                    if any(keyword in text_lower for keyword in keywords):
                        print("✅ Reconnaissance vocale réussie")
                        return True, "Voix reconnue"
                    else:
                        return False, "Mots-clés non reconnus"
                        
                except sr.UnknownValueError:
                    return False, "Parole non comprise"
                except sr.RequestError as e:
                    return False, f"Erreur service reconnaissance: {e}"
                    
        except Exception as e:
            return False, f"Erreur vérification vocale: {e}"

    def verify_face(self):
        """Vérifie le visage de l'utilisateur"""
        if self.jean_luc_face_encoding is None:
            return False, "Profil facial non configuré"
        
        try:
            print("📹 Vérification faciale - Regardez la caméra")
            
            # Capturer plusieurs frames pour plus de précision
            for attempt in range(10):
                ret, frame = self.video_capture.read()
                if not ret:
                    continue
                
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                face_locations = face_recognition.face_locations(rgb_frame)
                face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)
                
                if face_encodings:
                    # Comparer avec le profil de Jean-Luc
                    matches = face_recognition.compare_faces(
                        [self.jean_luc_face_encoding], 
                        face_encodings[0],
                        tolerance=0.6
                    )
                    
                    if matches[0]:
                        print("✅ Visage reconnu - Jean-Luc Passave")
                        return True, "Visage reconnu"
                
                time.sleep(0.1)
            
            return False, "Visage non reconnu"
            
        except Exception as e:
            return False, f"Erreur vérification faciale: {e}"

    def authenticate_user(self):
        """Authentification complète (voix + visage)"""
        if self.is_locked_out():
            return False, "Système verrouillé - trop de tentatives échouées"
        
        print("🔐 AUTHENTIFICATION JARVIS SÉCURISÉE")
        print("Vérification biométrique en cours...")
        
        # Vérification vocale
        voice_ok, voice_msg = self.verify_voice()
        if not voice_ok:
            self.log_failed_attempt("voice", voice_msg)
            return False, f"Échec vocal: {voice_msg}"
        
        # Vérification faciale
        face_ok, face_msg = self.verify_face()
        if not face_ok:
            self.log_failed_attempt("face", face_msg)
            return False, f"Échec facial: {face_msg}"
        
        # Authentification réussie
        self.is_authenticated = True
        self.current_user = "Jean-Luc Passave"
        self.failed_attempts = 0
        self.log_successful_access()
        
        print("✅ AUTHENTIFICATION RÉUSSIE - Bienvenue Jean-Luc !")
        return True, "Authentification réussie"

    def is_locked_out(self):
        """Vérifie si le système est verrouillé"""
        if self.failed_attempts >= self.max_failed_attempts:
            time_since_last_attempt = time.time() - self.last_failed_attempt
            if time_since_last_attempt < self.lockout_time:
                return True
            else:
                # Reset après expiration du verrouillage
                self.failed_attempts = 0
        return False

    def log_failed_attempt(self, method, reason):
        """Enregistre une tentative d'accès échouée"""
        self.failed_attempts += 1
        self.last_failed_attempt = time.time()
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'method': method,
            'reason': reason,
            'failed_attempts': self.failed_attempts,
            'ip_address': 'localhost'  # À améliorer
        }
        
        self.save_access_log(log_entry)
        print(f"❌ Tentative échouée ({self.failed_attempts}/{self.max_failed_attempts})")

    def log_successful_access(self):
        """Enregistre un accès réussi"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'user': self.current_user,
            'method': 'biometric',
            'status': 'success',
            'ip_address': 'localhost'
        }
        
        self.save_access_log(log_entry)

    def save_access_log(self, log_entry):
        """Sauvegarde les logs d'accès"""
        try:
            if os.path.exists(self.access_log_file):
                with open(self.access_log_file, 'r') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            logs.append(log_entry)
            
            # Garder seulement les 1000 derniers logs
            if len(logs) > 1000:
                logs = logs[-1000:]
            
            with open(self.access_log_file, 'w') as f:
                json.dump(logs, f, indent=2)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde log: {e}")

    def load_security_profiles(self):
        """Charge les profils de sécurité"""
        try:
            # Charger profil vocal
            if os.path.exists(self.voice_profile_file):
                with open(self.voice_profile_file, 'rb') as f:
                    encrypted_data = f.read()
                decrypted_data = self.decrypt_data(encrypted_data)
                if decrypted_data:
                    self.jean_luc_voice_profile = json.loads(decrypted_data)
                    print("✅ Profil vocal Jean-Luc chargé")
            
            # Charger profil facial
            if os.path.exists(self.face_profile_file):
                with open(self.face_profile_file, 'rb') as f:
                    encrypted_data = f.read()
                decrypted_data = self.decrypt_data(encrypted_data)
                if decrypted_data:
                    face_data = json.loads(decrypted_data)
                    self.jean_luc_face_encoding = np.array(face_data['encoding'])
                    print("✅ Profil facial Jean-Luc chargé")
                    
        except Exception as e:
            print(f"❌ Erreur chargement profils: {e}")

    def setup_initial_security(self):
        """Configuration initiale de la sécurité"""
        print("🔐 CONFIGURATION INITIALE SÉCURITÉ JARVIS")
        print("Cette configuration ne se fait qu'une seule fois")
        
        if not self.jean_luc_voice_profile:
            if not self.create_jean_luc_voice_profile():
                return False
        
        if self.jean_luc_face_encoding is None:
            if not self.create_jean_luc_face_profile():
                return False
        
        print("✅ Configuration sécurité terminée")
        return True

    def __del__(self):
        """Nettoyage"""
        try:
            if hasattr(self, 'video_capture'):
                self.video_capture.release()
            cv2.destroyAllWindows()
        except:
            pass

# Fonction utilitaire pour l'interface
def initialize_jarvis_security():
    """Initialise le système de sécurité JARVIS"""
    security = JarvisSecuritySystem()
    
    # Vérifier si la configuration initiale est nécessaire
    if not security.jean_luc_voice_profile or security.jean_luc_face_encoding is None:
        print("🔧 Configuration initiale requise")
        if not security.setup_initial_security():
            print("❌ Échec configuration sécurité")
            return None
    
    return security

if __name__ == "__main__":
    print("🔐 JARVIS SECURITY BIOMETRIC SYSTEM")
    print("===================================")
    
    security = initialize_jarvis_security()
    if security:
        # Test d'authentification
        success, message = security.authenticate_user()
        if success:
            print("🎉 Accès autorisé à JARVIS !")
        else:
            print(f"🚫 Accès refusé: {message}")
    else:
        print("❌ Impossible d'initialiser le système de sécurité")
