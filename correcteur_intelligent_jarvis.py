#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR INTELLIGENT JARVIS - JEAN-LUC PASSAVE
Applique les corrections basées sur le code de référence de mémoire thermique
"""

import re
import os
import json
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔧 [{timestamp}] {message}")

def analyser_corrections_necessaires():
    """Analyse les corrections nécessaires basées sur les tests et la référence"""
    log("🔍 ANALYSE CORRECTIONS NÉCESSAIRES")
    
    corrections_identifiees = {
        "timeouts_deepseek": {
            "probleme": "Timeouts sur questions complexes (>20s)",
            "solution": "Augmenter timeouts et optimiser requêtes",
            "priorite": "HAUTE"
        },
        "structure_memoire_thermique": {
            "probleme": "Structure mémoire thermique incomplète vs r<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "solution": "Implémenter structure évolutive complète",
            "priorite": "HAUTE"
        },
        "fusion_conceptuelle": {
            "probleme": "Mémoire thermique pas intégrée naturellement",
            "solution": "Implémenter fusion conceptuelle native",
            "priorite": "MOYENNE"
        },
        "recherche_contextuelle": {
            "probleme": "Recherche floue basique",
            "solution": "Améliorer algorithme de recherche contextuelle",
            "priorite": "MOYENNE"
        },
        "apprentissage_evolutif": {
            "probleme": "Pas d'apprentissage continu des patterns",
            "solution": "Ajouter analyse habitudes et suggestions proactives",
            "priorite": "MOYENNE"
        },
        "performance_calculs": {
            "probleme": "Calculs BTP/URSSAF lents",
            "solution": "Cache intelligent pour réponses courantes",
            "priorite": "BASSE"
        }
    }
    
    log(f"✅ {len(corrections_identifiees)} corrections identifiées")
    return corrections_identifiees

def corriger_timeouts_deepseek():
    """Correction 1: Augmenter les timeouts DeepSeek"""
    log("🕐 CORRECTION TIMEOUTS DEEPSEEK")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Corrections timeouts
        corrections_timeout = [
            (r'timeout=10', 'timeout=45'),
            (r'timeout=15', 'timeout=60'),
            (r'timeout=20', 'timeout=90'),
            (r'timeout=30', 'timeout=120'),
            (r'max_tokens=200', 'max_tokens=1000'),
            (r'max_tokens=500', 'max_tokens=1500'),
        ]
        
        corrections_appliquees = 0
        for pattern, replacement in corrections_timeout:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                corrections_appliquees += 1
                log(f"✅ Timeout corrigé: {pattern} → {replacement}")
        
        # Ajouter gestion d'erreur améliorée
        if 'except requests.exceptions.Timeout' not in code:
            timeout_handler = '''
        except requests.exceptions.Timeout:
            print("⏰ Timeout DeepSeek - Réessai avec paramètres réduits")
            # Réessai avec moins de tokens
            payload["max_tokens"] = min(payload.get("max_tokens", 500), 200)
            try:
                response = requests.post(url, json=payload, timeout=30)
                return response
            except:
                print("❌ Échec définitif après réessai")
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ Erreur réseau DeepSeek: {e}")
            return None'''
            
            # Ajouter après les imports
            pos_imports = code.find('import requests')
            if pos_imports != -1:
                code = code[:pos_imports] + timeout_handler + "\n\n" + code[pos_imports:]
                corrections_appliquees += 1
                log("✅ Gestion d'erreur timeout ajoutée")
        
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log(f"✅ {corrections_appliquees} corrections timeout appliquées")
        return corrections_appliquees > 0
        
    except Exception as e:
        log(f"❌ Erreur correction timeouts: {e}")
        return False

def corriger_structure_memoire_thermique():
    """Correction 2: Implémenter structure mémoire thermique évolutive complète"""
    log("🧠 CORRECTION STRUCTURE MÉMOIRE THERMIQUE")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Nouvelle fonction de sauvegarde évolutive
        nouvelle_sauvegarde = '''
def save_to_thermal_memory_evolutive(user_message, agent_response, agent_type):
    """SAUVEGARDE MÉMOIRE THERMIQUE ÉVOLUTIVE - STRUCTURE COMPLÈTE"""
    try:
        # Charger la structure existante ou créer la structure évolutive
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                memory = json.load(f)
        else:
            memory = {
                "conversations_by_date": {},
                "conversations": [],
                "thermal_stats": {
                    "total_entries": 0,
                    "memory_size_mb": 0,
                    "active_zones": [],
                    "thermal_zone_priority": {
                        "keyword_indexing": 10,
                        "agent1_output": 7,
                        "agent2_output": 6,
                        "input_processing": 5,
                        "summary": 8
                    },
                    "last_cleanup": "",
                    "user_habits": {},
                    "frequent_topics": {},
                    "time_patterns": {}
                },
                "learning_data": {
                    "user_preferences": {},
                    "recurring_queries": [],
                    "proactive_suggestions": [],
                    "auto_summaries": []
                },
                "lastUpdate": ""
            }

        # Métadonnées de la conversation
        timestamp = time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        date_key = timestamp[:10]  # YYYY-MM-DD
        conversation_id = f"uuid-{int(time.time() * 1000)}"

        # Analyse avancée des données
        user_keywords = [w for w in user_message.lower().split() if len(w) > 3][:10]
        response_keywords = [w for w in agent_response.lower().split() if len(w) > 3][:10]

        # Calcul complexité avancée
        unique_words = len(set(user_keywords))
        total_words = len(user_keywords)
        complexity_score = (unique_words / max(total_words, 1)) * 10  # Score 0-10

        # Priorité thermique basée sur complexité et longueur
        thermal_priority = min(10, int(complexity_score + (len(user_message) / 50)))

        # Structure de conversation évolutive
        conversation_entry = {
            "id": conversation_id,
            "timestamp": timestamp,
            "user_message": user_message,
            "agent_response": agent_response,
            "agent": agent_type,
            "thermal_zone": "input_processing" if agent_type == "user" else f"{agent_type}_output",
            "keywords": user_keywords + response_keywords,
            "complexity": complexity_score,
            "processing_time": time.time(),
            "message_length": len(user_message),
            "thermal_priority": thermal_priority
        }

        # Ajouter à conversations_by_date
        if date_key not in memory["conversations_by_date"]:
            memory["conversations_by_date"][date_key] = []
        memory["conversations_by_date"][date_key].append(conversation_entry)

        # Ajouter à conversations (compatibilité)
        memory["conversations"].append(conversation_entry)

        # Mise à jour thermal_stats évolutive
        memory["thermal_stats"]["total_entries"] = len(memory["conversations"])
        memory["thermal_stats"]["memory_size_mb"] = len(json.dumps(memory)) / 1024 / 1024
        memory["thermal_stats"]["active_zones"] = ["input_processing", f"{agent_type}_output", "keyword_indexing"]
        memory["thermal_stats"]["last_agent"] = agent_type

        # Analyse patterns utilisateur (évolutif)
        hour = timestamp[11:13]
        memory["thermal_stats"]["time_patterns"][hour] = memory["thermal_stats"]["time_patterns"].get(hour, 0) + 1

        # Analyse sujets fréquents
        for keyword in user_keywords:
            memory["thermal_stats"]["frequent_topics"][keyword] = memory["thermal_stats"]["frequent_topics"].get(keyword, 0) + 1

        # Learning data évolutif
        if len(user_message) > 50:  # Messages significatifs
            memory["learning_data"]["recurring_queries"].append({
                "query": user_message[:100],
                "timestamp": timestamp,
                "complexity": complexity_score
            })

        # Garder seulement les 50 dernières requêtes
        if len(memory["learning_data"]["recurring_queries"]) > 50:
            memory["learning_data"]["recurring_queries"] = memory["learning_data"]["recurring_queries"][-50:]

        # Mise à jour finale
        memory["lastUpdate"] = timestamp

        # Sauvegarder la structure évolutive complète
        with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(memory, f, ensure_ascii=False, indent=2)

        print(f"🧠 MÉMOIRE THERMIQUE ÉVOLUTIVE: {memory['thermal_stats']['total_entries']} entrées | Complexité: {complexity_score:.1f}")
        return True

    except Exception as e:
        print(f"❌ ERREUR SAUVEGARDE ÉVOLUTIVE: {e}")
        return False
'''
        
        # Remplacer l'ancienne fonction de sauvegarde
        if 'def save_to_thermal_memory(' in code:
            # Trouver la fonction existante
            start_pos = code.find('def save_to_thermal_memory(')
            if start_pos != -1:
                # Trouver la fin de la fonction
                end_pos = code.find('\ndef ', start_pos + 1)
                if end_pos == -1:
                    end_pos = len(code)
                
                # Remplacer
                code = code[:start_pos] + nouvelle_sauvegarde + code[end_pos:]
                log("✅ Fonction sauvegarde évolutive remplacée")
            else:
                # Ajouter avant create_interface
                pos_create = code.find("def create_interface():")
                if pos_create != -1:
                    code = code[:pos_create] + nouvelle_sauvegarde + "\n\n" + code[pos_create:]
                    log("✅ Fonction sauvegarde évolutive ajoutée")
        
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction structure: {e}")
        return False

def corriger_fusion_conceptuelle():
    """Correction 3: Implémenter fusion conceptuelle native"""
    log("🔗 CORRECTION FUSION CONCEPTUELLE")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Fonction de fusion conceptuelle
        fusion_conceptuelle = '''
def prepend_memory_context(user_message):
    """FUSION CONCEPTUELLE - Intègre automatiquement la mémoire thermique"""
    try:
        # RECHERCHE INTELLIGENTE AUTOMATIQUE
        memory_results = contextual_search_advanced(user_message)
        
        if not memory_results:
            return ""
        
        # CONTEXTE INTÉGRÉ - Pas de distinction mémoire/corpus
        context = f"\\n\\nTES SOUVENIRS PERTINENTS (partie de ta mémoire):\\n"
        
        for i, result in enumerate(memory_results[:3], 1):
            # Extraire l'essentiel sans révéler la structure technique
            timestamp = result.get('timestamp', '')[:10]  # Date seulement
            content = result.get('content', '')[:150]
            
            context += f"{i}. {timestamp}: {content}...\\n"
        
        context += "\\nUtilise ces souvenirs naturellement dans ta réponse.\\n"
        
        return context
        
    except Exception as e:
        print(f"❌ ERREUR CONTEXTE MÉMOIRE: {e}")
        return ""

def contextual_search_advanced(query):
    """RECHERCHE CONTEXTUELLE AVANCÉE avec fuzzy matching"""
    try:
        # 1. RECHERCHE EXACTE par mots-clés
        exact_results = search_memory(query)
        if exact_results and len(exact_results) >= 2:
            return exact_results
        
        # 2. RECHERCHE FLOUE si résultats insuffisants
        fuzzy_results = fuzzy_memory_search_advanced(query)
        
        # 3. COMBINER et scorer les résultats
        all_results = exact_results + fuzzy_results
        
        # Dédupliquer par ID
        seen_ids = set()
        unique_results = []
        for result in all_results:
            if result.get('id') not in seen_ids:
                seen_ids.add(result.get('id'))
                unique_results.append(result)
        
        # Trier par pertinence (timestamp récent + longueur contenu)
        unique_results.sort(key=lambda x: (
            x.get('timestamp', ''), 
            len(x.get('content', ''))
        ), reverse=True)
        
        return unique_results[:5]  # Top 5 résultats
        
    except Exception as e:
        print(f"❌ ERREUR RECHERCHE CONTEXTUELLE: {e}")
        return []

def fuzzy_memory_search_advanced(query):
    """RECHERCHE FLOUE AVANCÉE dans la mémoire thermique"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return []
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        conversations = data.get('conversations', [])
        results = []
        
        query_words = set(query.lower().split())
        
        for conv in conversations:
            # Recherche floue dans le contenu
            user_msg = conv.get('user_message', '')
            agent_resp = conv.get('agent_response', '')
            content = f"{user_msg} {agent_resp}"
            content_words = set(content.lower().split())
            
            # Calcul de similarité amélioré (intersection/union)
            if query_words and content_words:
                intersection = len(query_words.intersection(content_words))
                union = len(query_words.union(content_words))
                similarity = intersection / union if union > 0 else 0
                
                # Bonus pour mots-clés importants
                important_words = ['jarvis', 'jean-luc', 'important', 'urgent']
                for word in important_words:
                    if word in content.lower():
                        similarity += 0.1
                
                # Seuil de similarité
                if similarity > 0.15:  # 15% de similarité minimum
                    results.append({
                        'id': conv.get('id'),
                        'timestamp': conv.get('timestamp', ''),
                        'content': content[:200],
                        'similarity': similarity,
                        'sender': 'Jean-Luc',
                        'agent': conv.get('agent', 'agent1')
                    })
        
        # Trier par similarité
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        return results[:3]  # Top 3 résultats flous
        
    except Exception as e:
        print(f"❌ ERREUR RECHERCHE FLOUE: {e}")
        return []
'''
        
        # Ajouter les fonctions avant create_interface
        pos_create = code.find("def create_interface():")
        if pos_create != -1:
            code = code[:pos_create] + fusion_conceptuelle + "\n\n" + code[pos_create:]
            log("✅ Fusion conceptuelle ajoutée")
        
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur fusion conceptuelle: {e}")
        return False

def corriger_apprentissage_evolutif():
    """Correction 4: Ajouter apprentissage évolutif et suggestions proactives"""
    log("🎓 CORRECTION APPRENTISSAGE ÉVOLUTIF")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Fonctions d'apprentissage évolutif
        apprentissage_evolutif = '''
def analyze_user_habits():
    """PATTERN 1 - Analyse des habitudes de Jean-Luc"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return "❌ Aucune donnée disponible"
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        conversations = data.get('conversations', [])
        
        # Analyse des requêtes fréquentes
        user_queries = []
        topics = {}
        time_patterns = {}
        
        for conv in conversations:
            if 'user_message' in conv:
                content = conv.get('user_message', '').lower()
                user_queries.append(content)
                
                # Analyse des sujets
                words = content.split()
                for word in words:
                    if len(word) > 3:  # Mots significatifs
                        topics[word] = topics.get(word, 0) + 1
                
                # Analyse temporelle
                timestamp = conv.get('timestamp', '')
                if timestamp:
                    hour = timestamp[11:13]
                    time_patterns[hour] = time_patterns.get(hour, 0) + 1
        
        # Top sujets
        top_topics = sorted(topics.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Heures d'activité
        top_hours = sorted(time_patterns.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "total_queries": len(user_queries),
            "unique_topics": len(topics),
            "top_topics": top_topics,
            "preferred_hours": top_hours,
            "activity_periods": len(time_patterns)
        }
        
    except Exception as e:
        return f"❌ **ERREUR ANALYSE HABITUDES**: {str(e)}"

def suggest_proactive_actions():
    """PATTERN 2 - Suggestions proactives basées sur l'historique"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return []
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        conversations = data.get('conversations', [])
        
        # Analyser les patterns de questions récentes
        recent_topics = []
        
        for conv in conversations[-10:]:  # 10 dernières conversations
            if 'user_message' in conv:
                content = conv.get('user_message', '').lower()
                words = [w for w in content.split() if len(w) > 4]
                recent_topics.extend(words[:3])
        
        # Suggestions intelligentes
        suggestions = []
        
        if 'code' in ' '.join(recent_topics):
            suggestions.append("💻 Veux-tu que je révise ton code récent ?")
        
        if 'application' in ' '.join(recent_topics) or 'lance' in ' '.join(recent_topics):
            suggestions.append("🚀 Dois-je scanner tes nouvelles applications ?")
        
        if 'mémoire' in ' '.join(recent_topics):
            suggestions.append("🧠 Veux-tu voir l'évolution de notre mémoire partagée ?")
        
        if 'jarvis' in ' '.join(recent_topics):
            suggestions.append("🤖 Veux-tu que j'analyse mes performances récentes ?")
        
        if not suggestions:
            suggestions = [
                "🔍 Veux-tu que je scanne tes applications ?",
                "📊 Dois-je analyser tes habitudes de travail ?",
                "🧠 Veux-tu voir nos conversations récentes ?"
            ]
        
        return suggestions[:3]  # Max 3 suggestions
        
    except Exception as e:
        print(f"❌ ERREUR SUGGESTIONS: {e}")
        return []

def analyze_evolutionary_memory():
    """ANALYSE LA STRUCTURE ÉVOLUTIVE DE LA MÉMOIRE THERMIQUE"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return "❌ Aucune mémoire thermique trouvée"
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            full_memory = json.load(f)

        # Statistiques structure évolutive
        conversations_by_date = full_memory.get("conversations_by_date", {})
        thermal_stats = full_memory.get("thermal_stats", {})
        learning_data = full_memory.get("learning_data", {})

        # Analyse par date
        total_conversations = len(full_memory.get("conversations", []))

        # Analyse zones thermiques
        zone_priorities = thermal_stats.get("thermal_zone_priority", {})
        frequent_topics = thermal_stats.get("frequent_topics", {})
        time_patterns = thermal_stats.get("time_patterns", {})

        # Top sujets
        top_topics = sorted(frequent_topics.items(), key=lambda x: x[1], reverse=True)[:5]

        # Heures d'activité
        top_hours = sorted(time_patterns.items(), key=lambda x: x[1], reverse=True)[:3]

        # Analyse learning data
        recurring_queries = learning_data.get("recurring_queries", [])

        return {
            "total_conversations": total_conversations,
            "dates_count": len(conversations_by_date),
            "zones_count": len(zone_priorities),
            "topics_count": len(frequent_topics),
            "patterns_count": len(time_patterns),
            "queries_count": len(recurring_queries),
            "top_topics": top_topics,
            "top_hours": top_hours,
            "memory_size": thermal_stats.get("memory_size_mb", 0)
        }

    except Exception as e:
        return f"❌ Erreur analyse évolutive: {str(e)}"
'''
        
        # Ajouter les fonctions avant create_interface
        pos_create = code.find("def create_interface():")
        if pos_create != -1:
            code = code[:pos_create] + apprentissage_evolutif + "\n\n" + code[pos_create:]
            log("✅ Apprentissage évolutif ajouté")
        
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur apprentissage évolutif: {e}")
        return False

def appliquer_toutes_corrections():
    """Applique toutes les corrections identifiées"""
    log("🔧 APPLICATION TOUTES CORRECTIONS")
    print("=" * 60)
    
    # Analyser les corrections nécessaires
    corrections = analyser_corrections_necessaires()
    log(f"📋 {len(corrections)} corrections à appliquer")
    
    corrections_reussies = 0
    
    # 1. Corriger timeouts DeepSeek
    log("CORRECTION 1: Timeouts DeepSeek")
    if corriger_timeouts_deepseek():
        corrections_reussies += 1
    
    # 2. Corriger structure mémoire thermique
    log("CORRECTION 2: Structure mémoire thermique")
    if corriger_structure_memoire_thermique():
        corrections_reussies += 1
    
    # 3. Corriger fusion conceptuelle
    log("CORRECTION 3: Fusion conceptuelle")
    if corriger_fusion_conceptuelle():
        corrections_reussies += 1
    
    # 4. Corriger apprentissage évolutif
    log("CORRECTION 4: Apprentissage évolutif")
    if corriger_apprentissage_evolutif():
        corrections_reussies += 1
    
    # Résultat
    print("\n" + "=" * 60)
    log("📊 RÉSULTAT CORRECTIONS")
    print("=" * 60)
    
    print(f"✅ Corrections réussies: {corrections_reussies}/4")
    
    if corrections_reussies >= 3:
        print("🎉 JARVIS COMPLÈTEMENT CORRIGÉ !")
        print("🕐 Timeouts DeepSeek optimisés")
        print("🧠 Mémoire thermique évolutive complète")
        print("🔗 Fusion conceptuelle native")
        print("🎓 Apprentissage évolutif actif")
        print("🚀 Performance maximale attendue")
        return True
    else:
        print("⚠️ CORRECTIONS PARTIELLES")
        return False

if __name__ == "__main__":
    print("🔧 CORRECTEUR INTELLIGENT JARVIS")
    print("Applique toutes les corrections nécessaires")
    print("=" * 50)
    
    if appliquer_toutes_corrections():
        print("\n🎉 JARVIS COMPLÈTEMENT CORRIGÉ !")
        print("Redémarrez pour voir les améliorations")
    else:
        print("\n⚠️ CORRECTIONS PARTIELLES")
        print("Vérification manuelle nécessaire")
