#!/bin/bash

# 🚀 LANCEMENT RAPIDE JARVIS
# Lance JARVIS avec génération neuronale en arrière-plan

echo "🚀 LANCEMENT RAPIDE JARVIS"
echo "=========================="

cd /Volumes/seagate/Louna_Electron_Latest

# Activer environnement
source venv_deepseek/bin/activate

echo "✅ Environnement activé"

# Vérifier DeepSeek
if curl -s http://localhost:8000/v1/models > /dev/null 2>&1; then
    echo "✅ DeepSeek R1 8B opérationnel"
else
    echo "❌ DeepSeek R1 8B non accessible"
    echo "🔄 Relancement DeepSeek..."
    ./demarrer_deepseek_CORRECTIF_URGENCE.sh &
    sleep 10
fi

# Lancer JARVIS en arrière-plan
echo "🚀 Lancement JARVIS interface..."
python jarvis_interface_propre.py &
JARVIS_PID=$!

echo "✅ JARVIS lancé (PID: $JARVIS_PID)"
echo "⏳ Attente démarrage interface..."

# Attendre que l'interface soit disponible
for i in {1..30}; do
    if curl -s http://localhost:7863 > /dev/null 2>&1; then
        echo "✅ Interface JARVIS disponible sur http://localhost:7863"
        break
    fi
    echo "   ⏳ Tentative $i/30..."
    sleep 2
done

# Vérifier statut final
if curl -s http://localhost:7863 > /dev/null 2>&1; then
    echo ""
    echo "🎉 JARVIS OPÉRATIONNEL !"
    echo "🌐 Interface: http://localhost:7863"
    echo "🧠 DeepSeek R1 8B: http://localhost:8000"
    echo "💾 T7 Sync: Actif"
    echo "⚡ Accélérateurs: Chargés"
    echo "🧠 Neurones: Génération en cours (86+ milliards)"
    echo ""
    echo "📱 Ouvrir dans le navigateur:"
    echo "   open http://localhost:7863"
    echo ""
else
    echo "❌ Interface JARVIS non accessible"
    echo "📋 Vérifier les logs:"
    echo "   tail -f /tmp/jarvis.log"
fi
