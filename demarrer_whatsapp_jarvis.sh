#!/bin/bash

echo "📱 =================================="
echo "🚀 DÉMARRAGE WHATSAPP JARVIS"
echo "📱 =================================="

# Vérifier que JARVIS principal est démarré
echo "🔍 Vérification JARVIS principal..."
if ! curl -s http://localhost:8000/v1/models > /dev/null; then
    echo "⚠️ JARVIS principal (DeepSeek) n'est pas démarré"
    echo "🚀 Démarrage automatique de JARVIS..."
    ./demarrer_jarvis_optimise.sh &
    sleep 10
fi

if ! curl -s http://localhost:7863 > /dev/null; then
    echo "⚠️ Interface JARVIS n'est pas accessible"
    echo "🚀 Veuillez démarrer JARVIS d'abord avec: ./demarrer_jarvis_optimise.sh"
    exit 1
fi

echo "✅ JARVIS principal opérationnel"

# Démarrer le service WhatsApp
echo ""
echo "📱 Démarrage du service WhatsApp JARVIS..."
echo "📱 Un QR code va apparaître - scannez-le avec votre téléphone"
echo ""

node jarvis_whatsapp_integration.js
