{"name": "fluent-ffmpeg", "version": "2.1.2", "description": "A fluent API to FFMPEG (http://www.ffmpeg.org)", "keywords": ["ffmpeg"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"mail": "<EMAIL>", "url": "http://github.com/fluent-ffmpeg/node-fluent-ffmpeg/issues"}, "repository": "git://github.com/fluent-ffmpeg/node-fluent-ffmpeg.git", "devDependencies": {"mocha": "latest", "should": "latest", "jsdoc": "latest"}, "dependencies": {"async": ">=0.2.9", "which": "^1.1.1"}, "engines": {"node": ">=0.8.0"}, "main": "index", "scripts": {"test": "make test"}}