{"name": "whatsapp-web.js", "version": "1.30.0", "description": "Library for interacting with the WhatsApp Web API ", "main": "./index.js", "typings": "./index.d.ts", "scripts": {"test": "mocha tests --recursive --timeout 5000", "test-single": "mocha", "shell": "node --experimental-repl-await ./shell.js", "generate-docs": "npx jsdoc --configure .jsdoc.json --verbose"}, "repository": {"type": "git", "url": "git+https://github.com/pedroslopez/whatsapp-web.js.git"}, "keywords": ["whatsapp", "whatsapp-web", "api", "bot", "client", "node"], "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/pedroslopez/whatsapp-web.js/issues"}, "homepage": "https://wwebjs.dev/", "dependencies": {"@pedroslopez/moduleraid": "^5.0.2", "fluent-ffmpeg": "2.1.2", "mime": "^3.0.0", "node-fetch": "^2.6.9", "node-webpmux": "3.1.7", "puppeteer": "^18.2.1"}, "devDependencies": {"@types/node-fetch": "^2.5.12", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "dotenv": "^16.0.0", "eslint": "^8.4.1", "eslint-plugin-mocha": "^10.0.3", "jsdoc": "^3.6.4", "jsdoc-baseline": "^0.1.5", "mocha": "^9.0.2", "sinon": "^13.0.1"}, "engines": {"node": ">=18.0.0"}, "optionalDependencies": {"archiver": "^5.3.1", "fs-extra": "^10.1.0", "unzipper": "^0.10.11"}}