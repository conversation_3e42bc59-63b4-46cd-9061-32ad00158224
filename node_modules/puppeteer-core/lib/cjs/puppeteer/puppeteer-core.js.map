{"version": 3, "file": "puppeteer-core.js", "sourceRoot": "", "sources": ["../../../src/puppeteer-core.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;AAGH,gEAA8C;AAC9C,qDAAmC;AACnC,gEAA8C;AAC9C,2DAAyC;AAEzC,iDAA2C;AAC3C,8DAAsD;AACtD,4CAAmD;AAEnD;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,gCAAa,CAAC;IAClC,WAAW,EAAE,IAAA,6BAAmB,EAAC,0BAAW,CAAC;IAC7C,eAAe,EAAE,IAAI;CACtB,CAAC,CAAC;AAGD,eAAO,GAKL,SAAS,UAJX,4BAAoB,GAIlB,SAAS,uBAHX,mBAAW,GAGT,SAAS,cAFX,sBAAc,GAEZ,SAAS,iBADX,cAAM,GACJ,SAAS,QAAC;AAEd,kBAAe,SAAS,CAAC"}