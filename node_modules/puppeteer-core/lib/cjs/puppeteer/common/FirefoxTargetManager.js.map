{"version": 3, "file": "FirefoxTargetManager.js", "sourceRoot": "", "sources": ["../../../../src/common/FirefoxTargetManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAGH,iDAAyC;AACzC,mDAAuD;AASvD,uDAA+C;AAE/C;;;;;;;;;;;;;GAaG;AACH,MAAa,oBACX,SAAQ,8BAAY;IAiDpB,YACE,UAAsB,EACtB,aAA4B,EAC5B,oBAA2C;QAE3C,KAAK,EAAE,CAAC;;QAnDV,mDAAwB;QACxB;;;;;;;;;WASG;QACH,4DACE,IAAI,GAAG,EAAE,EAAC;QACZ;;;;;WAKG;QACH,2DAAmD,IAAI,GAAG,EAAE,EAAC;QAC7D;;WAEG;QACH,4DAAoD,IAAI,GAAG,EAAE,EAAC;QAC9D;;;WAGG;QACH,+CAAkB,IAAI,GAAG,EAAU,EAAC;QACpC,6DAAwD;QACxD,sDAA8B;QAE9B,mDACE,IAAI,OAAO,EAAE,EAAC;QAEhB,mEAGI,IAAI,OAAO,EAAE,EAAC;QAElB,mDAAsB,GAAG,EAAE,GAAE,CAAC,EAAC;QAC/B,kDAAoC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACxD,uBAAA,IAAI,4CAAuB,OAAO,MAAA,CAAC;QACrC,CAAC,CAAC,EAAC;QACH,kDAAkC,IAAI,GAAG,EAAE,EAAC;QAiD5C,kDAAqB,CAAC,OAAmB,EAAE,EAAE;YAC3C,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACrC,uBAAA,IAAI,gDAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACzC,uBAAA,IAAI,yDAA6B,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACzD,CAAC,EAAC;QA2BF,gDAAmB,KAAK,EACtB,KAAyC,EAC1B,EAAE;YACjB,IAAI,uBAAA,IAAI,yDAA6B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBACpE,OAAO;aACR;YAED,uBAAA,IAAI,yDAA6B,CAAC,GAAG,CACnC,KAAK,CAAC,UAAU,CAAC,QAAQ,EACzB,KAAK,CAAC,UAAU,CACjB,CAAC;YAEF,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE;gBACpE,MAAM,MAAM,GAAG,uBAAA,IAAI,2CAAe,MAAnB,IAAI,EAAgB,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;gBAChE,uBAAA,IAAI,wDAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACxE,uBAAA,IAAI,0FAA6B,MAAjC,IAAI,EAA8B,MAAM,CAAC,SAAS,CAAC,CAAC;gBACpD,OAAO;aACR;YAED,IACE,uBAAA,IAAI,kDAAsB;gBAC1B,CAAC,uBAAA,IAAI,kDAAsB,MAA1B,IAAI,EAAuB,KAAK,CAAC,UAAU,CAAC,EAC7C;gBACA,uBAAA,IAAI,4CAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACpD,uBAAA,IAAI,0FAA6B,MAAjC,IAAI,EAA8B,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC7D,OAAO;aACR;YAED,MAAM,MAAM,GAAG,uBAAA,IAAI,2CAAe,MAAnB,IAAI,EAAgB,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAChE,uBAAA,IAAI,wDAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxE,IAAI,CAAC,IAAI,qEAA6C,MAAM,CAAC,CAAC;YAC9D,uBAAA,IAAI,0FAA6B,MAAjC,IAAI,EAA8B,MAAM,CAAC,SAAS,CAAC,CAAC;QACtD,CAAC,EAAC;QAEF,kDAAqB,CAAC,KAA2C,EAAQ,EAAE;YACzE,uBAAA,IAAI,yDAA6B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACzD,uBAAA,IAAI,0FAA6B,MAAjC,IAAI,EAA8B,KAAK,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,uBAAA,IAAI,wDAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpE,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,IAAI,2DAAwC,MAAM,CAAC,CAAC;gBACzD,uBAAA,IAAI,wDAA4B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;aACzD;QACH,CAAC,EAAC;QAEF,mDAAsB,KAAK,EACzB,aAAsC,EACtC,KAA4C,EAC5C,EAAE;YACF,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YACpC,MAAM,OAAO,GAAG,uBAAA,IAAI,wCAAY,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,WAAW,KAAK,CAAC,SAAS,mBAAmB,CAAC,CAAC;aAChE;YAED,MAAM,MAAM,GAAG,uBAAA,IAAI,wDAA4B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEzE,IAAA,kBAAM,EAAC,MAAM,EAAE,UAAU,UAAU,CAAC,QAAQ,aAAa,CAAC,CAAC;YAE3D,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAEvC,uBAAA,IAAI,yDAA6B,CAAC,GAAG,CACnC,OAAO,CAAC,EAAE,EAAE,EACZ,uBAAA,IAAI,wDAA4B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAE,CAC3D,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,uBAAA,IAAI,gDAAoB,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE;gBACpE,IAAI,CAAC,CAAC,aAAa,YAAY,0BAAU,CAAC,EAAE;oBAC1C,IAAA,kBAAM,EAAC,uBAAA,IAAI,yDAA6B,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;iBACnE;gBACD,MAAM,IAAI,CACR,MAAM,EACN,aAAa,YAAY,0BAAU;oBACjC,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,uBAAA,IAAI,yDAA6B,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,CAAE,CAC/D,CAAC;aACH;QACH,CAAC,EAAC;QApJA,uBAAA,IAAI,oCAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,8CAAyB,oBAAoB,MAAA,CAAC;QAClD,uBAAA,IAAI,uCAAkB,aAAa,MAAA,CAAC;QAEpC,uBAAA,IAAI,wCAAY,CAAC,EAAE,CAAC,sBAAsB,EAAE,uBAAA,IAAI,6CAAiB,CAAC,CAAC;QACnE,uBAAA,IAAI,wCAAY,CAAC,EAAE,CAAC,wBAAwB,EAAE,uBAAA,IAAI,+CAAmB,CAAC,CAAC;QACvE,uBAAA,IAAI,wCAAY,CAAC,EAAE,CAAC,iBAAiB,EAAE,uBAAA,IAAI,+CAAmB,CAAC,CAAC;QAChE,IAAI,CAAC,wBAAwB,CAAC,uBAAA,IAAI,wCAAY,CAAC,CAAC;IAClD,CAAC;IAED,oBAAoB,CAClB,MAA+B,EAC/B,WAA8B;QAE9B,MAAM,YAAY,GAAG,uBAAA,IAAI,gDAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAChE,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/B,uBAAA,IAAI,gDAAoB,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC;IAED,uBAAuB,CACrB,MAA+B,EAC/B,WAA8B;QAE9B,MAAM,YAAY,GAAG,uBAAA,IAAI,gDAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAChE,uBAAA,IAAI,gDAAoB,CAAC,GAAG,CAC1B,MAAM,EACN,YAAY,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE;YACvC,OAAO,kBAAkB,KAAK,WAAW,CAAC;QAC5C,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,wBAAwB,CAAC,OAAgC;QACvD,MAAM,QAAQ,GAAG,CAAC,KAA4C,EAAE,EAAE;YAChE,OAAO,uBAAA,IAAI,gDAAoB,MAAxB,IAAI,EAAqB,OAAO,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC;QACF,IAAA,kBAAM,EAAC,CAAC,uBAAA,IAAI,gEAAoC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,uBAAA,IAAI,gEAAoC,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAChE,OAAO,CAAC,EAAE,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;IAQD,sBAAsB,CAAC,OAAmB;QACxC,IAAI,uBAAA,IAAI,gEAAoC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACzD,OAAO,CAAC,GAAG,CACT,yBAAyB,EACzB,uBAAA,IAAI,gEAAoC,CAAC,GAAG,CAAC,OAAO,CAAE,CACvD,CAAC;YACF,uBAAA,IAAI,gEAAoC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC1D;IACH,CAAC;IAED,mBAAmB;QACjB,OAAO,uBAAA,IAAI,wDAA4B,CAAC;IAC1C,CAAC;IAED,OAAO;QACL,uBAAA,IAAI,wCAAY,CAAC,GAAG,CAAC,sBAAsB,EAAE,uBAAA,IAAI,6CAAiB,CAAC,CAAC;QACpE,uBAAA,IAAI,wCAAY,CAAC,GAAG,CAAC,wBAAwB,EAAE,uBAAA,IAAI,+CAAmB,CAAC,CAAC;IAC1E,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,uBAAA,IAAI,wCAAY,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAC3E,uBAAA,IAAI,2CAAsB,IAAI,GAAG,CAAC,uBAAA,IAAI,yDAA6B,CAAC,IAAI,EAAE,CAAC,MAAA,CAAC;QAC5E,MAAM,uBAAA,IAAI,+CAAmB,CAAC;IAChC,CAAC;CAsFF;AApND,oDAoNC;olCAN8B,QAAgB;IAC3C,uBAAA,IAAI,+CAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACzC,IAAI,uBAAA,IAAI,+CAAmB,CAAC,IAAI,KAAK,CAAC,EAAE;QACtC,uBAAA,IAAI,gDAAoB,MAAxB,IAAI,CAAsB,CAAC;KAC5B;AACH,CAAC"}