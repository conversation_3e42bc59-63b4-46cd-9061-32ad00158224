{"version": 3, "file": "Input.js", "sourceRoot": "", "sources": ["../../../../src/common/Input.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAEH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAC,eAAe,EAA0B,MAAM,uBAAuB,CAAC;AAQ/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AACH,MAAM,OAAO,QAAQ;IASnB;;OAEG;IACH,YAAY,MAAkB;;QAX9B,mCAAoB;QACpB,gCAAe,IAAI,GAAG,EAAU,EAAC;QAEjC;;WAEG;QACH,eAAU,GAAG,CAAC,CAAC;QAMb,uBAAA,IAAI,oBAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,KAAK,CAAC,IAAI,CACR,GAAa,EACb,UAA2B,EAAC,IAAI,EAAE,SAAS,EAAC;QAE5C,MAAM,WAAW,GAAG,uBAAA,IAAI,8DAAyB,MAA7B,IAAI,EAA0B,GAAG,CAAC,CAAC;QAEvD,MAAM,UAAU,GAAG,uBAAA,IAAI,6BAAa,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3D,uBAAA,IAAI,6BAAa,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,UAAU,IAAI,uBAAA,IAAI,kDAAa,MAAjB,IAAI,EAAc,WAAW,CAAC,GAAG,CAAC,CAAC;QAEtD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;QAC1E,MAAM,uBAAA,IAAI,wBAAQ,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChD,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY;YACrC,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,qBAAqB,EAAE,WAAW,CAAC,OAAO;YAC1C,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,IAAI,EAAE,IAAI;YACV,cAAc,EAAE,IAAI;YACpB,UAAU;YACV,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ,KAAK,CAAC;SACrC,CAAC,CAAC;IACL,CAAC;IAwED;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,CAAC,GAAa;QACpB,MAAM,WAAW,GAAG,uBAAA,IAAI,8DAAyB,MAA7B,IAAI,EAA0B,GAAG,CAAC,CAAC;QAEvD,IAAI,CAAC,UAAU,IAAI,CAAC,uBAAA,IAAI,kDAAa,MAAjB,IAAI,EAAc,WAAW,CAAC,GAAG,CAAC,CAAC;QACvD,uBAAA,IAAI,6BAAa,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,uBAAA,IAAI,wBAAQ,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChD,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,qBAAqB,EAAE,WAAW,CAAC,OAAO;YAC1C,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;SAC/B,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,MAAM,uBAAA,IAAI,wBAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,OAAO,CAAC,CAAC,eAAe,CAAC,IAAgB,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,KAAK,CAAC,IAAI,CAAC,IAAY,EAAE,UAA4B,EAAE;QACrD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC;QACzC,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;YACvB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBACxB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC;aACjC;iBAAM;gBACL,IAAI,KAAK,EAAE;oBACT,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;wBACpB,OAAO,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC;iBACJ;gBACD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAChC;SACF;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,KAAK,CAAC,KAAK,CACT,GAAa,EACb,UAA2C,EAAE;QAE7C,MAAM,EAAC,KAAK,GAAG,IAAI,EAAC,GAAG,OAAO,CAAC;QAC/B,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9B,IAAI,KAAK,EAAE;YACT,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;SACJ;QACD,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;CACF;qKA5Lc,GAAW;IACtB,IAAI,GAAG,KAAK,KAAK,EAAE;QACjB,OAAO,CAAC,CAAC;KACV;IACD,IAAI,GAAG,KAAK,SAAS,EAAE;QACrB,OAAO,CAAC,CAAC;KACV;IACD,IAAI,GAAG,KAAK,MAAM,EAAE;QAClB,OAAO,CAAC,CAAC;KACV;IACD,IAAI,GAAG,KAAK,OAAO,EAAE;QACnB,OAAO,CAAC,CAAC;KACV;IACD,OAAO,CAAC,CAAC;AACX,CAAC,iFAEwB,SAAmB;IAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IAClC,MAAM,WAAW,GAAG;QAClB,GAAG,EAAE,EAAE;QACP,OAAO,EAAE,CAAC;QACV,IAAI,EAAE,EAAE;QACR,IAAI,EAAE,EAAE;QACR,QAAQ,EAAE,CAAC;KACZ,CAAC;IAEF,MAAM,UAAU,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;IAC9C,MAAM,CAAC,UAAU,EAAE,iBAAiB,SAAS,GAAG,CAAC,CAAC;IAElD,IAAI,UAAU,CAAC,GAAG,EAAE;QAClB,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;KAClC;IACD,IAAI,KAAK,IAAI,UAAU,CAAC,QAAQ,EAAE;QAChC,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC;KACvC;IAED,IAAI,UAAU,CAAC,OAAO,EAAE;QACtB,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;KAC1C;IACD,IAAI,KAAK,IAAI,UAAU,CAAC,YAAY,EAAE;QACpC,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC;KAC/C;IAED,IAAI,UAAU,CAAC,IAAI,EAAE;QACnB,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;KACpC;IAED,IAAI,UAAU,CAAC,QAAQ,EAAE;QACvB,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;KAC5C;IAED,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QAChC,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC;KACpC;IAED,IAAI,UAAU,CAAC,IAAI,EAAE;QACnB,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;KACpC;IACD,IAAI,KAAK,IAAI,UAAU,CAAC,SAAS,EAAE;QACjC,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC;KACzC;IAED,qEAAqE;IACrE,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE;QACxB,WAAW,CAAC,IAAI,GAAG,EAAE,CAAC;KACvB;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AA+IH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsEG;AACH,MAAM,OAAO,KAAK;IAOhB;;OAEG;IACH,YAAY,MAAkB,EAAE,QAAkB;QATlD,gCAAoB;QACpB,kCAAoB;QACpB,mBAAK,CAAC,EAAC;QACP,mBAAK,CAAC,EAAC;QACP,wBAAgC,MAAM,EAAC;QAMrC,uBAAA,IAAI,iBAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,mBAAa,QAAQ,MAAA,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,IAAI,CACR,CAAS,EACT,CAAS,EACT,UAA4B,EAAE;QAE9B,MAAM,EAAC,KAAK,GAAG,CAAC,EAAC,GAAG,OAAO,CAAC;QAC5B,MAAM,KAAK,GAAG,uBAAA,IAAI,gBAAG,EACnB,KAAK,GAAG,uBAAA,IAAI,gBAAG,CAAC;QAClB,uBAAA,IAAI,YAAM,CAAC,MAAA,CAAC;QACZ,uBAAA,IAAI,YAAM,CAAC,MAAA,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,MAAM,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAClD,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,uBAAA,IAAI,qBAAQ;gBACpB,CAAC,EAAE,KAAK,GAAG,CAAC,uBAAA,IAAI,gBAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC1C,CAAC,EAAE,KAAK,GAAG,CAAC,uBAAA,IAAI,gBAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC1C,SAAS,EAAE,uBAAA,IAAI,uBAAU,CAAC,UAAU;aACrC,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAK,CACT,CAAS,EACT,CAAS,EACT,UAA2C,EAAE;QAE7C,MAAM,EAAC,KAAK,GAAG,IAAI,EAAC,GAAG,OAAO,CAAC;QAC/B,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;SACxB;aAAM;YACL,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;SACxB;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,IAAI,CAAC,UAAwB,EAAE;QACnC,MAAM,EAAC,MAAM,GAAG,MAAM,EAAE,UAAU,GAAG,CAAC,EAAC,GAAG,OAAO,CAAC;QAClD,uBAAA,IAAI,iBAAW,MAAM,MAAA,CAAC;QACtB,MAAM,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,cAAc;YACpB,MAAM;YACN,CAAC,EAAE,uBAAA,IAAI,gBAAG;YACV,CAAC,EAAE,uBAAA,IAAI,gBAAG;YACV,SAAS,EAAE,uBAAA,IAAI,uBAAU,CAAC,UAAU;YACpC,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,EAAE,CAAC,UAAwB,EAAE;QACjC,MAAM,EAAC,MAAM,GAAG,MAAM,EAAE,UAAU,GAAG,CAAC,EAAC,GAAG,OAAO,CAAC;QAClD,uBAAA,IAAI,iBAAW,MAAM,MAAA,CAAC;QACtB,MAAM,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,eAAe;YACrB,MAAM;YACN,CAAC,EAAE,uBAAA,IAAI,gBAAG;YACV,CAAC,EAAE,uBAAA,IAAI,gBAAG;YACV,SAAS,EAAE,uBAAA,IAAI,uBAAU,CAAC,UAAU;YACpC,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,KAAK,CAAC,UAA6B,EAAE;QACzC,MAAM,EAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAC,GAAG,OAAO,CAAC;QACzC,MAAM,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,YAAY;YAClB,CAAC,EAAE,uBAAA,IAAI,gBAAG;YACV,CAAC,EAAE,uBAAA,IAAI,gBAAG;YACV,MAAM;YACN,MAAM;YACN,SAAS,EAAE,uBAAA,IAAI,uBAAU,CAAC,UAAU;YACpC,WAAW,EAAE,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI,CAAC,KAAY,EAAE,MAAa;QACpC,MAAM,OAAO,GAAG,IAAI,OAAO,CAA0B,OAAO,CAAC,EAAE;YAC7D,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,EAAE;gBACjD,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,SAAS,CAAC,MAAa,EAAE,IAA6B;QAC1D,MAAM,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjD,IAAI,EAAE,WAAW;YACjB,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,SAAS,EAAE,uBAAA,IAAI,uBAAU,CAAC,UAAU;YACpC,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAa,EAAE,IAA6B;QACzD,MAAM,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjD,IAAI,EAAE,UAAU;YAChB,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,SAAS,EAAE,uBAAA,IAAI,uBAAU,CAAC,UAAU;YACpC,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI,CAAC,MAAa,EAAE,IAA6B;QACrD,MAAM,uBAAA,IAAI,qBAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjD,IAAI,EAAE,MAAM;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,SAAS,EAAE,uBAAA,IAAI,uBAAU,CAAC,UAAU;YACpC,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,WAAW,CACf,KAAY,EACZ,MAAa,EACb,UAA4B,EAAE;QAE9B,MAAM,EAAC,KAAK,GAAG,IAAI,EAAC,GAAG,OAAO,CAAC;QAC/B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC5C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACnC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,EAAE;YACT,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC1B,OAAO,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;SACJ;QACD,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9B,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;IAClB,CAAC;CACF;;AAED;;;GAGG;AACH,MAAM,OAAO,WAAW;IAItB;;OAEG;IACH,YAAY,MAAkB,EAAE,QAAkB;QANlD,sCAAoB;QACpB,wCAAoB;QAMlB,uBAAA,IAAI,uBAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,yBAAa,QAAQ,MAAA,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,GAAG,CAAC,CAAS,EAAE,CAAS;QAC5B,MAAM,WAAW,GAAG,CAAC,EAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAC3D,MAAM,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,YAAY;YAClB,WAAW;YACX,SAAS,EAAE,uBAAA,IAAI,6BAAU,CAAC,UAAU;SACrC,CAAC,CAAC;QACH,MAAM,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,uBAAA,IAAI,6BAAU,CAAC,UAAU;SACrC,CAAC,CAAC;IACL,CAAC;CACF"}