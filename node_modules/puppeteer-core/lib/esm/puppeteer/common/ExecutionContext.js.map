{"version": 3, "file": "ExecutionContext.js", "sourceRoot": "", "sources": ["../../../../src/common/ExecutionContext.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;AAKH,OAAO,EAAC,QAAQ,EAAC,MAAM,eAAe,CAAC;AACvC,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAErC,OAAO,EACL,cAAc,EACd,mBAAmB,EACnB,QAAQ,EACR,qBAAqB,GACtB,MAAM,WAAW,CAAC;AAEnB;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,wCAAwC,CAAC;AAC9E,MAAM,gBAAgB,GAAG,6CAA6C,CAAC;AAEvE;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,OAAO,gBAAgB;IAkB3B;;OAEG;IACH,YACE,MAAkB,EAClB,cAA4D,EAC5D,KAAqB;;QAErB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,uBAAA,IAAI,+DAAU,MAAd,IAAI,EAAW,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IACH,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,uBAAA,IAAI,+DAAU,MAAd,IAAI,EAAW,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;CAwJF;0EAtIC,KAAK,qCAIH,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;IAEf,MAAM,MAAM,GAAG,iBAAiB,qBAAqB,EAAE,CAAC;IAExD,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,UAAU,GAAG,YAAY,CAAC;QAChC,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;YAC/D,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,GAAG,IAAI,GAAG,MAAM,CAAC;QAE/B,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO;aAChE,IAAI,CAAC,kBAAkB,EAAE;YACxB,UAAU,EAAE,uBAAuB;YACnC,SAAS;YACT,aAAa;YACb,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;SAClB,CAAC;aACD,KAAK,CAAC,YAAY,CAAC,CAAC;QAEvB,IAAI,gBAAgB,EAAE;YACpB,MAAM,IAAI,KAAK,CACb,qBAAqB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,CAC9D,CAAC;SACH;QAED,OAAO,aAAa;YAClB,CAAC,CAAC,qBAAqB,CAAC,YAAY,CAAC;YACrC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KACxC;IAED,IAAI,YAAY,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;IAC3C,IAAI;QACF,IAAI,QAAQ,CAAC,GAAG,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;KACxC;IAAC,OAAO,KAAK,EAAE;QACd,6DAA6D;QAC7D,8BAA8B;QAC9B,IAAI,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YACrC,YAAY;gBACV,iBAAiB,GAAG,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SAC/D;aAAM;YACL,YAAY,GAAG,WAAW,GAAG,YAAY,CAAC;SAC3C;QACD,IAAI;YACF,IAAI,QAAQ,CAAC,GAAG,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;SACxC;QAAC,OAAO,KAAK,EAAE;YACd,8DAA8D;YAC9D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC9D;KACF;IACD,IAAI,qBAAqB,CAAC;IAC1B,IAAI;QACF,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAClE,mBAAmB,EAAE,YAAY,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI;YACxD,kBAAkB,EAAE,IAAI,CAAC,UAAU;YACnC,SAAS,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClE,aAAa;YACb,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;KACJ;IAAC,OAAO,KAAK,EAAE;QACd,IACE,KAAK,YAAY,SAAS;YAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE;YACA,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;SACxD;QACD,MAAM,KAAK,CAAC;KACb;IACD,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAC5C,MAAM,qBAAqB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAClD,IAAI,gBAAgB,EAAE;QACpB,MAAM,IAAI,KAAK,CACb,qBAAqB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,CAC9D,CAAC;KACH;IACD,OAAO,aAAa;QAClB,CAAC,CAAC,qBAAqB,CAAC,YAAY,CAAC;QACrC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAEvC,KAAK,UAAU,eAAe,CAE5B,GAAY;QAEZ,IAAI,GAAG,YAAY,OAAO,EAAE;YAC1B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;SACvB;QACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,mCAAmC;YACnC,OAAO,EAAC,mBAAmB,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAC,CAAC;SACpD;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE;YACtB,OAAO,EAAC,mBAAmB,EAAE,IAAI,EAAC,CAAC;SACpC;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;YAC5B,OAAO,EAAC,mBAAmB,EAAE,UAAU,EAAC,CAAC;SAC1C;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE;YAC7B,OAAO,EAAC,mBAAmB,EAAE,WAAW,EAAC,CAAC;SAC3C;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YACvB,OAAO,EAAC,mBAAmB,EAAE,KAAK,EAAC,CAAC;SACrC;QACD,MAAM,YAAY,GAAG,GAAG,IAAI,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;QACjE,IAAI,YAAY,EAAE;YAChB,IAAI,YAAY,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;gBAC5C,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;aACH;YACD,IAAI,YAAY,CAAC,QAAQ,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YACD,IAAI,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE;gBACnD,OAAO;oBACL,mBAAmB,EACjB,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB;iBAClD,CAAC;aACH;YACD,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE;gBACzC,OAAO,EAAC,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,KAAK,EAAC,CAAC;aACnD;YACD,OAAO,EAAC,QAAQ,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAC,CAAC;SACzD;QACD,OAAO,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC;IACtB,CAAC;AACH,CAAC;AAGH,MAAM,YAAY,GAAG,CAAC,KAAY,EAAqC,EAAE;IACvE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAAC,EAAE;QAChE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;KACtC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAAE;QAClE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;KACtC;IAED,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QAC/D,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAC9D;QACA,MAAM,IAAI,KAAK,CACb,uEAAuE,CACxE,CAAC;KACH;IACD,MAAM,KAAK,CAAC;AACd,CAAC,CAAC"}