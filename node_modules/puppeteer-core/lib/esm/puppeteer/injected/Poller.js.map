{"version": 3, "file": "Poller.js", "sourceRoot": "", "sources": ["../../../../src/injected/Poller.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAEH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EACL,qBAAqB,GAEtB,MAAM,4BAA4B,CAAC;AAWpC;;GAEG;AACH,MAAM,OAAO,cAAc;IAOzB,YAAY,EAAoB,EAAE,IAAU;QAN5C,qCAAsB;QAEtB,uCAAY;QAEZ,2CAA6B;QAC7B,0CAA8B;QAE5B,uBAAA,IAAI,sBAAO,EAAE,MAAA,CAAC;QACd,uBAAA,IAAI,wBAAS,IAAI,MAAA,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,OAAO,GAAG,CAAC,uBAAA,IAAI,2BAAY,qBAAqB,EAAK,MAAA,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,0BAAI,MAAR,IAAI,CAAM,CAAC;QAChC,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxB,OAAO;SACR;QAED,uBAAA,IAAI,4BAAa,IAAI,gBAAgB,CAAC,KAAK,IAAI,EAAE;YAC/C,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,0BAAI,MAAR,IAAI,CAAM,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO;aACR;YACD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,CAAC,MAAA,CAAC;QACH,uBAAA,IAAI,gCAAU,CAAC,OAAO,CAAC,uBAAA,IAAI,4BAAM,EAAE;YACjC,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,uBAAA,IAAI,+BAAS,EAAE,wBAAwB,CAAC,CAAC;QAChD,IAAI,CAAC,uBAAA,IAAI,+BAAS,CAAC,QAAQ,EAAE,EAAE;YAC7B,uBAAA,IAAI,+BAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACpD;QACD,IAAI,uBAAA,IAAI,gCAAU,EAAE;YAClB,uBAAA,IAAI,gCAAU,CAAC,UAAU,EAAE,CAAC;YAC5B,uBAAA,IAAI,4BAAa,SAAS,MAAA,CAAC;SAC5B;IACH,CAAC;IAED,MAAM;QACJ,MAAM,CAAC,uBAAA,IAAI,+BAAS,EAAE,wBAAwB,CAAC,CAAC;QAChD,OAAO,uBAAA,IAAI,+BAAS,CAAC;IACvB,CAAC;CACF;;AAED;;GAEG;AACH,MAAM,OAAO,SAAS;IAGpB,YAAY,EAAoB;QAFhC,gCAAsB;QACtB,qCAA8B;QAE5B,uBAAA,IAAI,iBAAO,EAAE,MAAA,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,OAAO,GAAG,CAAC,uBAAA,IAAI,sBAAY,qBAAqB,EAAK,MAAA,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,qBAAI,MAAR,IAAI,CAAM,CAAC;QAChC,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxB,OAAO;SACR;QAED,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE;YACtB,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE;gBACtB,OAAO;aACR;YACD,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,qBAAI,MAAR,IAAI,CAAM,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBACnC,OAAO;aACR;YACD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,CAAC;QACF,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,uBAAA,IAAI,0BAAS,EAAE,wBAAwB,CAAC,CAAC;QAChD,IAAI,CAAC,uBAAA,IAAI,0BAAS,CAAC,QAAQ,EAAE,EAAE;YAC7B,uBAAA,IAAI,0BAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACpD;IACH,CAAC;IAED,MAAM;QACJ,MAAM,CAAC,uBAAA,IAAI,0BAAS,EAAE,wBAAwB,CAAC,CAAC;QAChD,OAAO,uBAAA,IAAI,0BAAS,CAAC;IACvB,CAAC;CACF;;AAED;;GAEG;AAEH,MAAM,OAAO,cAAc;IAMzB,YAAY,EAAoB,EAAE,EAAU;QAL5C,qCAAsB;QACtB,qCAAY;QAEZ,2CAAyB;QACzB,0CAA8B;QAE5B,uBAAA,IAAI,sBAAO,EAAE,MAAA,CAAC;QACd,uBAAA,IAAI,sBAAO,EAAE,MAAA,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,OAAO,GAAG,CAAC,uBAAA,IAAI,2BAAY,qBAAqB,EAAK,MAAA,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,0BAAI,MAAR,IAAI,CAAM,CAAC;QAChC,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxB,OAAO;SACR;QAED,uBAAA,IAAI,4BAAa,WAAW,CAAC,KAAK,IAAI,EAAE;YACtC,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,0BAAI,MAAR,IAAI,CAAM,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO;aACR;YACD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,EAAE,uBAAA,IAAI,0BAAI,CAAC,MAAA,CAAC;IACf,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,uBAAA,IAAI,+BAAS,EAAE,wBAAwB,CAAC,CAAC;QAChD,IAAI,CAAC,uBAAA,IAAI,+BAAS,CAAC,QAAQ,EAAE,EAAE;YAC7B,uBAAA,IAAI,+BAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACpD;QACD,IAAI,uBAAA,IAAI,gCAAU,EAAE;YAClB,aAAa,CAAC,uBAAA,IAAI,gCAAU,CAAC,CAAC;YAC9B,uBAAA,IAAI,4BAAa,SAAS,MAAA,CAAC;SAC5B;IACH,CAAC;IAED,MAAM;QACJ,MAAM,CAAC,uBAAA,IAAI,+BAAS,EAAE,wBAAwB,CAAC,CAAC;QAChD,OAAO,uBAAA,IAAI,+BAAS,CAAC;IACvB,CAAC;CACF"}