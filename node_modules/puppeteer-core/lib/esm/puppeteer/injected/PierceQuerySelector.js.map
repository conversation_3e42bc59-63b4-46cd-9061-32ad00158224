{"version": 3, "file": "PierceQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/PierceQuerySelector.ts"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AAEjC;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,CACjC,IAAU,EACV,QAAgB,EACA,EAAE;IAClB,IAAI,KAAK,GAAgB,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,CAAC,IAAU,EAAE,EAAE;QAC5B,MAAM,IAAI,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;QACtE,GAAG;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAsB,CAAC;YAChD,IAAI,WAAW,CAAC,UAAU,EAAE;gBAC1B,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;aAChC;YACD,IAAI,WAAW,YAAY,UAAU,EAAE;gBACrC,SAAS;aACV;YACD,IAAI,WAAW,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACnE,KAAK,GAAG,WAAW,CAAC;aACrB;SACF,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;IACtC,CAAC,CAAC;IACF,IAAI,IAAI,YAAY,QAAQ,EAAE;QAC5B,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,MAAM,CAAC,IAAI,CAAC,CAAC;IACb,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CACpC,OAAa,EACb,QAAgB,EACL,EAAE;IACb,MAAM,MAAM,GAAc,EAAE,CAAC;IAC7B,MAAM,OAAO,GAAG,CAAC,IAAU,EAAE,EAAE;QAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;QACtE,GAAG;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAsB,CAAC;YAChD,IAAI,WAAW,CAAC,UAAU,EAAE;gBAC1B,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;aACjC;YACD,IAAI,WAAW,YAAY,UAAU,EAAE;gBACrC,SAAS;aACV;YACD,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACzD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC1B;SACF,QAAQ,IAAI,CAAC,QAAQ,EAAE,EAAE;IAC5B,CAAC,CAAC;IACF,IAAI,OAAO,YAAY,QAAQ,EAAE;QAC/B,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;KACnC;IACD,OAAO,CAAC,OAAO,CAAC,CAAC;IACjB,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC"}