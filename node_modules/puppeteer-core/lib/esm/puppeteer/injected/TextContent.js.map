{"version": 3, "file": "TextContent.js", "sourceRoot": "", "sources": ["../../../../src/injected/TextContent.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAMH,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AAE1E;;;;GAIG;AACH,MAAM,qBAAqB,GAAG,CAAC,IAAU,EAA+B,EAAE;IACxE,IAAI,IAAI,YAAY,iBAAiB,EAAE;QACrC,OAAO,IAAI,CAAC;KACb;IACD,IAAI,IAAI,YAAY,mBAAmB,EAAE;QACvC,OAAO,IAAI,CAAC;KACb;IACD,IACE,IAAI,YAAY,gBAAgB;QAChC,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EACzC;QACA,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG,CAAC,IAAU,EAAW,EAAE;;IACnE,OAAO,CACL,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,MAAA,QAAQ,CAAC,IAAI,0CAAE,QAAQ,CAAC,IAAI,CAAC,CAAA,CAC5E,CAAC;AACJ,CAAC,CAAC;AAYF;;GAEG;AACH,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAqB,CAAC;AAC1D,MAAM,cAAc,GAAG,CAAC,IAAiB,EAAE,EAAE;IAC3C,OAAO,IAAI,EAAE;QACX,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,IAAI,YAAY,UAAU,EAAE;YAC9B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;SAClB;aAAM;YACL,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;SACxB;KACF;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;AAC1C,MAAM,kBAAkB,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;IAC1D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KACjC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,IAAU,EAAe,EAAE;;IAC3D,IAAI,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,KAAK,EAAE;QACT,OAAO,KAAK,CAAC;KACd;IACD,KAAK,GAAG,EAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAC,CAAC;IAClC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;QACxC,OAAO,KAAK,CAAC;KACd;IAED,IAAI,gBAAgB,GAAG,EAAE,CAAC;IAC1B,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;QAC/B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEjC,IAAI,CAAC,gBAAgB,CACnB,OAAO,EACP,KAAK,CAAC,EAAE;YACN,cAAc,CAAC,KAAK,CAAC,MAA0B,CAAC,CAAC;QACnD,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAC5B,CAAC;KACH;SAAM;QACL,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE;YAClE,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;gBACrC,KAAK,CAAC,IAAI,IAAI,MAAA,KAAK,CAAC,SAAS,mCAAI,EAAE,CAAC;gBACpC,gBAAgB,IAAI,MAAA,KAAK,CAAC,SAAS,mCAAI,EAAE,CAAC;gBAC1C,SAAS;aACV;YACD,IAAI,gBAAgB,EAAE;gBACpB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACxC;YACD,gBAAgB,GAAG,EAAE,CAAC;YACtB,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,EAAE;gBACxC,KAAK,CAAC,IAAI,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;aAC7C;SACF;QACD,IAAI,gBAAgB,EAAE;YACpB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACxC;QACD,IAAI,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;YAC9C,KAAK,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;SACvD;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE;gBAC/B,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC;YACH,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACzB;KACF;IACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAClC,OAAO,KAAK,CAAC;AACf,CAAC,CAAC"}