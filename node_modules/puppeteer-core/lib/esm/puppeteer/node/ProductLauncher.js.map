{"version": 3, "file": "ProductLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/ProductLauncher.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAC,UAAU,EAAE,UAAU,EAAC,MAAM,IAAI,CAAC;AAC1C,OAAO,EAAE,MAAM,IAAI,CAAC;AAGpB,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAkBrD;;GAEG;AACH,MAAM,UAAU,wBAAwB,CACtC,OAA6B;IAE7B,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAE/B,IAAI,UAA8B,CAAC;IACnC,QAAQ,QAAQ,EAAE;QAChB,KAAK,OAAO;YACV,QAAQ,OAAO,EAAE;gBACf,KAAK,QAAQ;oBACX,UAAU,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,2CAA2C,CAAC;oBACvF,MAAM;gBACR,KAAK,aAAa;oBAChB,UAAU,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,gDAAgD,CAAC;oBAC5F,MAAM;gBACR,KAAK,eAAe;oBAClB,UAAU,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,+CAA+C,CAAC;oBAC3F,MAAM;gBACR,KAAK,YAAY;oBACf,UAAU,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,+CAA+C,CAAC;oBAC3F,MAAM;aACT;YACD,MAAM;QACR,KAAK,QAAQ;YACX,QAAQ,OAAO,EAAE;gBACf,KAAK,QAAQ;oBACX,UAAU;wBACR,8DAA8D,CAAC;oBACjE,MAAM;gBACR,KAAK,aAAa;oBAChB,UAAU;wBACR,wEAAwE,CAAC;oBAC3E,MAAM;gBACR,KAAK,eAAe;oBAClB,UAAU;wBACR,4EAA4E,CAAC;oBAC/E,MAAM;gBACR,KAAK,YAAY;oBACf,UAAU;wBACR,sEAAsE,CAAC;oBACzE,MAAM;aACT;YACD,MAAM;QACR,KAAK,OAAO;YACV,QAAQ,OAAO,EAAE;gBACf,KAAK,QAAQ;oBACX,UAAU,GAAG,2BAA2B,CAAC;oBACzC,MAAM;gBACR,KAAK,aAAa;oBAChB,UAAU,GAAG,gCAAgC,CAAC;oBAC9C,MAAM;gBACR,KAAK,YAAY;oBACf,UAAU,GAAG,oCAAoC,CAAC;oBAClD,MAAM;aACT;YACD,MAAM;KACT;IAED,IAAI,CAAC,UAAU,EAAE;QACf,MAAM,IAAI,KAAK,CACb,iDAAiD,OAAO,QAAQ,QAAQ,GAAG,CAC5E,CAAC;KACH;IAED,4CAA4C;IAC5C,IAAI;QACF,UAAU,CAAC,UAAU,CAAC,CAAC;KACxB;IAAC,OAAO,KAAK,EAAE;QACd,MAAM,IAAI,KAAK,CACb,wDAAwD,OAAO,SAAS,UAAU,IAAI,CACvF,CAAC;KACH;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CACnC,QAA0C;IAK1C,MAAM,EAAC,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,kBAAkB,EAAC,GACjE,QAAQ,CAAC;IACX,IAAI,YAAgC,CAAC;IACrC,sEAAsE;IACtE,IAAI,CAAC,gBAAgB,EAAE;QACrB,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC9D,IAAI,cAAc,EAAE;YAClB,MAAM,WAAW,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC;gBAC7C,CAAC,CAAC,4GAA4G;oBAC5G,cAAc;gBAChB,CAAC,CAAC,SAAS,CAAC;YACd,OAAO,EAAC,cAAc,EAAE,WAAW,EAAC,CAAC;SACtC;QACD,MAAM,kBAAkB,GAAG,2BAA2B,CAAC;QACvD,IACE,OAAO,KAAK,QAAQ;YACpB,EAAE,CAAC,QAAQ,EAAE,KAAK,QAAQ;YAC1B,EAAE,CAAC,IAAI,EAAE,KAAK,OAAO;YACrB,UAAU,CAAC,kBAAkB,CAAC,EAC9B;YACA,OAAO,EAAC,cAAc,EAAE,kBAAkB,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC;SACrE;QACD,YAAY;YACV,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;KAC7D;IACD,IAAI,CAAC,YAAY,EAAE;QACjB,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;KACH;IACD,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,YAAY,EAAE;QACtD,OAAO,EAAE,OAAO;QAChB,IAAI,EAAE,YAAY;KACnB,CAAC,CAAC;IAEH,IAAI,CAAC,gBAAgB,IAAI,OAAO,KAAK,QAAQ,EAAE;QAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC5D,IAAI,QAAQ,EAAE;YACZ,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,KAAK;gBACrC,CAAC,CAAC,0GAA0G;oBAC1G,YAAY,CAAC,cAAc;gBAC7B,CAAC,CAAC,SAAS,CAAC;YACd,OAAO,EAAC,cAAc,EAAE,YAAY,CAAC,cAAc,EAAE,WAAW,EAAC,CAAC;SACnE;KACF;IACD,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;IAErE,MAAM,WAAW,GAAG,+FAA+F,CAAC;IACpH,MAAM,UAAU,GAAG,kEAAkE,QAAQ,CAAC,kBAAkB,IAAI,CAAC;IACrH,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,KAAK;QACrC,CAAC,CAAC,oCAAoC,OAAO,cACzC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WACtC,EAAE;QACJ,CAAC,CAAC,SAAS,CAAC;IACd,OAAO,EAAC,cAAc,EAAE,YAAY,CAAC,cAAc,EAAE,WAAW,EAAC,CAAC;AACpE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAC5B,WAA+B,EAC/B,iBAAyB,EACzB,eAAwB,EACxB,UAAmB,QAAQ;IAE3B,QAAQ,OAAO,EAAE;QACf,KAAK,SAAS;YACZ,OAAO,IAAI,eAAe,CACxB,WAAW,EACX,iBAAiB,EACjB,eAAe,CAChB,CAAC;QACJ,KAAK,QAAQ;YACX,OAAO,IAAI,cAAc,CACvB,WAAW,EACX,iBAAiB,EACjB,eAAe,CAChB,CAAC;KACL;AACH,CAAC"}