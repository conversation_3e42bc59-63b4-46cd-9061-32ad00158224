CFBundleGetInfoString = "Chromium 107.0.5296.0, Autorių teisės priklauso „Chromium“ autoriams, 2022 m. Visos teisės saugomos.";
NSBluetoothAlwaysUsageDescription = "<PERSON> „Chromium“ galės pasiekti duomenis, svetain<PERSON>s taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSBluetoothPeripheralUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSCameraUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetain<PERSON>s taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSHumanReadableCopyright = "Autorių teisės priklauso „Chromium“ autoriams, 2022 m. Visos teisės saugomos.";
NSLocationUsageDescription = "<PERSON> „Chromium“ galės pasiekti duomenis, svetain<PERSON>s taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSMicrophoneUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
