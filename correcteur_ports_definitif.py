#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR PORTS DÉFINITIF - JEAN-LUC PASSAVE
Corrige TOUS les ports pour que DeepSeek soit sur 8000 et JARVIS sur 7865
"""

import re
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔧 [{timestamp}] {message}")

def corriger_tous_les_ports():
    """Corrige tous les ports dans le fichier"""
    log("🔧 CORRECTION DÉFINITIVE TOUS LES PORTS")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Backup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"jarvis_interface_propre_backup_ports_{timestamp}.py"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(code)
        log(f"💾 Backup: {backup_file}")
        
        corrections = 0
        
        # 1. Corriger toutes les URLs DeepSeek (7860 → 8000)
        patterns_deepseek = [
            (r'"http://localhost:7860/v1/chat/completions"', '"http://localhost:8000/v1/chat/completions"'),
            (r'localhost:7860/v1/chat/completions', 'localhost:8000/v1/chat/completions'),
            (r'DeepSeek.*localhost:7860', 'DeepSeek R1 8B via VLLM (localhost:8000)'),
            (r'DeepSeek R1 8B \(localhost:7860\)', 'DeepSeek R1 8B (localhost:8000)'),
        ]
        
        for pattern, replacement in patterns_deepseek:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                corrections += 1
                log(f"✅ DeepSeek: {pattern[:30]}... → 8000")
        
        # 2. Corriger les références d'interface (7860 → 7865)
        patterns_interface = [
            (r'Interface.*localhost:7860', 'Interface sur localhost:7865'),
            (r'Monitoring.*localhost:7860', 'Monitoring sur localhost:7865'),
            (r'Tableau de bord.*localhost:7860', 'Tableau de bord sur localhost:7865'),
            (r'http://localhost:7860(?!/v1)', 'http://localhost:7865'),
        ]
        
        for pattern, replacement in patterns_interface:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                corrections += 1
                log(f"✅ Interface: {pattern[:30]}... → 7865")
        
        # 3. Corriger les messages d'erreur
        error_patterns = [
            (r'localhost:7860\)', 'localhost:8000)'),
            (r'sur localhost:7860', 'sur localhost:8000'),
        ]
        
        for pattern, replacement in error_patterns:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                corrections += 1
                log(f"✅ Erreur: {pattern[:30]}... → 8000")
        
        # 4. Corriger le serveur final
        code = re.sub(r'print\("🔗 Serveur: localhost:7860"\)', 'print("🔗 Serveur: localhost:7865")', code)
        corrections += 1
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log(f"✅ {corrections} corrections appliquées")
        return True
        
    except Exception as e:
        log(f"❌ Erreur: {e}")
        return False

def verifier_corrections():
    """Vérifie que les corrections sont correctes"""
    log("🧪 VÉRIFICATION CORRECTIONS")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Vérifications
        deepseek_7860 = len(re.findall(r'localhost:7860/v1', code))
        deepseek_8000 = len(re.findall(r'localhost:8000/v1', code))
        interface_7865 = len(re.findall(r'localhost:7865', code))
        
        print(f"   DeepSeek sur 7860: {deepseek_7860} (doit être 0)")
        print(f"   DeepSeek sur 8000: {deepseek_8000} (doit être > 0)")
        print(f"   Interface sur 7865: {interface_7865} (doit être > 0)")
        
        if deepseek_7860 == 0 and deepseek_8000 > 0 and interface_7865 > 0:
            log("✅ Toutes les corrections sont correctes")
            return True
        else:
            log("❌ Corrections incomplètes")
            return False
        
    except Exception as e:
        log(f"❌ Erreur vérification: {e}")
        return False

def redemarrer_jarvis():
    """Redémarre JARVIS avec les bons ports"""
    log("🚀 REDÉMARRAGE JARVIS AVEC BONS PORTS")
    
    import subprocess
    import time
    
    try:
        # Arrêter processus existants
        subprocess.run(["pkill", "-f", "jarvis_interface_propre.py"], capture_output=True)
        time.sleep(2)
        
        # Lancer avec nouveaux ports
        process = subprocess.Popen([
            "bash", "-c", 
            "cd /Volumes/seagate/Louna_Electron_Latest && source venv_deepseek/bin/activate && python jarvis_interface_propre.py"
        ])
        
        log(f"🚀 JARVIS relancé (PID: {process.pid})")
        log("🌐 Interface: http://localhost:7865")
        log("🤖 DeepSeek: http://localhost:8000")
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur redémarrage: {e}")
        return False

if __name__ == "__main__":
    print("🔧 CORRECTEUR PORTS DÉFINITIF")
    print("Corrige DeepSeek → 8000 et JARVIS → 7865")
    print("=" * 50)
    
    if corriger_tous_les_ports():
        print("✅ Ports corrigés")
        
        if verifier_corrections():
            print("✅ Vérification OK")
            
            print("\n🚀 Redémarrage JARVIS...")
            if redemarrer_jarvis():
                print("🎉 JARVIS redémarré avec bons ports")
                print("🌐 Testez: http://localhost:7865")
            else:
                print("❌ Échec redémarrage")
        else:
            print("❌ Vérification échouée")
    else:
        print("❌ Échec correction ports")
