#!/usr/bin/env python3
"""
🧠 GESTIONNAIRE DE CONTEXTE INTELLIGENT JARVIS
Sépare les pensées internes des réponses utilisateur
C<PERSON><PERSON> par <PERSON>-<PERSON> - 19/06/2025
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class PrioriteSujet(Enum):
    """Niveaux de priorité des sujets"""
    CRITIQUE = 5      # Erreurs système, urgences
    HAUTE = 4         # Questions techniques importantes
    MOYENNE = 3       # Sujets de travail normaux
    BASSE = 2         # Suggestions, optimisations
    SOCIALE = 1       # Politesse, salutations

class StatutSujet(Enum):
    """Statut de traitement d'un sujet"""
    NON_TRAITE = "non_traité"
    EN_COURS = "en_cours"
    REPONDU = "répondu"
    REPORTE = "reporté"
    ABANDONNE = "abandonné"

@dataclass
class SujetContextuel:
    """Représente un sujet dans la file d'attente contextuelle"""
    id: str
    contenu: str
    priorite: PrioriteSujet
    statut: StatutSujet
    timestamp_creation: str
    timestamp_derniere_maj: str
    tentatives_reponse: int = 0
    contexte_associe: Dict[str, Any] = None
    reponse_partielle: str = ""
    
    def __post_init__(self):
        if self.contexte_associe is None:
            self.contexte_associe = {}

class GestionnaireContexteJARVIS:
    """Gestionnaire intelligent du contexte conversationnel"""
    
    def __init__(self):
        self.file_sujets: List[SujetContextuel] = []
        self.pensees_internes: List[str] = []
        self.mode_verbeux = False  # Affichage des pensées internes
        self.conversation_active = True
        self.derniere_interaction = datetime.now()
        self.seuil_timeout_sujet = 300  # 5 minutes
        
        # Patterns de reconnaissance
        self.patterns_sociaux = [
            "salut", "bonjour", "bonsoir", "ça va", "comment ça va",
            "comment allez-vous", "hello", "hi", "hey", "merci",
            "au revoir", "à bientôt", "bonne nuit", "bonne journée"
        ]
        
        self.patterns_techniques = [
            "code", "fonction", "erreur", "bug", "optimisation",
            "algorithme", "système", "mémoire", "performance"
        ]
        
    def analyser_message_utilisateur(self, message: str) -> Dict[str, Any]:
        """Analyse un message utilisateur et détermine sa nature"""
        message_lower = message.lower().strip()
        
        # Détection du type de message
        est_social = any(pattern in message_lower for pattern in self.patterns_sociaux)
        est_technique = any(pattern in message_lower for pattern in self.patterns_techniques)
        est_question = "?" in message or message_lower.startswith(("comment", "pourquoi", "quand", "où", "que", "qui"))
        
        # Détermination de la priorité
        if est_social and len(message.split()) <= 5:
            priorite = PrioriteSujet.SOCIALE
        elif "urgent" in message_lower or "critique" in message_lower:
            priorite = PrioriteSujet.CRITIQUE
        elif est_technique and est_question:
            priorite = PrioriteSujet.HAUTE
        elif est_technique:
            priorite = PrioriteSujet.MOYENNE
        else:
            priorite = PrioriteSujet.BASSE
            
        return {
            "message": message,
            "est_social": est_social,
            "est_technique": est_technique,
            "est_question": est_question,
            "priorite": priorite,
            "longueur": len(message.split()),
            "timestamp": datetime.now().isoformat()
        }
    
    def ajouter_sujet(self, contenu: str, priorite: PrioriteSujet = None, 
                     contexte: Dict[str, Any] = None) -> str:
        """Ajoute un nouveau sujet à la file d'attente"""
        
        # Analyse automatique si priorité non spécifiée
        if priorite is None:
            analyse = self.analyser_message_utilisateur(contenu)
            priorite = analyse["priorite"]
        
        # Génération ID unique
        sujet_id = f"sujet_{int(time.time() * 1000)}"
        timestamp = datetime.now().isoformat()
        
        nouveau_sujet = SujetContextuel(
            id=sujet_id,
            contenu=contenu,
            priorite=priorite,
            statut=StatutSujet.NON_TRAITE,
            timestamp_creation=timestamp,
            timestamp_derniere_maj=timestamp,
            contexte_associe=contexte or {}
        )
        
        # Insertion selon la priorité
        self._inserer_par_priorite(nouveau_sujet)
        
        # Pensée interne
        self._ajouter_pensee_interne(f"Nouveau sujet ajouté: {contenu[:50]}... (Priorité: {priorite.name})")
        
        return sujet_id
    
    def _inserer_par_priorite(self, nouveau_sujet: SujetContextuel):
        """Insère un sujet dans la file selon sa priorité"""
        inserted = False
        for i, sujet in enumerate(self.file_sujets):
            if nouveau_sujet.priorite.value > sujet.priorite.value:
                self.file_sujets.insert(i, nouveau_sujet)
                inserted = True
                break
        
        if not inserted:
            self.file_sujets.append(nouveau_sujet)
    
    def obtenir_sujet_prioritaire(self) -> Optional[SujetContextuel]:
        """Retourne le sujet de plus haute priorité non traité"""
        for sujet in self.file_sujets:
            if sujet.statut == StatutSujet.NON_TRAITE:
                return sujet
        return None
    
    def marquer_sujet_en_cours(self, sujet_id: str):
        """Marque un sujet comme en cours de traitement"""
        for sujet in self.file_sujets:
            if sujet.id == sujet_id:
                sujet.statut = StatutSujet.EN_COURS
                sujet.timestamp_derniere_maj = datetime.now().isoformat()
                sujet.tentatives_reponse += 1
                break
    
    def marquer_sujet_repondu(self, sujet_id: str, reponse: str = ""):
        """Marque un sujet comme répondu"""
        for sujet in self.file_sujets:
            if sujet.id == sujet_id:
                sujet.statut = StatutSujet.REPONDU
                sujet.timestamp_derniere_maj = datetime.now().isoformat()
                if reponse:
                    sujet.reponse_partielle = reponse
                break
    
    def generer_reponse_contextuelle(self, message_utilisateur: str) -> Dict[str, str]:
        """Génère une réponse en séparant pensées internes et réponse utilisateur"""
        
        # Analyse du message
        analyse = self.analyser_message_utilisateur(message_utilisateur)
        
        # Ajouter le message comme nouveau sujet
        sujet_id = self.ajouter_sujet(message_utilisateur, analyse["priorite"], analyse)
        
        # Pensées internes
        pensees = []
        
        # Vérifier les sujets en attente
        sujets_non_traites = [s for s in self.file_sujets if s.statut == StatutSujet.NON_TRAITE]
        if len(sujets_non_traites) > 1:
            pensees.append(f"J'ai {len(sujets_non_traites)} sujets en file d'attente")
        
        # Analyser le contexte
        if analyse["est_social"]:
            pensees.append("Message social détecté - priorité à la politesse")
            reponse_utilisateur = self._generer_reponse_sociale(message_utilisateur)
        elif analyse["est_technique"]:
            pensees.append("Question technique détectée - analyse approfondie nécessaire")
            reponse_utilisateur = self._generer_reponse_technique(message_utilisateur, analyse)
        else:
            pensees.append("Message général - traitement standard")
            reponse_utilisateur = self._generer_reponse_generale(message_utilisateur)
        
        # Marquer le sujet comme répondu
        self.marquer_sujet_repondu(sujet_id, reponse_utilisateur)
        
        # Ajouter info sur sujets en attente si pertinent
        if len(sujets_non_traites) > 1 and not analyse["est_social"]:
            pensees.append(f"Sujets restants à traiter: {len(sujets_non_traites) - 1}")
        
        # Compilation des pensées
        pensees_internes = " | ".join(pensees) if pensees else "Traitement direct"
        
        return {
            "pensees_internes": pensees_internes,
            "reponse_utilisateur": reponse_utilisateur,
            "mode_verbeux": self.mode_verbeux,
            "sujets_en_attente": len(sujets_non_traites) - 1
        }
    
    def _generer_reponse_sociale(self, message: str) -> str:
        """Génère une réponse sociale appropriée"""
        message_lower = message.lower()
        
        if any(salut in message_lower for salut in ["salut", "bonjour", "hello", "hi"]):
            return "Salut ! Je vais très bien, merci. Comment puis-je t'aider ?"
        elif "ça va" in message_lower or "comment ça va" in message_lower:
            return "Ça va très bien ! Mes systèmes fonctionnent parfaitement. Et toi ?"
        elif "merci" in message_lower:
            return "De rien ! C'est toujours un plaisir de t'aider."
        elif any(au_revoir in message_lower for au_revoir in ["au revoir", "à bientôt", "bye"]):
            return "À bientôt ! N'hésite pas à revenir quand tu veux."
        else:
            return "Merci pour ton message ! Comment puis-je t'aider ?"
    
    def _generer_reponse_technique(self, message: str, analyse: Dict) -> str:
        """Génère une réponse technique"""
        if analyse["est_question"]:
            return f"Excellente question technique ! Je vais analyser '{message}' en détail et te donner une réponse complète."
        else:
            return f"J'ai bien noté ton point technique sur '{message}'. Je vais l'examiner et revenir vers toi."
    
    def _generer_reponse_generale(self, message: str) -> str:
        """Génère une réponse générale"""
        return f"J'ai bien reçu ton message. Je vais traiter '{message}' et te donner une réponse appropriée."
    
    def _ajouter_pensee_interne(self, pensee: str):
        """Ajoute une pensée interne"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.pensees_internes.append(f"[{timestamp}] {pensee}")
        
        # Garder seulement les 50 dernières pensées
        if len(self.pensees_internes) > 50:
            self.pensees_internes = self.pensees_internes[-50:]
    
    def activer_mode_verbeux(self):
        """Active l'affichage des pensées internes"""
        self.mode_verbeux = True
        return "🧠 Mode verbeux activé - Les pensées internes seront affichées"
    
    def desactiver_mode_verbeux(self):
        """Désactive l'affichage des pensées internes"""
        self.mode_verbeux = False
        return "🤫 Mode verbeux désactivé - Seules les réponses directes seront affichées"
    
    def get_statut_file_attente(self) -> str:
        """Retourne le statut de la file d'attente"""
        total = len(self.file_sujets)
        non_traites = len([s for s in self.file_sujets if s.statut == StatutSujet.NON_TRAITE])
        en_cours = len([s for s in self.file_sujets if s.statut == StatutSujet.EN_COURS])
        repondus = len([s for s in self.file_sujets if s.statut == StatutSujet.REPONDU])
        
        rapport = f"📊 **FILE D'ATTENTE CONTEXTUELLE**\n\n"
        rapport += f"• Total sujets: {total}\n"
        rapport += f"• Non traités: {non_traites}\n"
        rapport += f"• En cours: {en_cours}\n"
        rapport += f"• Répondus: {repondus}\n\n"
        
        if non_traites > 0:
            rapport += "**SUJETS EN ATTENTE:**\n"
            for sujet in self.file_sujets:
                if sujet.statut == StatutSujet.NON_TRAITE:
                    rapport += f"• {sujet.priorite.name}: {sujet.contenu[:50]}...\n"
        
        return rapport
    
    def nettoyer_sujets_anciens(self):
        """Nettoie les sujets trop anciens"""
        maintenant = datetime.now()
        sujets_a_garder = []
        
        for sujet in self.file_sujets:
            timestamp_sujet = datetime.fromisoformat(sujet.timestamp_creation)
            age_secondes = (maintenant - timestamp_sujet).total_seconds()
            
            if age_secondes < self.seuil_timeout_sujet or sujet.statut != StatutSujet.REPONDU:
                sujets_a_garder.append(sujet)
        
        anciens_supprimés = len(self.file_sujets) - len(sujets_a_garder)
        self.file_sujets = sujets_a_garder
        
        if anciens_supprimés > 0:
            self._ajouter_pensee_interne(f"Nettoyage: {anciens_supprimés} sujets anciens supprimés")
        
        return f"🧹 Nettoyage effectué: {anciens_supprimés} sujets anciens supprimés"

# Instance globale du gestionnaire de contexte
GESTIONNAIRE_CONTEXTE = GestionnaireContexteJARVIS()

def traiter_message_avec_contexte(message: str) -> Dict[str, str]:
    """Interface principale pour traiter un message avec gestion de contexte"""
    return GESTIONNAIRE_CONTEXTE.generer_reponse_contextuelle(message)

def activer_pensees_internes():
    """Active l'affichage des pensées internes"""
    return GESTIONNAIRE_CONTEXTE.activer_mode_verbeux()

def desactiver_pensees_internes():
    """Désactive l'affichage des pensées internes"""
    return GESTIONNAIRE_CONTEXTE.desactiver_mode_verbeux()

def get_file_attente_contexte():
    """Retourne l'état de la file d'attente contextuelle"""
    return GESTIONNAIRE_CONTEXTE.get_statut_file_attente()

def nettoyer_contexte_ancien():
    """Nettoie les anciens sujets du contexte"""
    return GESTIONNAIRE_CONTEXTE.nettoyer_sujets_anciens()

if __name__ == "__main__":
    # Test du gestionnaire de contexte
    print("🧠 Test du gestionnaire de contexte JARVIS")
    
    gestionnaire = GestionnaireContexteJARVIS()
    gestionnaire.activer_mode_verbeux()
    
    # Test de messages variés
    messages_test = [
        "Salut, ça va ?",
        "Comment optimiser la mémoire thermique ?",
        "Peux-tu corriger ce bug urgent ?",
        "Merci pour ton aide",
        "Quelle est la meilleure approche pour les algorithmes ?"
    ]
    
    for message in messages_test:
        print(f"\n--- MESSAGE: {message} ---")
        reponse = gestionnaire.generer_reponse_contextuelle(message)
        print(f"PENSÉES: {reponse['pensees_internes']}")
        print(f"RÉPONSE: {reponse['reponse_utilisateur']}")
    
    print(f"\n{gestionnaire.get_statut_file_attente()}")
