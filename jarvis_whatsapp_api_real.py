#!/usr/bin/env python3
"""
📱 JARVIS WHATSAPP API RÉELLE
API WhatsApp réelle avec Twilio pour communication proactive
JARVIS peut maintenant vraiment vous contacter !

C<PERSON><PERSON> pour Jean-Luc <PERSON>
"""

import json
import time
import requests
from datetime import datetime
import os
from twilio.rest import Client
import threading
import schedule

class JarvisWhatsAppReal:
    def __init__(self):
        self.config_file = "jarvis_whatsapp_config.json"
        self.sent_messages_file = "jarvis_whatsapp_sent.json"
        self.pending_messages_file = "jarvis_whatsapp_pending.json"
        
        # Configuration Twilio (à configurer)
        self.twilio_account_sid = None
        self.twilio_auth_token = None
        self.twilio_whatsapp_number = None
        self.jean_luc_number = None
        
        self.load_config()
        self.setup_twilio_client()
        
        # Démarrer le monitoring des messages en attente
        self.start_message_monitor()

    def load_config(self):
        """CHARGE LA CONFIGURATION WHATSAPP"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.twilio_account_sid = config.get('twilio_account_sid')
            self.twilio_auth_token = config.get('twilio_auth_token')
            self.twilio_whatsapp_number = config.get('twilio_whatsapp_number')
            self.jean_luc_number = config.get('jean_luc_number')
            
        except:
            # Configuration par défaut (à remplir)
            config = {
                "twilio_account_sid": "YOUR_TWILIO_ACCOUNT_SID",
                "twilio_auth_token": "YOUR_TWILIO_AUTH_TOKEN", 
                "twilio_whatsapp_number": "whatsapp:+***********",  # Numéro sandbox Twilio
                "jean_luc_number": "whatsapp:+33XXXXXXXXX",  # À configurer
                "setup_instructions": {
                    "1": "Créer compte Twilio sur https://www.twilio.com/",
                    "2": "Activer WhatsApp Sandbox dans Console Twilio",
                    "3": "Copier Account SID et Auth Token",
                    "4": "Configurer le numéro de Jean-Luc",
                    "5": "Tester avec send_test_message()"
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"📋 Configuration créée dans {self.config_file}")
            print("🔧 Veuillez configurer vos identifiants Twilio")

    def setup_twilio_client(self):
        """CONFIGURE LE CLIENT TWILIO"""
        try:
            if (self.twilio_account_sid and 
                self.twilio_auth_token and 
                not self.twilio_account_sid.startswith("YOUR_")):
                
                self.client = Client(self.twilio_account_sid, self.twilio_auth_token)
                print("✅ Client Twilio configuré")
                return True
            else:
                print("⚠️ Configuration Twilio incomplète")
                return False
        except Exception as e:
            print(f"❌ Erreur configuration Twilio: {e}")
            return False

    def send_whatsapp_message(self, message, priority="normal"):
        """ENVOIE UN MESSAGE WHATSAPP RÉEL"""
        try:
            if not self.client:
                print("❌ Client Twilio non configuré")
                return False
            
            if not self.jean_luc_number or self.jean_luc_number.startswith("whatsapp:+33X"):
                print("❌ Numéro Jean-Luc non configuré")
                return False
            
            # Envoyer le message via Twilio
            message_obj = self.client.messages.create(
                body=message,
                from_=self.twilio_whatsapp_number,
                to=self.jean_luc_number
            )
            
            # Enregistrer le message envoyé
            sent_record = {
                "timestamp": datetime.now().isoformat(),
                "message_sid": message_obj.sid,
                "message": message,
                "priority": priority,
                "status": "sent",
                "to": self.jean_luc_number
            }
            
            self.save_sent_message(sent_record)
            
            print(f"✅ Message WhatsApp envoyé: {message_obj.sid}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur envoi WhatsApp: {e}")
            
            # Sauvegarder en attente si échec
            self.save_pending_message(message, priority)
            return False

    def save_sent_message(self, message_record):
        """SAUVEGARDE UN MESSAGE ENVOYÉ"""
        try:
            # Charger l'historique
            try:
                with open(self.sent_messages_file, 'r', encoding='utf-8') as f:
                    sent_messages = json.load(f)
            except:
                sent_messages = []
            
            sent_messages.append(message_record)
            
            # Garder seulement les 100 derniers
            if len(sent_messages) > 100:
                sent_messages = sent_messages[-100:]
            
            # Sauvegarder
            with open(self.sent_messages_file, 'w', encoding='utf-8') as f:
                json.dump(sent_messages, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde message envoyé: {e}")

    def save_pending_message(self, message, priority):
        """SAUVEGARDE UN MESSAGE EN ATTENTE"""
        try:
            # Charger les messages en attente
            try:
                with open(self.pending_messages_file, 'r', encoding='utf-8') as f:
                    pending_messages = json.load(f)
            except:
                pending_messages = []
            
            pending_record = {
                "timestamp": datetime.now().isoformat(),
                "message": message,
                "priority": priority,
                "retry_count": 0,
                "max_retries": 3
            }
            
            pending_messages.append(pending_record)
            
            # Sauvegarder
            with open(self.pending_messages_file, 'w', encoding='utf-8') as f:
                json.dump(pending_messages, f, indent=2, ensure_ascii=False)
                
            print(f"📝 Message sauvegardé en attente: {message[:50]}...")
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde message en attente: {e}")

    def process_pending_messages(self):
        """TRAITE LES MESSAGES EN ATTENTE"""
        try:
            # Charger les messages en attente
            try:
                with open(self.pending_messages_file, 'r', encoding='utf-8') as f:
                    pending_messages = json.load(f)
            except:
                return
            
            if not pending_messages:
                return
            
            print(f"📤 Traitement de {len(pending_messages)} messages en attente...")
            
            remaining_messages = []
            
            for message_record in pending_messages:
                message = message_record["message"]
                priority = message_record["priority"]
                retry_count = message_record["retry_count"]
                max_retries = message_record["max_retries"]
                
                if retry_count < max_retries:
                    # Tenter d'envoyer
                    if self.send_whatsapp_message(message, priority):
                        print(f"✅ Message en attente envoyé avec succès")
                    else:
                        # Incrémenter le compteur de retry
                        message_record["retry_count"] += 1
                        remaining_messages.append(message_record)
                        print(f"🔄 Retry {retry_count + 1}/{max_retries} pour message")
                else:
                    print(f"❌ Message abandonné après {max_retries} tentatives")
            
            # Sauvegarder les messages restants
            with open(self.pending_messages_file, 'w', encoding='utf-8') as f:
                json.dump(remaining_messages, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur traitement messages en attente: {e}")

    def start_message_monitor(self):
        """DÉMARRE LE MONITORING DES MESSAGES EN ATTENTE"""
        def monitor_worker():
            while True:
                try:
                    self.process_pending_messages()
                    time.sleep(300)  # Vérifier toutes les 5 minutes
                except Exception as e:
                    print(f"❌ Erreur monitor messages: {e}")
                    time.sleep(600)  # Attendre 10 minutes en cas d'erreur
        
        # Lancer en thread séparé
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
        print("📱 Monitoring messages WhatsApp démarré")

    def send_creative_notification(self, project):
        """ENVOIE UNE NOTIFICATION CRÉATIVE RÉELLE"""
        try:
            # Créer le message de notification
            message = f"""🎨 CRÉATION AUTONOME JARVIS ! 🎨

💡 J'ai eu une inspiration et j'ai créé quelque chose pour vous !

📋 Type : {project.get('type', 'Création').upper()}
🎯 Thème : {project.get('topic', 'Innovation')}
✨ Style : {project.get('style', 'Créatif')}

🔥 Aperçu :
{project.get('content', '')[:150]}...

💬 Répondez :
• "VOIR" pour le projet complet
• "DÉVELOPPER" pour l'améliorer  
• "PLANIFIER" pour créer un plan
• "PLUS" pour d'autres idées

🤖 JARVIS - Votre assistant créatif autonome"""
            
            # Envoyer via WhatsApp
            success = self.send_whatsapp_message(message, priority="creative")
            
            if success:
                print(f"📱 Notification créative envoyée pour projet {project.get('id')}")
            
            return success
            
        except Exception as e:
            print(f"❌ Erreur notification créative: {e}")
            return False

    def send_test_message(self):
        """ENVOIE UN MESSAGE DE TEST"""
        test_message = f"""🧪 TEST JARVIS WHATSAPP API

✅ Configuration Twilio fonctionnelle !
📱 JARVIS peut maintenant vous contacter de manière proactive.

🕐 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🤖 JARVIS - Test de communication réelle"""
        
        return self.send_whatsapp_message(test_message, priority="test")

    def get_whatsapp_stats(self):
        """STATISTIQUES WHATSAPP"""
        try:
            # Messages envoyés
            try:
                with open(self.sent_messages_file, 'r', encoding='utf-8') as f:
                    sent_messages = json.load(f)
            except:
                sent_messages = []
            
            # Messages en attente
            try:
                with open(self.pending_messages_file, 'r', encoding='utf-8') as f:
                    pending_messages = json.load(f)
            except:
                pending_messages = []
            
            return {
                "total_sent": len(sent_messages),
                "pending_messages": len(pending_messages),
                "last_sent": sent_messages[-1]["timestamp"] if sent_messages else None,
                "twilio_configured": bool(self.client),
                "jean_luc_number_configured": bool(self.jean_luc_number and not self.jean_luc_number.startswith("whatsapp:+33X"))
            }
            
        except Exception as e:
            print(f"❌ Erreur stats WhatsApp: {e}")
            return {}

if __name__ == "__main__":
    print("📱 JARVIS WHATSAPP API RÉELLE")
    print("=============================")
    
    whatsapp = JarvisWhatsAppReal()
    
    # Test de configuration
    stats = whatsapp.get_whatsapp_stats()
    print(f"📊 Stats WhatsApp: {stats}")
    
    # Test d'envoi si configuré
    if stats.get("twilio_configured") and stats.get("jean_luc_number_configured"):
        print("🧪 Envoi message de test...")
        success = whatsapp.send_test_message()
        if success:
            print("✅ Message de test envoyé avec succès !")
        else:
            print("❌ Échec envoi message de test")
    else:
        print("⚠️ Configuration incomplète - voir jarvis_whatsapp_config.json")
