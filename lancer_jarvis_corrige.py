#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LANCEUR JARVIS CORRIGÉ
Lance JARVIS avec l'interface corrigée pour éviter les problèmes d'affichage horizontal
"""

import sys
import os
import subprocess
import time

def verifier_deepseek():
    """Vérifie si DeepSeek est en cours d'exécution"""
    try:
        import requests
        response = requests.get("http://localhost:8000/v1/models", timeout=5)
        if response.status_code == 200:
            print("✅ DeepSeek R1 8B détecté sur localhost:8000")
            return True
    except:
        pass
    
    print("❌ DeepSeek R1 8B non détecté sur localhost:8000")
    return False

def lancer_deepseek():
    """Lance DeepSeek si nécessaire"""
    if not verifier_deepseek():
        print("🚀 Lancement de DeepSeek R1 8B...")
        try:
            # Lancer le script de démarrage DeepSeek
            subprocess.Popen([
                "bash", "demarrer_deepseek_optimise.sh"
            ], cwd="/Volumes/seagate/Louna_Electron_Latest")
            
            # Attendre que DeepSeek soit prêt
            print("⏳ Attente du démarrage de DeepSeek...")
            for i in range(30):  # 30 secondes max
                time.sleep(1)
                if verifier_deepseek():
                    print("✅ DeepSeek R1 8B prêt !")
                    return True
                print(f"   Tentative {i+1}/30...")
            
            print("❌ Timeout - DeepSeek n'a pas démarré")
            return False
            
        except Exception as e:
            print(f"❌ Erreur lancement DeepSeek: {e}")
            return False
    
    return True

def lancer_jarvis():
    """Lance l'interface JARVIS corrigée"""
    try:
        print("🧠 Lancement de JARVIS avec interface corrigée...")
        
        # Importer et lancer l'interface
        from jarvis_interface_propre import create_interface
        
        demo = create_interface()
        
        print("🌟 JARVIS PRÊT !")
        print("📱 Interface: http://localhost:7863")
        print("🔧 DeepSeek: http://localhost:8000")
        print("\n💡 Corrections appliquées:")
        print("   ✅ CSS simplifié pour éviter l'affichage horizontal")
        print("   ✅ Configuration chatbot optimisée")
        print("   ✅ Thème par défaut plus stable")
        print("   ✅ Messages formatés verticalement")
        
        demo.launch(
            server_name="0.0.0.0",
            server_port=7863,
            share=False,
            quiet=False,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ Erreur lancement JARVIS: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Fonction principale"""
    print("🚀 LANCEMENT JARVIS CORRIGÉ")
    print("=" * 50)
    
    # Vérifier l'environnement
    if not os.path.exists("jarvis_interface_propre.py"):
        print("❌ Fichier jarvis_interface_propre.py non trouvé")
        return
    
    # Lancer DeepSeek si nécessaire
    if not lancer_deepseek():
        print("⚠️  DeepSeek non disponible - JARVIS fonctionnera en mode dégradé")
    
    # Lancer JARVIS
    lancer_jarvis()

if __name__ == "__main__":
    main()
