#!/usr/bin/env python3
"""
🧠💾 JARVIS T7 SYNCHRONIZATION ENGINE
Synchronisation automatique en temps réel de la mémoire thermique avec le T7

Créé pour <PERSON><PERSON> - JARVIS Vision
"""

import os
import json
import time
import shutil
import threading
import hashlib
import gzip
from datetime import datetime, timedelta
from pathlib import Path
import psutil
import subprocess

class JarvisT7SyncEngine:
    def __init__(self):
        # Configuration T7
        self.t7_mount_points = [
            "/Volumes/T7",
            "/Volumes/T7 Shield", 
            "/Volumes/Samsung_T7",
            "/media/t7",
            "/mnt/t7"
        ]
        
        self.t7_path = None
        self.jarvis_t7_path = None
        
        # Fichiers à synchroniser
        self.sync_files = {
            "thermal_memory_persistent.json": "memory/thermal_memory_persistent.json",
            "jarvis_memory_archives/": "memory/archives/",
            "jarvis_creative_projects.json": "memory/creative_projects.json",
            "jarvis_autonomous_thoughts.json": "memory/autonomous_thoughts.json",
            "jarvis_whatsapp_sent.json": "memory/whatsapp_sent.json",
            "jarvis_security_log.json": "memory/security_log.json",
            "jarvis_search_log.json": "memory/search_log.json"
        }
        
        # Configuration synchronisation
        self.sync_config = {
            "auto_sync_enabled": True,
            "sync_interval_seconds": 30,  # Sync toutes les 30 secondes
            "incremental_sync": True,     # Sync incrémentale
            "compression_enabled": True,  # Compression des gros fichiers
            "backup_versions": 5,         # Garder 5 versions
            "real_time_monitoring": True  # Monitoring temps réel
        }
        
        self.sync_stats = {
            "last_sync": None,
            "total_syncs": 0,
            "bytes_synced": 0,
            "errors": 0,
            "sync_speed_mbps": 0
        }
        
        self.file_hashes = {}  # Cache des hash pour sync incrémentale
        self.sync_thread = None
        self.monitoring_thread = None
        self.is_running = False
        
        # Initialiser
        self.detect_t7_drive()
        self.setup_t7_structure()
        
    def detect_t7_drive(self):
        """DÉTECTER AUTOMATIQUEMENT LE DISQUE T7"""
        print("🔍 Recherche du disque T7...")
        
        for mount_point in self.t7_mount_points:
            if os.path.exists(mount_point):
                # Vérifier que c'est bien un T7 (par la taille ou le nom)
                try:
                    disk_usage = psutil.disk_usage(mount_point)
                    total_gb = disk_usage.total / (1024**3)
                    
                    # T7 fait généralement 1TB, 2TB, 4TB
                    if 900 < total_gb < 4500:  # Entre 900GB et 4.5TB
                        self.t7_path = mount_point
                        self.jarvis_t7_path = os.path.join(mount_point, "JARVIS_BRAIN")
                        print(f"✅ T7 détecté: {mount_point} ({total_gb:.1f} GB)")
                        return True
                        
                except Exception as e:
                    continue
        
        print("❌ Disque T7 non trouvé")
        return False
    
    def setup_t7_structure(self):
        """CRÉER LA STRUCTURE JARVIS SUR LE T7"""
        if not self.t7_path:
            return False
            
        try:
            # Créer la structure de dossiers
            os.makedirs(self.jarvis_t7_path, exist_ok=True)
            os.makedirs(os.path.join(self.jarvis_t7_path, "memory"), exist_ok=True)
            os.makedirs(os.path.join(self.jarvis_t7_path, "memory", "archives"), exist_ok=True)
            os.makedirs(os.path.join(self.jarvis_t7_path, "memory", "backups"), exist_ok=True)
            os.makedirs(os.path.join(self.jarvis_t7_path, "logs"), exist_ok=True)
            
            # Créer fichier de configuration
            config_file = os.path.join(self.jarvis_t7_path, "jarvis_brain_config.json")
            if not os.path.exists(config_file):
                config = {
                    "created": datetime.now().isoformat(),
                    "version": "2.0",
                    "owner": "Jean-Luc Passave",
                    "description": "JARVIS Brain - Mémoire thermique évolutive",
                    "auto_sync": True,
                    "last_sync": None
                }
                
                with open(config_file, 'w') as f:
                    json.dump(config, f, indent=2)
            
            print(f"✅ Structure T7 créée: {self.jarvis_t7_path}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur création structure T7: {e}")
            return False
    
    def calculate_file_hash(self, filepath):
        """CALCULER LE HASH D'UN FICHIER POUR SYNC INCRÉMENTALE"""
        try:
            hash_md5 = hashlib.md5()
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return None
    
    def needs_sync(self, local_file, t7_file):
        """VÉRIFIER SI UN FICHIER NÉCESSITE UNE SYNCHRONISATION"""
        if not os.path.exists(local_file):
            return False
            
        if not os.path.exists(t7_file):
            return True
        
        # Comparer les hash si sync incrémentale activée
        if self.sync_config["incremental_sync"]:
            local_hash = self.calculate_file_hash(local_file)
            cached_hash = self.file_hashes.get(local_file)
            
            if local_hash != cached_hash:
                self.file_hashes[local_file] = local_hash
                return True
            
            return False
        
        # Sinon comparer les dates de modification
        local_mtime = os.path.getmtime(local_file)
        t7_mtime = os.path.getmtime(t7_file)
        
        return local_mtime > t7_mtime
    
    def sync_file(self, local_file, t7_file):
        """SYNCHRONISER UN FICHIER VERS LE T7"""
        try:
            start_time = time.time()
            
            # Créer le dossier de destination si nécessaire
            os.makedirs(os.path.dirname(t7_file), exist_ok=True)
            
            # Backup de l'ancienne version si elle existe
            if os.path.exists(t7_file) and self.sync_config["backup_versions"] > 0:
                backup_dir = os.path.join(self.jarvis_t7_path, "memory", "backups")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"{os.path.basename(t7_file)}.{timestamp}.bak"
                backup_path = os.path.join(backup_dir, backup_name)
                
                shutil.copy2(t7_file, backup_path)
                
                # Nettoyer les anciens backups
                self.cleanup_old_backups(backup_dir, os.path.basename(t7_file))
            
            # Copier le fichier
            if self.sync_config["compression_enabled"] and os.path.getsize(local_file) > 10*1024*1024:  # > 10MB
                # Compresser les gros fichiers
                with open(local_file, 'rb') as f_in:
                    with gzip.open(f"{t7_file}.gz", 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                t7_file = f"{t7_file}.gz"
            else:
                shutil.copy2(local_file, t7_file)
            
            # Statistiques
            file_size = os.path.getsize(local_file)
            sync_time = time.time() - start_time
            speed_mbps = (file_size / (1024*1024)) / max(sync_time, 0.001)
            
            self.sync_stats["bytes_synced"] += file_size
            self.sync_stats["sync_speed_mbps"] = speed_mbps
            
            print(f"✅ Sync: {os.path.basename(local_file)} ({file_size/1024:.1f} KB, {speed_mbps:.1f} MB/s)")
            return True
            
        except Exception as e:
            print(f"❌ Erreur sync {local_file}: {e}")
            self.sync_stats["errors"] += 1
            return False
    
    def cleanup_old_backups(self, backup_dir, base_filename):
        """NETTOYER LES ANCIENS BACKUPS"""
        try:
            backups = []
            for file in os.listdir(backup_dir):
                if file.startswith(base_filename) and file.endswith('.bak'):
                    filepath = os.path.join(backup_dir, file)
                    backups.append((filepath, os.path.getmtime(filepath)))
            
            # Trier par date et garder seulement les N plus récents
            backups.sort(key=lambda x: x[1], reverse=True)
            
            for filepath, _ in backups[self.sync_config["backup_versions"]:]:
                os.remove(filepath)
                
        except Exception as e:
            print(f"⚠️ Erreur nettoyage backups: {e}")
    
    def perform_sync(self):
        """EFFECTUER UNE SYNCHRONISATION COMPLÈTE"""
        if not self.t7_path:
            print("❌ T7 non disponible pour sync")
            return False
        
        print(f"🔄 Synchronisation vers T7: {self.jarvis_t7_path}")
        sync_start = time.time()
        files_synced = 0
        
        try:
            for local_path, t7_relative_path in self.sync_files.items():
                t7_full_path = os.path.join(self.jarvis_t7_path, t7_relative_path)
                
                if os.path.isdir(local_path):
                    # Synchroniser un dossier
                    for root, dirs, files in os.walk(local_path):
                        for file in files:
                            local_file = os.path.join(root, file)
                            rel_path = os.path.relpath(local_file, local_path)
                            t7_file = os.path.join(t7_full_path, rel_path)
                            
                            if self.needs_sync(local_file, t7_file):
                                if self.sync_file(local_file, t7_file):
                                    files_synced += 1
                else:
                    # Synchroniser un fichier
                    if os.path.exists(local_path) and self.needs_sync(local_path, t7_full_path):
                        if self.sync_file(local_path, t7_full_path):
                            files_synced += 1
            
            # Mettre à jour les statistiques
            sync_duration = time.time() - sync_start
            self.sync_stats["last_sync"] = datetime.now().isoformat()
            self.sync_stats["total_syncs"] += 1
            
            # Sauvegarder les stats
            stats_file = os.path.join(self.jarvis_t7_path, "logs", "sync_stats.json")
            with open(stats_file, 'w') as f:
                json.dump(self.sync_stats, f, indent=2)
            
            print(f"✅ Sync terminée: {files_synced} fichiers en {sync_duration:.1f}s")
            return True
            
        except Exception as e:
            print(f"❌ Erreur synchronisation: {e}")
            self.sync_stats["errors"] += 1
            return False
    
    def start_auto_sync(self):
        """DÉMARRER LA SYNCHRONISATION AUTOMATIQUE"""
        if self.is_running:
            return
        
        self.is_running = True
        
        def sync_worker():
            print("🚀 Synchronisation automatique T7 démarrée")
            
            while self.is_running:
                try:
                    if self.sync_config["auto_sync_enabled"]:
                        self.perform_sync()
                    
                    time.sleep(self.sync_config["sync_interval_seconds"])
                    
                except Exception as e:
                    print(f"❌ Erreur worker sync: {e}")
                    time.sleep(60)  # Attendre 1 minute en cas d'erreur
        
        self.sync_thread = threading.Thread(target=sync_worker, daemon=True)
        self.sync_thread.start()
        
        print(f"💾 Auto-sync T7 actif (intervalle: {self.sync_config['sync_interval_seconds']}s)")
    
    def stop_auto_sync(self):
        """ARRÊTER LA SYNCHRONISATION AUTOMATIQUE"""
        self.is_running = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
        print("⏹️ Synchronisation automatique arrêtée")
    
    def get_sync_status(self):
        """OBTENIR LE STATUT DE SYNCHRONISATION"""
        if not self.t7_path:
            return {
                "status": "❌ T7 non détecté",
                "t7_available": False
            }
        
        return {
            "status": "✅ T7 connecté" if self.is_running else "⏸️ Sync arrêtée",
            "t7_available": True,
            "t7_path": self.t7_path,
            "jarvis_path": self.jarvis_t7_path,
            "auto_sync": self.is_running,
            "last_sync": self.sync_stats["last_sync"],
            "total_syncs": self.sync_stats["total_syncs"],
            "bytes_synced": f"{self.sync_stats['bytes_synced'] / (1024*1024):.1f} MB",
            "errors": self.sync_stats["errors"],
            "sync_speed": f"{self.sync_stats['sync_speed_mbps']:.1f} MB/s"
        }

# Instance globale
jarvis_t7_sync = JarvisT7SyncEngine()

def start_t7_sync():
    """DÉMARRER LA SYNCHRONISATION T7"""
    return jarvis_t7_sync.start_auto_sync()

def stop_t7_sync():
    """ARRÊTER LA SYNCHRONISATION T7"""
    return jarvis_t7_sync.stop_auto_sync()

def force_t7_sync():
    """FORCER UNE SYNCHRONISATION IMMÉDIATE"""
    return jarvis_t7_sync.perform_sync()

def get_t7_status():
    """OBTENIR LE STATUT T7"""
    return jarvis_t7_sync.get_sync_status()

if __name__ == "__main__":
    # Test du module
    print("🧠💾 Test du moteur de synchronisation T7 JARVIS")
    
    # Statut
    status = get_t7_status()
    print(f"Statut: {json.dumps(status, indent=2)}")
    
    # Test de sync
    if status["t7_available"]:
        print("🔄 Test de synchronisation...")
        force_t7_sync()
        
        print("🚀 Démarrage auto-sync...")
        start_t7_sync()
        
        # Attendre un peu
        time.sleep(5)
        
        print("⏹️ Arrêt auto-sync...")
        stop_t7_sync()
    else:
        print("❌ T7 non disponible pour les tests")
