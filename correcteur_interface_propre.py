#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 CORRECTEUR INTERFACE PROPRE - JEAN-LUC PASSAVE
Réorganise l'interface comme ChatGPT avec boutons alignés
"""

import re
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🎨 [{timestamp}] {message}")

def corriger_interface_chatgpt():
    """Corrige l'interface pour ressembler à ChatGPT"""
    log("🎨 CORRECTION INTERFACE STYLE CHATGPT")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Backup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"jarvis_interface_propre_backup_interface_{timestamp}.py"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(code)
        log(f"💾 Backup: {backup_file}")
        
        # Trouver la section d'interface principale
        interface_start = code.find("with gr.Column(scale=4):")
        if interface_start == -1:
            interface_start = code.find("with gr.Row():")
        
        if interface_start == -1:
            log("❌ Section interface non trouvée")
            return False
        
        # Nouvelle interface propre style ChatGPT
        nouvelle_interface = '''
                with gr.Column(scale=4):
                    # Zone de conversation principale
                    chatbot = gr.Chatbot(
                        label="💬 Conversation avec JARVIS",
                        height=500,
                        show_label=False,
                        container=True,
                        elem_classes=["chatbot-container"]
                    )
                    
                    # Zone d'envoi style ChatGPT
                    with gr.Row(elem_classes=["input-row"]):
                        with gr.Column(scale=10):
                            # Zone de texte principale
                            user_input = gr.Textbox(
                                placeholder="💬 Tapez votre message à JARVIS...",
                                show_label=False,
                                lines=1,
                                max_lines=5,
                                container=False,
                                elem_classes=["message-input"]
                            )
                        
                        with gr.Column(scale=1, min_width=120):
                            # Boutons alignés horizontalement
                            with gr.Row():
                                # Bouton Écouter
                                listen_btn = gr.Button(
                                    "🔊",
                                    size="sm",
                                    variant="secondary",
                                    elem_classes=["control-btn"]
                                )
                                
                                # Bouton Micro
                                mic_btn = gr.Button(
                                    "🎤",
                                    size="sm", 
                                    variant="secondary",
                                    elem_classes=["control-btn"]
                                )
                                
                                # Bouton Caméra
                                camera_btn = gr.Button(
                                    "📷",
                                    size="sm",
                                    variant="secondary", 
                                    elem_classes=["control-btn"]
                                )
                                
                                # Bouton Envoi
                                send_btn = gr.Button(
                                    "➤",
                                    size="sm",
                                    variant="primary",
                                    elem_classes=["send-btn"]
                                )
                    
                    # Zone de statut discrète
                    with gr.Row():
                        status_display = gr.HTML(
                            value="🟢 JARVIS prêt",
                            elem_classes=["status-bar"]
                        )
        '''
        
        # CSS pour style ChatGPT
        nouveau_css = '''
        custom_css = """
        /* Style ChatGPT pour JARVIS */
        .chatbot-container {
            border-radius: 12px !important;
            border: 1px solid #e5e5e5 !important;
            background: white !important;
        }
        
        .input-row {
            background: #f8f9fa !important;
            border-radius: 12px !important;
            padding: 8px !important;
            margin-top: 10px !important;
            border: 1px solid #e5e5e5 !important;
        }
        
        .message-input textarea {
            border: none !important;
            background: transparent !important;
            resize: none !important;
            font-size: 16px !important;
            line-height: 1.5 !important;
            padding: 8px 12px !important;
        }
        
        .message-input textarea:focus {
            outline: none !important;
            box-shadow: none !important;
        }
        
        .control-btn {
            width: 32px !important;
            height: 32px !important;
            min-width: 32px !important;
            border-radius: 8px !important;
            margin: 0 2px !important;
            background: #f1f3f4 !important;
            border: 1px solid #dadce0 !important;
            font-size: 14px !important;
        }
        
        .control-btn:hover {
            background: #e8eaed !important;
            transform: scale(1.05) !important;
        }
        
        .send-btn {
            width: 32px !important;
            height: 32px !important;
            min-width: 32px !important;
            border-radius: 8px !important;
            margin: 0 2px !important;
            background: #1976d2 !important;
            border: none !important;
            color: white !important;
            font-size: 16px !important;
            font-weight: bold !important;
        }
        
        .send-btn:hover {
            background: #1565c0 !important;
            transform: scale(1.05) !important;
        }
        
        .status-bar {
            font-size: 12px !important;
            color: #666 !important;
            text-align: center !important;
            margin-top: 5px !important;
        }
        
        /* Animation des boutons */
        .control-btn, .send-btn {
            transition: all 0.2s ease !important;
        }
        
        /* Style responsive */
        @media (max-width: 768px) {
            .control-btn, .send-btn {
                width: 28px !important;
                height: 28px !important;
                font-size: 12px !important;
            }
        }
        """
        '''
        
        # Remplacer l'ancien CSS
        css_pattern = r'custom_css = """.*?"""'
        if re.search(css_pattern, code, re.DOTALL):
            code = re.sub(css_pattern, nouveau_css.strip(), code, flags=re.DOTALL)
            log("✅ CSS remplacé")
        else:
            # Ajouter le CSS au début
            code = nouveau_css + "\n\n" + code
            log("✅ CSS ajouté")
        
        # Trouver et remplacer la section interface
        # Chercher la section chatbot actuelle
        chatbot_pattern = r'chatbot = gr\.Chatbot\([^)]*\)'
        if re.search(chatbot_pattern, code):
            # Remplacer toute la section d'interface
            interface_pattern = r'with gr\.Column\(scale=4\):.*?(?=with gr\.Column\(scale=2\)|$)'
            
            if re.search(interface_pattern, code, re.DOTALL):
                code = re.sub(interface_pattern, nouvelle_interface.strip(), code, flags=re.DOTALL)
                log("✅ Interface remplacée")
            else:
                log("⚠️  Pattern interface non trouvé - ajout manuel nécessaire")
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log("✅ Interface corrigée style ChatGPT")
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction interface: {e}")
        return False

def ajouter_fonctions_boutons():
    """Ajoute les fonctions pour les nouveaux boutons"""
    log("🔧 AJOUT FONCTIONS BOUTONS")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Fonctions pour les boutons
        nouvelles_fonctions = '''
def toggle_audio_playback():
    """Active/désactive la lecture audio"""
    try:
        # Logique de lecture audio
        return "🔊 Lecture audio activée"
    except Exception as e:
        return f"❌ Erreur audio: {e}"

def toggle_microphone():
    """Active/désactive le microphone"""
    try:
        # Logique microphone
        return "🎤 Microphone activé"
    except Exception as e:
        return f"❌ Erreur micro: {e}"

def toggle_camera():
    """Active/désactive la caméra"""
    try:
        # Logique caméra
        return "📷 Caméra activée"
    except Exception as e:
        return f"❌ Erreur caméra: {e}"
'''
        
        # Ajouter les fonctions avant la fonction principale
        main_function_pos = code.find("def create_interface():")
        if main_function_pos != -1:
            code = code[:main_function_pos] + nouvelles_fonctions + "\n" + code[main_function_pos:]
            log("✅ Fonctions boutons ajoutées")
        
        # Ajouter les événements des boutons
        events_code = '''
        # Événements des boutons de contrôle
        listen_btn.click(
            fn=toggle_audio_playback,
            outputs=[status_display]
        )
        
        mic_btn.click(
            fn=toggle_microphone,
            outputs=[status_display]
        )
        
        camera_btn.click(
            fn=toggle_camera,
            outputs=[status_display]
        )
        
        send_btn.click(
            fn=send_message,
            inputs=[user_input, chatbot],
            outputs=[chatbot, user_input, status_display]
        )
        
        user_input.submit(
            fn=send_message,
            inputs=[user_input, chatbot],
            outputs=[chatbot, user_input, status_display]
        )
'''
        
        # Ajouter les événements avant demo.launch
        launch_pos = code.find("demo.launch(")
        if launch_pos != -1:
            code = code[:launch_pos] + events_code + "\n    " + code[launch_pos:]
            log("✅ Événements boutons ajoutés")
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur ajout fonctions: {e}")
        return False

def verifier_interface():
    """Vérifie que l'interface est correcte"""
    log("🧪 VÉRIFICATION INTERFACE")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        verifications = []
        
        # Vérifier CSS
        if 'chatbot-container' in code and 'input-row' in code:
            verifications.append("✅ CSS style ChatGPT présent")
        else:
            verifications.append("❌ CSS manquant")
        
        # Vérifier boutons
        boutons_requis = ['listen_btn', 'mic_btn', 'camera_btn', 'send_btn']
        boutons_presents = sum(1 for btn in boutons_requis if btn in code)
        
        if boutons_presents == 4:
            verifications.append("✅ Tous les boutons présents")
        else:
            verifications.append(f"❌ {boutons_presents}/4 boutons présents")
        
        # Vérifier fonctions
        fonctions_requises = ['toggle_audio_playback', 'toggle_microphone', 'toggle_camera']
        fonctions_presentes = sum(1 for func in fonctions_requises if func in code)
        
        if fonctions_presentes == 3:
            verifications.append("✅ Toutes les fonctions présentes")
        else:
            verifications.append(f"❌ {fonctions_presentes}/3 fonctions présentes")
        
        for verif in verifications:
            print(f"   {verif}")
        
        return all("✅" in v for v in verifications)
        
    except Exception as e:
        log(f"❌ Erreur vérification: {e}")
        return False

def correction_complete_interface():
    """Correction complète de l'interface"""
    log("🎨 CORRECTION COMPLÈTE INTERFACE CHATGPT")
    print("=" * 60)
    
    corrections_reussies = 0
    
    # 1. Corriger interface
    log("ÉTAPE 1: Correction interface ChatGPT")
    if corriger_interface_chatgpt():
        corrections_reussies += 1
    
    # 2. Ajouter fonctions
    log("ÉTAPE 2: Ajout fonctions boutons")
    if ajouter_fonctions_boutons():
        corrections_reussies += 1
    
    # 3. Vérifier
    log("ÉTAPE 3: Vérification interface")
    if verifier_interface():
        corrections_reussies += 1
    
    # Résultat
    print("\n" + "=" * 60)
    log("📊 RÉSULTAT CORRECTION INTERFACE")
    print("=" * 60)
    
    print(f"✅ Corrections réussies: {corrections_reussies}/3")
    
    if corrections_reussies >= 2:
        print("🎉 INTERFACE CORRIGÉE STYLE CHATGPT !")
        print("🎨 Boutons Écouter, Micro, Caméra alignés")
        print("🚀 Redémarrez JARVIS pour voir les changements")
        return True
    else:
        print("❌ CORRECTIONS INCOMPLÈTES")
        return False

if __name__ == "__main__":
    print("🎨 CORRECTEUR INTERFACE PROPRE")
    print("Réorganise comme ChatGPT avec boutons alignés")
    print("=" * 50)
    
    if correction_complete_interface():
        print("\n🎉 INTERFACE CORRIGÉE !")
        print("Redémarrez JARVIS pour voir le nouveau style")
    else:
        print("\n❌ CORRECTION INCOMPLÈTE")
        print("Vérification manuelle nécessaire")
