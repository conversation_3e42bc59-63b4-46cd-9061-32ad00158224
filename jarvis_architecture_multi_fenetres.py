#!/usr/bin/env python3
"""
🏗️ ARCHITECTURE MULTI-FENÊTRES JARVIS
Interface organisée et professionnelle pour Jean-Luc Passave
Chaque fonction dans sa propre fenêtre
"""

import gradio as gr
import webbrowser
import threading
import time
from datetime import datetime
import json
import os

# ============================================================================
# CONFIGURATION GLOBALE
# ============================================================================

JARVIS_CONFIG = {
    "main_port": 7867,
    "code_port": 7868,
    "thoughts_port": 7869,
    "config_port": 7870,
    "whatsapp_port": 7871,
    "security_port": 7872,
    "monitoring_port": 7873,
    "memory_port": 7874
}

# ============================================================================
# FENÊTRE PRINCIPALE - DASHBOARD CENTRAL
# ============================================================================

def create_main_dashboard():
    """Crée le dashboard principal avec navigation vers les autres fenêtres"""
    
    with gr.Blocks(
        title="🤖 JARVIS - Dashboard Principal",
        theme=gr.themes.Soft(),
        css="""
        .main-container { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .window-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        .window-card:hover {
            transform: translateY(-5px);
        }
        .launch-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        """
    ) as main_interface:
        
        gr.HTML("""
        <div class="main-container">
            <div style="text-align: center; color: white; margin-bottom: 30px;">
                <h1 style="font-size: 3em; margin: 0;">🤖 JARVIS</h1>
                <h2 style="font-size: 1.5em; margin: 10px 0;">Dashboard Principal</h2>
                <p style="font-size: 1.1em; opacity: 0.9;">Interface Multi-Fenêtres Professionnelle</p>
            </div>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("""
                <div class="window-card">
                    <h3>💻 ÉDITEUR DE CODE</h3>
                    <p>Interface dédiée pour écrire et exécuter du code dans tous les langages</p>
                </div>
                """)
                launch_code_btn = gr.Button("🚀 Ouvrir Éditeur Code", elem_classes=["launch-btn"])
                
                gr.HTML("""
                <div class="window-card">
                    <h3>🧠 PENSÉES JARVIS</h3>
                    <p>Visualisation en temps réel des processus cognitifs de JARVIS</p>
                </div>
                """)
                launch_thoughts_btn = gr.Button("🚀 Ouvrir Pensées", elem_classes=["launch-btn"])
            
            with gr.Column(scale=1):
                gr.HTML("""
                <div class="window-card">
                    <h3>⚙️ CONFIGURATION</h3>
                    <p>Paramètres, options et personnalisation de JARVIS</p>
                </div>
                """)
                launch_config_btn = gr.Button("🚀 Ouvrir Configuration", elem_classes=["launch-btn"])
                
                gr.HTML("""
                <div class="window-card">
                    <h3>📱 WHATSAPP</h3>
                    <p>Interface de communication WhatsApp intégrée</p>
                </div>
                """)
                launch_whatsapp_btn = gr.Button("🚀 Ouvrir WhatsApp", elem_classes=["launch-btn"])
            
            with gr.Column(scale=1):
                gr.HTML("""
                <div class="window-card">
                    <h3>🔐 SÉCURITÉ</h3>
                    <p>Biométrie, VPN et systèmes de sécurité avancés</p>
                </div>
                """)
                launch_security_btn = gr.Button("🚀 Ouvrir Sécurité", elem_classes=["launch-btn"])
                
                gr.HTML("""
                <div class="window-card">
                    <h3>📊 MONITORING</h3>
                    <p>Surveillance 24h/24 et suivi des performances</p>
                </div>
                """)
                launch_monitoring_btn = gr.Button("🚀 Ouvrir Monitoring", elem_classes=["launch-btn"])
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("""
                <div class="window-card">
                    <h3>💾 MÉMOIRE THERMIQUE</h3>
                    <p>Gestion avancée de la mémoire persistante de JARVIS</p>
                </div>
                """)
                launch_memory_btn = gr.Button("🚀 Ouvrir Mémoire", elem_classes=["launch-btn"])
            
            with gr.Column(scale=1):
                gr.HTML("""
                <div class="window-card">
                    <h3>🎯 STATUT SYSTÈME</h3>
                    <p>Vue d'ensemble de tous les composants JARVIS</p>
                </div>
                """)
                system_status = gr.HTML(get_system_status())
        
        # Connexions des boutons pour ouvrir les fenêtres
        launch_code_btn.click(
            fn=lambda: open_window("code"),
            outputs=[]
        )
        
        launch_thoughts_btn.click(
            fn=lambda: open_window("thoughts"),
            outputs=[]
        )
        
        launch_config_btn.click(
            fn=lambda: open_window("config"),
            outputs=[]
        )
        
        launch_whatsapp_btn.click(
            fn=lambda: open_window("whatsapp"),
            outputs=[]
        )
        
        launch_security_btn.click(
            fn=lambda: open_window("security"),
            outputs=[]
        )
        
        launch_monitoring_btn.click(
            fn=lambda: open_window("monitoring"),
            outputs=[]
        )
        
        launch_memory_btn.click(
            fn=lambda: open_window("memory"),
            outputs=[]
        )
    
    return main_interface

def get_system_status():
    """Retourne le statut système en HTML"""
    return """
    <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; border-left: 4px solid #4CAF50;">
        <h4 style="color: #2e7d32; margin: 0 0 10px 0;">✅ SYSTÈME OPÉRATIONNEL</h4>
        <p style="margin: 5px 0; color: #333;">🧠 Neural Engine M4: Actif</p>
        <p style="margin: 5px 0; color: #333;">💾 Mémoire Thermique: 156 entrées</p>
        <p style="margin: 5px 0; color: #333;">🔗 Connexions: 172 actives</p>
        <p style="margin: 5px 0; color: #333;">⚡ Accélérateurs: Tous opérationnels</p>
        <p style="margin: 5px 0; color: #333;">💾 T7 Sync: Auto 30s</p>
    </div>
    """

def open_window(window_type):
    """Ouvre une nouvelle fenêtre selon le type"""
    ports = {
        "code": JARVIS_CONFIG["code_port"],
        "thoughts": JARVIS_CONFIG["thoughts_port"],
        "config": JARVIS_CONFIG["config_port"],
        "whatsapp": JARVIS_CONFIG["whatsapp_port"],
        "security": JARVIS_CONFIG["security_port"],
        "monitoring": JARVIS_CONFIG["monitoring_port"],
        "memory": JARVIS_CONFIG["memory_port"]
    }
    
    if window_type in ports:
        url = f"http://localhost:{ports[window_type]}"
        webbrowser.open(url)
        return f"🚀 Ouverture de la fenêtre {window_type.upper()} sur {url}"
    
    return "❌ Type de fenêtre non reconnu"

# ============================================================================
# FENÊTRE ÉDITEUR DE CODE
# ============================================================================

def create_code_editor():
    """Crée l'interface dédiée à l'édition et exécution de code"""
    
    with gr.Blocks(
        title="💻 JARVIS - Éditeur de Code",
        theme=gr.themes.Monochrome()
    ) as code_interface:
        
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #2196F3, #21CBF3); color: white; padding: 20px; margin: -20px -20px 20px -20px;">
            <h1>💻 ÉDITEUR DE CODE JARVIS</h1>
            <p>Interface dédiée pour l'écriture et l'exécution de code</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                language_selector = gr.Dropdown(
                    choices=[
                        "python", "javascript", "html", "css", "bash", "sql", 
                        "json", "yaml", "java", "cpp", "c", "go", "rust", 
                        "php", "ruby", "swift", "kotlin", "typescript", "r"
                    ],
                    value="python",
                    label="🔧 Langage de Programmation"
                )
            
            with gr.Column(scale=2):
                with gr.Row():
                    execute_btn = gr.Button("▶️ Exécuter", variant="primary")
                    save_btn = gr.Button("💾 Sauvegarder", variant="secondary")
                    clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
                    format_btn = gr.Button("🎨 Formater", variant="secondary")
        
        code_editor = gr.Code(
            label="💻 Éditeur de Code",
            language="python",
            value="# Bienvenue dans l'éditeur JARVIS\nprint('Hello JARVIS!')\n2 + 2",
            lines=20
        )
        
        code_output = gr.HTML(
            label="📤 Résultat d'Exécution",
            value="<div style='padding: 10px; background: #f5f5f5; border-radius: 5px;'>Prêt à exécuter du code...</div>"
        )
        
        # Connexions (à implémenter avec les vraies fonctions)
        execute_btn.click(
            fn=lambda code, lang: f"<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>✅ Code {lang} exécuté avec succès!</div>",
            inputs=[code_editor, language_selector],
            outputs=[code_output]
        )
    
    return code_interface

# ============================================================================
# FENÊTRE PENSÉES JARVIS
# ============================================================================

def create_thoughts_viewer():
    """Crée l'interface pour visualiser les pensées de JARVIS"""
    
    with gr.Blocks(
        title="🧠 JARVIS - Pensées Cognitives",
        theme=gr.themes.Soft()
    ) as thoughts_interface:
        
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #9C27B0, #E91E63); color: white; padding: 20px; margin: -20px -20px 20px -20px;">
            <h1>🧠 PENSÉES COGNITIVES JARVIS</h1>
            <p>Visualisation en temps réel des processus mentaux de l'IA</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>🔍 Processus Actuel</h3>")
                current_thought = gr.HTML(
                    value="<div style='background: #f0f8ff; padding: 15px; border-radius: 10px;'>💭 JARVIS réfléchit...</div>"
                )
                
                gr.HTML("<h3>🧠 Étages Neuronaux</h3>")
                neural_stages = gr.HTML(
                    value="""
                    <div style='background: #fff; padding: 10px; border-radius: 5px; border-left: 4px solid #4CAF50;'>
                        <p><strong>Étage 0:</strong> 2048 neurones (Perception) ✅</p>
                        <p><strong>Étage 1:</strong> 1024 neurones (Traitement) ✅</p>
                        <p><strong>Étage 2:</strong> 512 neurones (Mémoire CT) ✅</p>
                        <p><strong>Étage 3:</strong> 256 neurones (Analyse) ✅</p>
                        <p><strong>Étage 4:</strong> 128 neurones (Décision) ✅</p>
                        <p><strong>Étage 5:</strong> 64 neurones (Génération) ✅</p>
                        <p><strong>Étage 6:</strong> 32 neurones (Contrôle) ✅</p>
                    </div>
                    """
                )
            
            with gr.Column(scale=2):
                gr.HTML("<h3>💭 Flux de Pensées</h3>")
                thoughts_stream = gr.Chatbot(
                    value=[
                        ("🧠 JARVIS", "Initialisation des processus cognitifs..."),
                        ("🔍 Analyse", "Traitement de la demande utilisateur en cours..."),
                        ("💡 Réflexion", "Génération de solutions optimales...")
                    ],
                    height=400
                )
                
                with gr.Row():
                    pause_thoughts_btn = gr.Button("⏸️ Pause", variant="secondary")
                    clear_thoughts_btn = gr.Button("🗑️ Effacer", variant="secondary")
                    export_thoughts_btn = gr.Button("📥 Exporter", variant="primary")
    
    return thoughts_interface

# ============================================================================
# FENÊTRE CONFIGURATION
# ============================================================================

def create_config_panel():
    """Crée l'interface de configuration JARVIS"""

    with gr.Blocks(
        title="⚙️ JARVIS - Configuration",
        theme=gr.themes.Base()
    ) as config_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #FF9800, #FF5722); color: white; padding: 20px; margin: -20px -20px 20px -20px;">
            <h1>⚙️ CONFIGURATION JARVIS</h1>
            <p>Paramètres et personnalisation de votre assistant IA</p>
        </div>
        """)

        with gr.Tabs():
            with gr.Tab("🎯 Général"):
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🤖 Paramètres IA</h3>")
                        temperature = gr.Slider(0.1, 2.0, value=0.7, label="🌡️ Température")
                        max_tokens = gr.Slider(100, 4000, value=800, label="📝 Tokens Max")
                        model_select = gr.Dropdown(
                            choices=["DeepSeek R1 8B", "GPT-4", "Claude"],
                            value="DeepSeek R1 8B",
                            label="🧠 Modèle IA"
                        )

                    with gr.Column():
                        gr.HTML("<h3>🎨 Interface</h3>")
                        theme_select = gr.Dropdown(
                            choices=["Sombre", "Clair", "Auto"],
                            value="Auto",
                            label="🎨 Thème"
                        )
                        language_ui = gr.Dropdown(
                            choices=["Français", "English"],
                            value="Français",
                            label="🌍 Langue"
                        )
                        auto_save = gr.Checkbox(value=True, label="💾 Sauvegarde Auto")

            with gr.Tab("🔐 Sécurité"):
                gr.HTML("<h3>🛡️ Paramètres de Sécurité</h3>")
                biometric_enabled = gr.Checkbox(value=True, label="👤 Authentification Biométrique")
                vpn_auto = gr.Checkbox(value=True, label="🔐 VPN Automatique")
                encryption_level = gr.Dropdown(
                    choices=["Standard", "Élevé", "Maximum"],
                    value="Élevé",
                    label="🔒 Niveau Chiffrement"
                )

            with gr.Tab("💾 Mémoire"):
                gr.HTML("<h3>🧠 Gestion Mémoire Thermique</h3>")
                memory_size = gr.Slider(100, 10000, value=1000, label="📊 Taille Mémoire (MB)")
                compression_level = gr.Slider(1, 10, value=7, label="🗜️ Compression")
                auto_cleanup = gr.Checkbox(value=True, label="🧹 Nettoyage Auto")

        save_config_btn = gr.Button("💾 Sauvegarder Configuration", variant="primary")
        config_status = gr.HTML("<div style='padding: 10px;'>Prêt à configurer...</div>")

        save_config_btn.click(
            fn=lambda: "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>✅ Configuration sauvegardée!</div>",
            outputs=[config_status]
        )

    return config_interface

# ============================================================================
# FENÊTRE WHATSAPP
# ============================================================================

def create_whatsapp_interface():
    """Crée l'interface WhatsApp dédiée"""

    with gr.Blocks(
        title="📱 JARVIS - WhatsApp",
        theme=gr.themes.Soft()
    ) as whatsapp_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #25D366, #128C7E); color: white; padding: 20px; margin: -20px -20px 20px -20px;">
            <h1>📱 WHATSAPP JARVIS</h1>
            <p>Interface de communication WhatsApp intégrée</p>
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>📊 Statut WhatsApp</h3>")
                whatsapp_status = gr.HTML("""
                <div style='background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #25D366;'>
                    <h4 style='color: #128C7E; margin: 0 0 10px 0;'>✅ CONNECTÉ</h4>
                    <p style='margin: 5px 0;'>📱 Numéro: +33 1 23 45 67 89</p>
                    <p style='margin: 5px 0;'>🔗 Statut: En ligne</p>
                    <p style='margin: 5px 0;'>📨 Messages: 12 non lus</p>
                </div>
                """)

                gr.HTML("<h3>⚙️ Actions Rapides</h3>")
                with gr.Column():
                    connect_whatsapp_btn = gr.Button("🔗 Connecter WhatsApp", variant="primary")
                    scan_qr_btn = gr.Button("📷 Scanner QR Code", variant="secondary")
                    send_test_btn = gr.Button("📤 Envoyer Test", variant="secondary")
                    disconnect_btn = gr.Button("🔌 Déconnecter", variant="stop")

            with gr.Column(scale=2):
                gr.HTML("<h3>💬 Conversations</h3>")
                whatsapp_chat = gr.Chatbot(
                    value=[
                        ("Jean-Luc", "Salut JARVIS, comment ça va ?"),
                        ("JARVIS", "🤖 Bonjour Jean-Luc ! Tout va bien, je suis opérationnel à 100% !"),
                        ("Jean-Luc", "Parfait ! Peux-tu me rappeler mon RDV de demain ?"),
                        ("JARVIS", "📅 Demain à 14h30 : Réunion client chez M. Dupont")
                    ],
                    height=400
                )

                with gr.Row():
                    message_input = gr.Textbox(
                        placeholder="Tapez votre message...",
                        label="💬 Nouveau Message",
                        scale=4
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)

        # Connexions
        connect_whatsapp_btn.click(
            fn=lambda: """
            <div style='background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #25D366;'>
                <h4 style='color: #128C7E; margin: 0 0 10px 0;'>✅ CONNEXION RÉUSSIE</h4>
                <p style='margin: 5px 0;'>📱 WhatsApp Web connecté</p>
                <p style='margin: 5px 0;'>🔗 Session active</p>
                <p style='margin: 5px 0;'>📨 Prêt à recevoir/envoyer</p>
            </div>
            """,
            outputs=[whatsapp_status]
        )

    return whatsapp_interface

# ============================================================================
# FENÊTRE MONITORING
# ============================================================================

def create_monitoring_dashboard():
    """Crée l'interface de monitoring 24h/24"""

    with gr.Blocks(
        title="📊 JARVIS - Monitoring 24h/24",
        theme=gr.themes.Monochrome()
    ) as monitoring_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #1976D2, #1565C0); color: white; padding: 20px; margin: -20px -20px 20px -20px;">
            <h1>📊 MONITORING JARVIS 24H/24</h1>
            <p>Surveillance continue et suivi des performances</p>
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>⚡ Performances Temps Réel</h3>")
                performance_metrics = gr.HTML("""
                <div style='background: #f5f5f5; padding: 15px; border-radius: 10px;'>
                    <div style='margin: 10px 0; padding: 10px; background: white; border-radius: 5px;'>
                        <strong>🧠 CPU:</strong> 23% <div style='background: #e0e0e0; height: 8px; border-radius: 4px;'><div style='background: #4CAF50; height: 8px; width: 23%; border-radius: 4px;'></div></div>
                    </div>
                    <div style='margin: 10px 0; padding: 10px; background: white; border-radius: 5px;'>
                        <strong>💾 RAM:</strong> 67% <div style='background: #e0e0e0; height: 8px; border-radius: 4px;'><div style='background: #FF9800; height: 8px; width: 67%; border-radius: 4px;'></div></div>
                    </div>
                    <div style='margin: 10px 0; padding: 10px; background: white; border-radius: 5px;'>
                        <strong>🌐 Réseau:</strong> 12 Mbps <div style='background: #e0e0e0; height: 8px; border-radius: 4px;'><div style='background: #2196F3; height: 8px; width: 45%; border-radius: 4px;'></div></div>
                    </div>
                </div>
                """)

                gr.HTML("<h3>🔄 Activités Récentes</h3>")
                recent_activities = gr.HTML("""
                <div style='background: white; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>
                    <p><strong>15:42</strong> - 💻 Exécution code Python réussie</p>
                    <p><strong>15:41</strong> - 📱 Message WhatsApp envoyé</p>
                    <p><strong>15:40</strong> - 🧠 Mise à jour mémoire thermique</p>
                    <p><strong>15:39</strong> - 💾 Synchronisation T7 terminée</p>
                    <p><strong>15:38</strong> - 🔐 Authentification biométrique OK</p>
                </div>
                """)

            with gr.Column(scale=2):
                gr.HTML("<h3>📈 Graphiques de Performance</h3>")
                performance_chart = gr.HTML("""
                <div style='background: white; padding: 20px; border-radius: 10px; text-align: center;'>
                    <h4>📊 Utilisation Système (24h)</h4>
                    <div style='height: 200px; background: linear-gradient(to right, #4CAF50 0%, #FF9800 50%, #F44336 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;'>
                        Graphique de performance en temps réel
                    </div>
                </div>
                """)

                gr.HTML("<h3>🎯 Objectifs et KPI</h3>")
                kpi_dashboard = gr.HTML("""
                <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 15px;'>
                    <div style='background: white; padding: 15px; border-radius: 10px; border-left: 4px solid #4CAF50;'>
                        <h4 style='margin: 0; color: #4CAF50;'>✅ Disponibilité</h4>
                        <p style='font-size: 24px; margin: 5px 0; font-weight: bold;'>99.8%</p>
                        <p style='margin: 0; color: #666;'>Objectif: 99.5%</p>
                    </div>
                    <div style='background: white; padding: 15px; border-radius: 10px; border-left: 4px solid #2196F3;'>
                        <h4 style='margin: 0; color: #2196F3;'>⚡ Temps Réponse</h4>
                        <p style='font-size: 24px; margin: 5px 0; font-weight: bold;'>1.2s</p>
                        <p style='margin: 0; color: #666;'>Objectif: <2s</p>
                    </div>
                    <div style='background: white; padding: 15px; border-radius: 10px; border-left: 4px solid #FF9800;'>
                        <h4 style='margin: 0; color: #FF9800;'>🧠 Précision IA</h4>
                        <p style='font-size: 24px; margin: 5px 0; font-weight: bold;'>94.7%</p>
                        <p style='margin: 0; color: #666;'>Objectif: 90%</p>
                    </div>
                    <div style='background: white; padding: 15px; border-radius: 10px; border-left: 4px solid #9C27B0;'>
                        <h4 style='margin: 0; color: #9C27B0;'>💾 Sync T7</h4>
                        <p style='font-size: 24px; margin: 5px 0; font-weight: bold;'>100%</p>
                        <p style='margin: 0; color: #666;'>Dernière: Il y a 12s</p>
                    </div>
                </div>
                """)

        with gr.Row():
            refresh_monitoring_btn = gr.Button("🔄 Actualiser", variant="primary")
            export_report_btn = gr.Button("📥 Exporter Rapport", variant="secondary")
            alert_settings_btn = gr.Button("🔔 Alertes", variant="secondary")

    return monitoring_interface

# ============================================================================
# LANCEUR PRINCIPAL
# ============================================================================

def launch_all_windows():
    """Lance toutes les fenêtres JARVIS"""

    print("🚀 ================================")
    print("🤖 LANCEMENT ARCHITECTURE MULTI-FENÊTRES JARVIS")
    print("🚀 ================================")

    # Créer toutes les interfaces
    main_dashboard = create_main_dashboard()
    code_editor = create_code_editor()
    thoughts_viewer = create_thoughts_viewer()
    config_panel = create_config_panel()
    whatsapp_interface = create_whatsapp_interface()
    monitoring_dashboard = create_monitoring_dashboard()

    # Lancer les serveurs en parallèle
    def launch_server(interface, port, name):
        print(f"🌐 Lancement {name} sur port {port}")
        try:
            interface.launch(
                server_name="0.0.0.0",
                server_port=port,
                share=False,
                show_error=True,
                quiet=True
            )
        except Exception as e:
            print(f"❌ Erreur lancement {name}: {e}")

    # Threads pour chaque interface
    interfaces = [
        (main_dashboard, JARVIS_CONFIG["main_port"], "Dashboard Principal"),
        (code_editor, JARVIS_CONFIG["code_port"], "Éditeur Code"),
        (thoughts_viewer, JARVIS_CONFIG["thoughts_port"], "Pensées JARVIS"),
        (config_panel, JARVIS_CONFIG["config_port"], "Configuration"),
        (whatsapp_interface, JARVIS_CONFIG["whatsapp_port"], "WhatsApp"),
        (monitoring_dashboard, JARVIS_CONFIG["monitoring_port"], "Monitoring")
    ]

    threads = []
    for interface, port, name in interfaces:
        thread = threading.Thread(target=launch_server, args=(interface, port, name))
        thread.daemon = True
        threads.append(thread)

    # Démarrer tous les threads
    for thread in threads:
        thread.start()
        time.sleep(0.5)  # Délai entre les lancements

    print("\n🎉 TOUTES LES FENÊTRES JARVIS SONT LANCÉES !")
    print("=" * 60)
    print(f"🏠 Dashboard Principal: http://localhost:{JARVIS_CONFIG['main_port']}")
    print(f"💻 Éditeur Code: http://localhost:{JARVIS_CONFIG['code_port']}")
    print(f"🧠 Pensées JARVIS: http://localhost:{JARVIS_CONFIG['thoughts_port']}")
    print(f"⚙️ Configuration: http://localhost:{JARVIS_CONFIG['config_port']}")
    print(f"📱 WhatsApp: http://localhost:{JARVIS_CONFIG['whatsapp_port']}")
    print(f"📊 Monitoring: http://localhost:{JARVIS_CONFIG['monitoring_port']}")
    print("=" * 60)
    print("\n🔄 Appuyez sur Ctrl+C pour arrêter tous les serveurs")

    # Ouvrir automatiquement le dashboard principal
    time.sleep(3)
    webbrowser.open(f"http://localhost:{JARVIS_CONFIG['main_port']}")

    # Garder le programme en vie
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Arrêt de tous les serveurs JARVIS...")
        print("✅ Architecture multi-fenêtres fermée proprement")

if __name__ == "__main__":
    launch_all_windows()
