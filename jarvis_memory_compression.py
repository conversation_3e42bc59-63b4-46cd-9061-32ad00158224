#!/usr/bin/env python3
"""
💾 JARVIS MEMORY COMPRESSION
Système de compression intelligente de la mémoire thermique
Archivage automatique avec résumés et optimisation de performance

Créé pour Jean-Luc Passave
"""

import json
import time
import requests
from datetime import datetime, timedelta
import os
import threading
import gzip
import shutil

class JarvisMemoryCompression:
    def __init__(self):
        self.thermal_memory_file = "thermal_memory_persistent.json"
        self.compressed_archive_dir = "jarvis_memory_archives"
        self.memory_summaries_file = "jarvis_memory_summaries.json"
        self.compression_config_file = "jarvis_compression_config.json"
        
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        
        # Configuration par défaut
        self.compression_config = {
            "max_conversations_in_memory": 100,  # Nombre max de conversations en mémoire active
            "archive_after_days": 30,  # Archiver après 30 jours
            "compression_interval_hours": 24,  # Compression toutes les 24h
            "summary_length_target": 200,  # Longueur cible des résumés
            "keep_important_conversations": True,  # Garder les conversations importantes
            "importance_threshold": 0.7  # Seuil d'importance
        }
        
        self.load_compression_config()
        self.ensure_archive_directory()
        self.start_compression_daemon()

    def load_compression_config(self):
        """CHARGE LA CONFIGURATION DE COMPRESSION"""
        try:
            with open(self.compression_config_file, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
                self.compression_config.update(loaded_config)
        except:
            # Sauvegarder la configuration par défaut
            with open(self.compression_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.compression_config, f, indent=2, ensure_ascii=False)

    def ensure_archive_directory(self):
        """ASSURE QUE LE RÉPERTOIRE D'ARCHIVES EXISTE"""
        if not os.path.exists(self.compressed_archive_dir):
            os.makedirs(self.compressed_archive_dir)
            print(f"📁 Répertoire d'archives créé: {self.compressed_archive_dir}")

    def load_thermal_memory(self):
        """CHARGE LA MÉMOIRE THERMIQUE"""
        try:
            with open(self.thermal_memory_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {"conversations": [], "zones": {}}

    def save_thermal_memory(self, memory_data):
        """SAUVEGARDE LA MÉMOIRE THERMIQUE"""
        try:
            with open(self.thermal_memory_file, 'w', encoding='utf-8') as f:
                json.dump(memory_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde mémoire: {e}")

    def calculate_conversation_importance(self, conversation):
        """CALCULE L'IMPORTANCE D'UNE CONVERSATION"""
        try:
            importance_score = 0.0
            
            # Facteurs d'importance
            messages = conversation.get("messages", [])
            
            # Longueur de la conversation
            if len(messages) > 10:
                importance_score += 0.2
            elif len(messages) > 5:
                importance_score += 0.1
            
            # Présence de mots-clés importants
            important_keywords = [
                "jarvis", "capacités", "mémoire", "apprentissage", "créativité",
                "sécurité", "whatsapp", "agent", "innovation", "révolutionnaire",
                "jean-luc", "passave", "important", "critique", "essentiel"
            ]
            
            conversation_text = json.dumps(conversation).lower()
            keyword_matches = sum(1 for keyword in important_keywords if keyword in conversation_text)
            importance_score += min(keyword_matches * 0.05, 0.3)
            
            # Récence (conversations récentes sont plus importantes)
            try:
                timestamp = conversation.get("timestamp", "")
                if timestamp:
                    conv_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    days_ago = (datetime.now() - conv_time).days
                    if days_ago < 7:
                        importance_score += 0.2
                    elif days_ago < 30:
                        importance_score += 0.1
            except:
                pass
            
            # Présence de code ou de configurations
            if any("def " in str(msg) or "class " in str(msg) or "import " in str(msg) 
                   for msg in messages):
                importance_score += 0.15
            
            # Conversations avec des erreurs ou des solutions
            if any("erreur" in str(msg).lower() or "solution" in str(msg).lower() 
                   for msg in messages):
                importance_score += 0.1
            
            return min(importance_score, 1.0)  # Limiter à 1.0
            
        except Exception as e:
            print(f"❌ Erreur calcul importance: {e}")
            return 0.5  # Score par défaut

    def generate_conversation_summary(self, conversation):
        """GÉNÈRE UN RÉSUMÉ D'UNE CONVERSATION"""
        try:
            messages = conversation.get("messages", [])
            if not messages:
                return "Conversation vide"
            
            # Préparer le contenu pour résumé
            conversation_content = ""
            for msg in messages[:10]:  # Limiter aux 10 premiers messages
                role = msg.get("role", "unknown")
                content = msg.get("content", "")[:300]  # Limiter la longueur
                conversation_content += f"{role}: {content}\n"
            
            summary_prompt = f"""
📝 RÉSUMÉ DE CONVERSATION JARVIS

🎯 MISSION : Crée un résumé concis et informatif de cette conversation

💬 CONVERSATION À RÉSUMER :
{conversation_content}

📋 RÉSUMÉ DEMANDÉ :
1. Sujet principal de la conversation
2. Points clés discutés
3. Décisions ou actions importantes
4. Résultat ou conclusion

🎯 CONTRAINTES :
- Maximum {self.compression_config['summary_length_target']} caractères
- Langage clair et précis
- Focus sur l'essentiel
- Conserver les informations importantes pour Jean-Luc

Crée un résumé professionnel et utile !
            """
            
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es JARVIS, expert en résumé de conversations. Tu crées des résumés concis et informatifs pour Jean-Luc Passave."
                        },
                        {
                            "role": "user",
                            "content": summary_prompt
                        }
                    ],
                    "max_tokens": 300,
                    "temperature": 0.3
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                summary = result['choices'][0]['message']['content']
                
                # Limiter la longueur du résumé
                if len(summary) > self.compression_config['summary_length_target']:
                    summary = summary[:self.compression_config['summary_length_target']] + "..."
                
                return summary
            else:
                return "Résumé automatique non disponible"
                
        except Exception as e:
            print(f"❌ Erreur génération résumé: {e}")
            return f"Conversation du {conversation.get('timestamp', 'date inconnue')[:10]}"

    def compress_old_conversations(self):
        """COMPRESSE LES ANCIENNES CONVERSATIONS"""
        try:
            print("💾 Démarrage compression mémoire...")
            
            # Charger la mémoire thermique
            memory_data = self.load_thermal_memory()
            conversations = memory_data.get("conversations", [])
            
            if len(conversations) <= self.compression_config["max_conversations_in_memory"]:
                print(f"ℹ️ Pas de compression nécessaire ({len(conversations)} conversations)")
                return
            
            # Trier par importance et date
            conversations_with_importance = []
            for conv in conversations:
                importance = self.calculate_conversation_importance(conv)
                conversations_with_importance.append((conv, importance))
            
            # Trier par importance décroissante
            conversations_with_importance.sort(key=lambda x: x[1], reverse=True)
            
            # Séparer les conversations à garder et à archiver
            to_keep = []
            to_archive = []
            
            for i, (conv, importance) in enumerate(conversations_with_importance):
                if (i < self.compression_config["max_conversations_in_memory"] or 
                    (self.compression_config["keep_important_conversations"] and 
                     importance >= self.compression_config["importance_threshold"])):
                    to_keep.append(conv)
                else:
                    to_archive.append(conv)
            
            if not to_archive:
                print("ℹ️ Aucune conversation à archiver")
                return
            
            # Créer l'archive
            archive_filename = f"archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json.gz"
            archive_path = os.path.join(self.compressed_archive_dir, archive_filename)
            
            # Générer des résumés pour les conversations archivées
            archived_conversations = []
            summaries = []
            
            for conv in to_archive:
                summary = self.generate_conversation_summary(conv)
                archived_conversations.append(conv)
                summaries.append({
                    "original_timestamp": conv.get("timestamp"),
                    "summary": summary,
                    "importance": self.calculate_conversation_importance(conv),
                    "message_count": len(conv.get("messages", [])),
                    "archive_file": archive_filename
                })
            
            # Sauvegarder l'archive compressée
            with gzip.open(archive_path, 'wt', encoding='utf-8') as f:
                json.dump(archived_conversations, f, indent=2, ensure_ascii=False)
            
            # Sauvegarder les résumés
            self.save_summaries(summaries)
            
            # Mettre à jour la mémoire thermique
            memory_data["conversations"] = to_keep
            self.save_thermal_memory(memory_data)
            
            print(f"✅ Compression terminée:")
            print(f"   📁 {len(to_archive)} conversations archivées dans {archive_filename}")
            print(f"   💾 {len(to_keep)} conversations gardées en mémoire active")
            print(f"   📝 {len(summaries)} résumés générés")
            
            # Statistiques de compression
            original_size = len(conversations)
            compressed_size = len(to_keep)
            compression_ratio = (1 - compressed_size / original_size) * 100
            print(f"   📊 Taux de compression: {compression_ratio:.1f}%")
            
        except Exception as e:
            print(f"❌ Erreur compression: {e}")

    def save_summaries(self, new_summaries):
        """SAUVEGARDE LES RÉSUMÉS"""
        try:
            # Charger les résumés existants
            try:
                with open(self.memory_summaries_file, 'r', encoding='utf-8') as f:
                    existing_summaries = json.load(f)
            except:
                existing_summaries = []
            
            # Ajouter les nouveaux résumés
            existing_summaries.extend(new_summaries)
            
            # Garder seulement les 500 derniers résumés
            if len(existing_summaries) > 500:
                existing_summaries = existing_summaries[-500:]
            
            # Sauvegarder
            with open(self.memory_summaries_file, 'w', encoding='utf-8') as f:
                json.dump(existing_summaries, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde résumés: {e}")

    def search_in_archives(self, query, max_results=5):
        """RECHERCHE DANS LES ARCHIVES"""
        try:
            # Charger les résumés
            try:
                with open(self.memory_summaries_file, 'r', encoding='utf-8') as f:
                    summaries = json.load(f)
            except:
                return []
            
            # Recherche simple dans les résumés
            query_lower = query.lower()
            matching_summaries = []
            
            for summary in summaries:
                summary_text = summary.get("summary", "").lower()
                if query_lower in summary_text:
                    matching_summaries.append(summary)
            
            # Trier par importance
            matching_summaries.sort(key=lambda x: x.get("importance", 0), reverse=True)
            
            return matching_summaries[:max_results]
            
        except Exception as e:
            print(f"❌ Erreur recherche archives: {e}")
            return []

    def get_compression_stats(self):
        """STATISTIQUES DE COMPRESSION"""
        try:
            # Mémoire active
            memory_data = self.load_thermal_memory()
            active_conversations = len(memory_data.get("conversations", []))
            
            # Archives
            archive_files = [f for f in os.listdir(self.compressed_archive_dir) 
                           if f.endswith('.json.gz')] if os.path.exists(self.compressed_archive_dir) else []
            
            # Résumés
            try:
                with open(self.memory_summaries_file, 'r', encoding='utf-8') as f:
                    summaries = json.load(f)
                total_summaries = len(summaries)
            except:
                total_summaries = 0
            
            # Taille des fichiers
            memory_size = os.path.getsize(self.thermal_memory_file) if os.path.exists(self.thermal_memory_file) else 0
            
            archive_size = 0
            for archive_file in archive_files:
                archive_path = os.path.join(self.compressed_archive_dir, archive_file)
                archive_size += os.path.getsize(archive_path)
            
            return {
                "active_conversations": active_conversations,
                "archive_files": len(archive_files),
                "total_summaries": total_summaries,
                "memory_file_size_kb": memory_size / 1024,
                "archives_size_kb": archive_size / 1024,
                "compression_ratio": (archive_size / (memory_size + archive_size) * 100) if (memory_size + archive_size) > 0 else 0,
                "last_compression": self.get_last_compression_time()
            }
            
        except Exception as e:
            print(f"❌ Erreur stats compression: {e}")
            return {}

    def get_last_compression_time(self):
        """OBTIENT LA DATE DE LA DERNIÈRE COMPRESSION"""
        try:
            if not os.path.exists(self.compressed_archive_dir):
                return None
            
            archive_files = [f for f in os.listdir(self.compressed_archive_dir) 
                           if f.endswith('.json.gz')]
            
            if not archive_files:
                return None
            
            # Trier par date de modification
            archive_files.sort(key=lambda f: os.path.getmtime(
                os.path.join(self.compressed_archive_dir, f)
            ), reverse=True)
            
            latest_file = archive_files[0]
            latest_time = os.path.getmtime(os.path.join(self.compressed_archive_dir, latest_file))
            
            return datetime.fromtimestamp(latest_time).isoformat()
            
        except:
            return None

    def start_compression_daemon(self):
        """DÉMARRE LE DÉMON DE COMPRESSION AUTOMATIQUE"""
        def compression_worker():
            while True:
                try:
                    # Attendre l'intervalle de compression
                    interval_seconds = self.compression_config["compression_interval_hours"] * 3600
                    time.sleep(interval_seconds)
                    
                    # Effectuer la compression
                    self.compress_old_conversations()
                    
                except Exception as e:
                    print(f"❌ Erreur démon compression: {e}")
                    time.sleep(3600)  # Attendre 1 heure en cas d'erreur
        
        # Lancer en thread séparé
        compression_thread = threading.Thread(target=compression_worker, daemon=True)
        compression_thread.start()
        print("💾 Démon de compression automatique démarré")

if __name__ == "__main__":
    print("💾 JARVIS MEMORY COMPRESSION")
    print("============================")
    
    compressor = JarvisMemoryCompression()
    
    # Test de compression
    print("🧪 Test de compression...")
    compressor.compress_old_conversations()
    
    # Afficher les stats
    stats = compressor.get_compression_stats()
    print(f"\n📊 Stats compression: {stats}")
