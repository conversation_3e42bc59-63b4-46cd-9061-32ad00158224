#!/usr/bin/env python3
"""
🔄 MIGRATION VERS ARCHITECTURE MULTI-FENÊTRES
Script pour migrer de l'ancienne interface vers la nouvelle architecture
Créé pour Jean-<PERSON> Passave
"""

import os
import shutil
import subprocess
import sys
from datetime import datetime

def create_backup():
    """Crée une sauvegarde de l'ancienne interface"""
    print("💾 CRÉATION SAUVEGARDE...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"BACKUP_ANCIENNE_INTERFACE_{timestamp}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        # Fichiers à sauvegarder
        files_to_backup = [
            "jarvis_interface_propre.py",
            "thermal_memory_persistent.json",
            "jarvis_accelerateurs_cascade_persistant.json",
            "jarvis_whatsapp_config.json",
            "jarvis_security_log.json"
        ]
        
        for file in files_to_backup:
            if os.path.exists(file):
                shutil.copy2(file, backup_dir)
                print(f"✅ Sauvegardé: {file}")
        
        print(f"✅ Sauvegarde créée dans: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        print(f"❌ Erreur sauvegarde: {e}")
        return None

def extract_functions_from_old_interface():
    """Extrait les fonctions importantes de l'ancienne interface"""
    print("🔍 EXTRACTION FONCTIONS ANCIENNES...")
    
    if not os.path.exists("jarvis_interface_propre.py"):
        print("❌ Fichier jarvis_interface_propre.py non trouvé")
        return False
    
    try:
        with open("jarvis_interface_propre.py", 'r') as f:
            old_content = f.read()
        
        # Extraire les fonctions importantes
        important_functions = [
            "execute_universal_code",
            "activate_whatsapp_integration",
            "get_whatsapp_status",
            "start_whatsapp_service",
            "test_whatsapp_real",
            "format_universal_code_result",
            "change_code_language",
            "save_code_to_file"
        ]
        
        extracted_functions = []
        
        for func_name in important_functions:
            # Rechercher la fonction
            start_pattern = f"def {func_name}("
            start_pos = old_content.find(start_pattern)
            
            if start_pos != -1:
                # Trouver la fin de la fonction (prochaine def ou fin de fichier)
                lines = old_content[start_pos:].split('\n')
                func_lines = [lines[0]]  # Première ligne (def)
                
                for i, line in enumerate(lines[1:], 1):
                    if line.startswith('def ') and not line.startswith('    '):
                        break
                    func_lines.append(line)
                
                extracted_functions.append('\n'.join(func_lines))
                print(f"✅ Fonction extraite: {func_name}")
        
        # Sauvegarder les fonctions extraites
        with open("fonctions_extraites.py", 'w') as f:
            f.write("# Fonctions extraites de l'ancienne interface\n\n")
            f.write('\n\n'.join(extracted_functions))
        
        print(f"✅ {len(extracted_functions)} fonctions extraites dans fonctions_extraites.py")
        return True
        
    except Exception as e:
        print(f"❌ Erreur extraction: {e}")
        return False

def test_new_architecture():
    """Teste la nouvelle architecture multi-fenêtres"""
    print("🧪 TEST NOUVELLE ARCHITECTURE...")
    
    try:
        # Importer et tester
        import jarvis_architecture_multi_fenetres as new_arch
        
        print("✅ Import réussi")
        
        # Tester la création des interfaces
        try:
            main_dash = new_arch.create_main_dashboard()
            print("✅ Dashboard principal créé")
        except Exception as e:
            print(f"❌ Erreur dashboard: {e}")
        
        try:
            code_editor = new_arch.create_code_editor()
            print("✅ Éditeur de code créé")
        except Exception as e:
            print(f"❌ Erreur éditeur: {e}")
        
        try:
            thoughts = new_arch.create_thoughts_viewer()
            print("✅ Visualiseur pensées créé")
        except Exception as e:
            print(f"❌ Erreur pensées: {e}")
        
        print("✅ Nouvelle architecture testée avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test architecture: {e}")
        return False

def create_launcher_script():
    """Crée un script de lancement simplifié"""
    print("📝 CRÉATION SCRIPT LANCEMENT...")
    
    launcher_content = '''#!/usr/bin/env python3
"""
🚀 LANCEUR JARVIS MULTI-FENÊTRES
Script de lancement simplifié pour Jean-Luc
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.append('.')

try:
    from jarvis_architecture_multi_fenetres import launch_all_windows
    
    print("🤖 DÉMARRAGE JARVIS MULTI-FENÊTRES")
    print("=" * 50)
    print("🏠 Interface organisée et professionnelle")
    print("🔧 Chaque fonction dans sa propre fenêtre")
    print("✨ Fini le désordre sur une seule page !")
    print("=" * 50)
    
    launch_all_windows()
    
except ImportError as e:
    print(f"❌ Erreur d'importation: {e}")
    print("Vérifiez que jarvis_architecture_multi_fenetres.py existe")
except Exception as e:
    print(f"❌ Erreur: {e}")
'''
    
    try:
        with open("lancer_jarvis_multi_fenetres.py", 'w') as f:
            f.write(launcher_content)
        
        # Rendre exécutable
        os.chmod("lancer_jarvis_multi_fenetres.py", 0o755)
        
        print("✅ Script de lancement créé: lancer_jarvis_multi_fenetres.py")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création script: {e}")
        return False

def main():
    """Fonction principale de migration"""
    print("🔄 ================================")
    print("🤖 MIGRATION VERS MULTI-FENÊTRES JARVIS")
    print("🔄 ================================")
    print("👨‍💻 Créé pour Jean-Luc Passave")
    print("🎯 Objectif: Interface organisée et professionnelle")
    print()
    
    # Étape 1: Sauvegarde
    backup_dir = create_backup()
    if not backup_dir:
        print("❌ Échec sauvegarde - Arrêt")
        return
    
    # Étape 2: Extraction des fonctions
    if not extract_functions_from_old_interface():
        print("⚠️ Échec extraction - Continuons quand même")
    
    # Étape 3: Test nouvelle architecture
    if not test_new_architecture():
        print("❌ Échec test nouvelle architecture")
        return
    
    # Étape 4: Création script de lancement
    if not create_launcher_script():
        print("❌ Échec création script")
        return
    
    print("\n🎉 MIGRATION RÉUSSIE !")
    print("=" * 50)
    print("✅ Sauvegarde créée")
    print("✅ Fonctions extraites")
    print("✅ Nouvelle architecture testée")
    print("✅ Script de lancement créé")
    print()
    print("🚀 PROCHAINES ÉTAPES:")
    print("1. Lancez: python lancer_jarvis_multi_fenetres.py")
    print("2. Testez chaque fenêtre individuellement")
    print("3. Vérifiez que tous les boutons fonctionnent")
    print()
    print("💡 AVANTAGES DE LA NOUVELLE ARCHITECTURE:")
    print("🏠 Dashboard principal clair et organisé")
    print("💻 Éditeur de code dans sa propre fenêtre")
    print("🧠 Pensées JARVIS visibles séparément")
    print("⚙️ Configuration dédiée")
    print("📱 WhatsApp dans sa propre interface")
    print("📊 Monitoring 24h/24 séparé")
    print("🔐 Sécurité dans sa propre fenêtre")
    print()
    print("🎯 FINI LE DÉSORDRE SUR UNE SEULE PAGE !")
    print("✨ Interface professionnelle et organisée")
    print("=" * 50)

if __name__ == "__main__":
    main()
