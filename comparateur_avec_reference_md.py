#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 COMPARATEUR AVEC RÉFÉRENCE MD - JEAN-LUC PASSAVE
Compare le code actuel avec le code de référence dans le fichier .md
"""

import os
import re
import json
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔍 [{timestamp}] {message}")

def extraire_code_du_md():
    """Extrait le code Python du fichier .md"""
    log("📄 EXTRACTION CODE DU FICHIER MD")
    
    try:
        with open("CODE_MEMOIRE_THERMIQUE_COMPLET_POUR_CHATGPT.md", 'r', encoding='utf-8') as f:
            contenu_md = f.read()
        
        # Extraire les blocs de code Python
        blocs_python = re.findall(r'```python\n(.*?)\n```', contenu_md, re.DOTALL)
        
        if blocs_python:
            # Combiner tous les blocs de code
            code_reference = '\n\n'.join(blocs_python)
            log(f"✅ Code extrait: {len(code_reference.split())} lignes")
            return code_reference
        else:
            log("❌ Aucun bloc Python trouvé dans le .md")
            return None
            
    except Exception as e:
        log(f"❌ Erreur lecture fichier .md: {e}")
        return None

def analyser_differences_avec_reference(code_reference, code_actuel):
    """Analyse les différences avec le code de référence"""
    log("🔍 ANALYSE DIFFÉRENCES AVEC RÉFÉRENCE")
    
    problemes_detectes = []
    
    # 1. Vérifier structure de base
    if "def create_interface" in code_reference and "def create_interface" not in code_actuel:
        problemes_detectes.append({
            'type': 'FONCTION_MANQUANTE',
            'probleme': 'Fonction create_interface manquante',
            'gravite': 'CRITIQUE'
        })
    
    # 2. Vérifier configuration Gradio simple
    config_ref = re.search(r'gr\.Blocks\([^)]*\)', code_reference)
    config_actuel = re.search(r'gr\.Blocks\([^)]*\)', code_actuel)
    
    if config_ref and config_actuel:
        if len(config_actuel.group()) > len(config_ref.group()) * 2:
            problemes_detectes.append({
                'type': 'CONFIG_COMPLEXE',
                'probleme': 'Configuration gr.Blocks trop complexe',
                'gravite': 'HAUTE'
            })
    
    # 3. Vérifier CSS
    if 'custom_css = ""' in code_reference and 'custom_css = """' in code_actuel:
        css_actuel = re.search(r'custom_css = """.*?"""', code_actuel, re.DOTALL)
        if css_actuel and len(css_actuel.group()) > 1000:
            problemes_detectes.append({
                'type': 'CSS_SURCHARGÉ',
                'probleme': 'CSS trop complexe par rapport à la référence',
                'gravite': 'MOYENNE'
            })
    
    # 4. Vérifier imports
    imports_ref = re.findall(r'^import .*|^from .* import .*', code_reference, re.MULTILINE)
    imports_actuel = re.findall(r'^import .*|^from .* import .*', code_actuel, re.MULTILINE)
    
    if len(imports_actuel) > len(imports_ref) * 2:
        problemes_detectes.append({
            'type': 'IMPORTS_EXCESSIFS',
            'probleme': f'Trop d\'imports: {len(imports_actuel)} vs {len(imports_ref)} référence',
            'gravite': 'MOYENNE'
        })
    
    # 5. Vérifier fonctions principales
    fonctions_ref = re.findall(r'^def ([a-zA-Z_][a-zA-Z0-9_]*)', code_reference, re.MULTILINE)
    fonctions_actuel = re.findall(r'^def ([a-zA-Z_][a-zA-Z0-9_]*)', code_actuel, re.MULTILINE)
    
    fonctions_essentielles = ['send_message', 'create_interface', 'load_thermal_memory']
    for fonction in fonctions_essentielles:
        if fonction in fonctions_ref and fonction not in fonctions_actuel:
            problemes_detectes.append({
                'type': 'FONCTION_ESSENTIELLE_MANQUANTE',
                'probleme': f'Fonction essentielle manquante: {fonction}',
                'gravite': 'CRITIQUE'
            })
    
    return problemes_detectes

def comparer_sections_specifiques(code_reference, code_actuel):
    """Compare des sections spécifiques du code"""
    log("🔍 COMPARAISON SECTIONS SPÉCIFIQUES")
    
    sections_problematiques = []
    
    # 1. Configuration chatbot
    chatbot_ref = re.search(r'gr\.Chatbot\([^)]*\)', code_reference)
    chatbot_actuel = re.search(r'gr\.Chatbot\([^)]*\)', code_actuel)
    
    if chatbot_ref and chatbot_actuel:
        # Vérifier si la config actuelle est plus complexe
        if 'type=' in chatbot_actuel.group() and 'type=' not in chatbot_ref.group():
            sections_problematiques.append({
                'section': 'CHATBOT',
                'probleme': 'Configuration chatbot plus complexe que référence',
                'reference': chatbot_ref.group(),
                'actuel': chatbot_actuel.group()
            })
    
    # 2. Fonction de lancement
    launch_ref = re.search(r'demo\.launch\([^)]*\)', code_reference)
    launch_actuel = re.search(r'demo\.launch\([^)]*\)', code_actuel)
    
    if launch_ref and launch_actuel:
        if len(launch_actuel.group()) > len(launch_ref.group()) * 1.5:
            sections_problematiques.append({
                'section': 'LAUNCH',
                'probleme': 'Configuration launch plus complexe',
                'reference': launch_ref.group(),
                'actuel': launch_actuel.group()
            })
    
    # 3. Gestion des messages
    if 'conversation_history' in code_reference and 'conversation_history' in code_actuel:
        # Vérifier la logique de gestion des messages
        msg_logic_ref = code_reference.count('conversation_history.append')
        msg_logic_actuel = code_actuel.count('conversation_history.append')
        
        if msg_logic_actuel != msg_logic_ref:
            sections_problematiques.append({
                'section': 'MESSAGE_LOGIC',
                'probleme': f'Logique messages différente: {msg_logic_ref} vs {msg_logic_actuel}',
                'gravite': 'HAUTE'
            })
    
    return sections_problematiques

def generer_corrections_automatiques(problemes, sections):
    """Génère des corrections automatiques basées sur l'analyse"""
    log("🔧 GÉNÉRATION CORRECTIONS AUTOMATIQUES")
    
    corrections = []
    
    for probleme in problemes:
        if probleme['type'] == 'CSS_SURCHARGÉ':
            corrections.append({
                'action': 'SIMPLIFIER_CSS',
                'description': 'Remplacer CSS complexe par CSS simple',
                'code': 'custom_css = ""',
                'priorite': 1
            })
        
        elif probleme['type'] == 'CONFIG_COMPLEXE':
            corrections.append({
                'action': 'SIMPLIFIER_BLOCKS',
                'description': 'Simplifier configuration gr.Blocks',
                'code': 'with gr.Blocks(title="JARVIS") as demo:',
                'priorite': 1
            })
    
    for section in sections:
        if section['section'] == 'CHATBOT':
            corrections.append({
                'action': 'SIMPLIFIER_CHATBOT',
                'description': 'Restaurer configuration chatbot simple',
                'code': 'gr.Chatbot(label="Conversation", height=400)',
                'priorite': 1
            })
    
    return corrections

def comparaison_avec_reference():
    """Comparaison complète avec le code de référence"""
    log("🔍 COMPARAISON AVEC CODE DE RÉFÉRENCE")
    print("=" * 60)
    
    # 1. Extraire code de référence
    code_reference = extraire_code_du_md()
    if not code_reference:
        log("❌ Impossible d'extraire le code de référence")
        return False
    
    # 2. Charger code actuel
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code_actuel = f.read()
    except Exception as e:
        log(f"❌ Erreur lecture code actuel: {e}")
        return False
    
    log(f"✅ Code référence: {len(code_reference.split())} lignes")
    log(f"✅ Code actuel: {len(code_actuel.split())} lignes")
    
    # 3. Analyser différences
    problemes = analyser_differences_avec_reference(code_reference, code_actuel)
    sections = comparer_sections_specifiques(code_reference, code_actuel)
    corrections = generer_corrections_automatiques(problemes, sections)
    
    # 4. Rapport
    print("\n" + "=" * 60)
    log("📊 RAPPORT COMPARAISON AVEC RÉFÉRENCE")
    print("=" * 60)
    
    print(f"🚨 Problèmes détectés: {len(problemes)}")
    for probleme in problemes:
        print(f"   {probleme['gravite']}: {probleme['probleme']}")
    
    print(f"\n⚠️  Sections problématiques: {len(sections)}")
    for section in sections:
        print(f"   {section['section']}: {section['probleme']}")
    
    print(f"\n🔧 Corrections proposées: {len(corrections)}")
    for correction in corrections:
        print(f"   {correction['priorite']}. {correction['description']}")
    
    # 5. Sauvegarder rapport
    rapport = {
        'timestamp': datetime.now().isoformat(),
        'problemes': problemes,
        'sections_problematiques': sections,
        'corrections_proposees': corrections,
        'stats': {
            'lignes_reference': len(code_reference.split('\n')),
            'lignes_actuel': len(code_actuel.split('\n')),
            'ratio_complexite': len(code_actuel) / len(code_reference)
        }
    }
    
    with open("rapport_comparaison_reference.json", 'w') as f:
        json.dump(rapport, f, indent=2, default=str)
    
    log("📄 Rapport sauvé: rapport_comparaison_reference.json")
    
    return True

def appliquer_corrections_automatiques():
    """Applique automatiquement les corrections identifiées"""
    log("🔧 APPLICATION CORRECTIONS AUTOMATIQUES")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Correction 1: Simplifier CSS
        if 'custom_css = """' in code and len(re.search(r'custom_css = """.*?"""', code, re.DOTALL).group()) > 500:
            code = re.sub(r'custom_css = """.*?"""', 'custom_css = ""', code, flags=re.DOTALL)
            log("✅ CSS simplifié")
        
        # Correction 2: Simplifier chatbot
        if 'type=' in code and 'gr.Chatbot(' in code:
            code = re.sub(r'gr\.Chatbot\([^)]*\)', 'gr.Chatbot(label="💬 Conversation", height=400)', code)
            log("✅ Configuration chatbot simplifiée")
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log("✅ Corrections appliquées")
        return True
        
    except Exception as e:
        log(f"❌ Erreur application corrections: {e}")
        return False

if __name__ == "__main__":
    print("🔍 COMPARATEUR AVEC RÉFÉRENCE MD")
    print("Compare avec le code de référence du fichier .md")
    print("=" * 60)
    print("1. Comparaison seulement")
    print("2. Comparaison + corrections automatiques")
    
    choix = input("\nVotre choix (1 ou 2): ").strip()
    
    if choix == "1":
        if comparaison_avec_reference():
            print("\n🎉 COMPARAISON TERMINÉE")
        else:
            print("\n❌ ÉCHEC comparaison")
    
    elif choix == "2":
        if comparaison_avec_reference():
            print("\n🔧 Application des corrections...")
            if appliquer_corrections_automatiques():
                print("✅ Corrections appliquées avec succès")
            else:
                print("❌ Échec application corrections")
        else:
            print("\n❌ ÉCHEC comparaison")
    
    else:
        print("❌ Choix invalide")
        comparaison_avec_reference()
