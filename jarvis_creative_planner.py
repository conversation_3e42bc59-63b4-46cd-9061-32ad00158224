#!/usr/bin/env python3
"""
📋 JARVIS CREATIVE PLANNER
Planificateur de projets créatifs pour organiser les idées de JARVIS
avec TODO, priorités, deadlines et suivi de progression

Créé pour Jean-Luc Passave
"""

import json
import time
import requests
from datetime import datetime, timedelta
from enum import Enum

class ProjectStatus(Enum):
    IDEA = "idea"
    PLANNED = "planned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"

class ProjectPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5

class JarvisCreativePlanner:
    def __init__(self):
        self.projects_file = "jarvis_creative_projects_planned.json"
        self.backlog_file = "jarvis_creative_backlog.json"
        self.deadlines_file = "jarvis_creative_deadlines.json"
        
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        
        # Charger les projets existants
        self.load_projects()

    def load_projects(self):
        """CHARGE LES PROJETS EXISTANTS"""
        try:
            with open(self.projects_file, 'r', encoding='utf-8') as f:
                self.projects = json.load(f)
        except:
            self.projects = []

    def save_projects(self):
        """SAUVEGARDE LES PROJETS"""
        try:
            with open(self.projects_file, 'w', encoding='utf-8') as f:
                json.dump(self.projects, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde projets: {e}")

    def create_project_plan(self, creative_idea):
        """CRÉE UN PLAN DE PROJET À PARTIR D'UNE IDÉE CRÉATIVE"""
        
        planning_prompt = f"""
📋 PLANIFICATION PROJET CRÉATIF JARVIS

🎯 MISSION : Crée un plan détaillé pour réaliser cette idée créative

💡 IDÉE CRÉATIVE :
Type : {creative_idea.get('type', 'unknown')}
Contenu : {creative_idea.get('content', '')[:500]}...

📋 GÉNÈRE UN PLAN STRUCTURÉ :

1. **OBJECTIF PRINCIPAL** (1 phrase claire)
2. **ÉTAPES DE RÉALISATION** (5-8 étapes concrètes)
3. **RESSOURCES NÉCESSAIRES** (outils, temps, compétences)
4. **LIVRABLES ATTENDUS** (ce qui sera produit)
5. **ESTIMATION TEMPS** (heures/jours nécessaires)
6. **CRITÈRES DE SUCCÈS** (comment mesurer la réussite)
7. **RISQUES POTENTIELS** (obstacles possibles)

🎯 Sois précis, réalisable et orienté résultats !
        """
        
        try:
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es JARVIS, expert en planification de projets créatifs. Tu crées des plans détaillés, réalisables et structurés pour Jean-Luc Passave."
                        },
                        {
                            "role": "user",
                            "content": planning_prompt
                        }
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.7
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                plan_content = result['choices'][0]['message']['content']
                
                # Créer le projet planifié
                project = {
                    "id": f"project_{int(time.time())}",
                    "title": self.extract_project_title(creative_idea),
                    "type": creative_idea.get('type', 'unknown'),
                    "original_idea": creative_idea,
                    "plan": plan_content,
                    "status": ProjectStatus.PLANNED.value,
                    "priority": self.determine_priority(creative_idea),
                    "created_date": datetime.now().isoformat(),
                    "estimated_duration": self.extract_duration(plan_content),
                    "progress": 0,
                    "tasks": self.extract_tasks(plan_content),
                    "deadline": None,
                    "tags": self.generate_tags(creative_idea)
                }
                
                return project
            else:
                return None
                
        except Exception as e:
            print(f"❌ Erreur planification: {e}")
            return None

    def extract_project_title(self, creative_idea):
        """EXTRAIT UN TITRE DE PROJET"""
        content = creative_idea.get('content', '')
        lines = content.split('\n')
        
        # Chercher un titre dans les premières lignes
        for line in lines[:5]:
            if any(keyword in line.lower() for keyword in ['titre', 'nom', 'projet', ':']):
                title = line.split(':')[-1].strip()
                if len(title) > 5:
                    return title
        
        # Titre par défaut
        return f"Projet {creative_idea.get('type', 'créatif')} - {datetime.now().strftime('%d/%m')}"

    def determine_priority(self, creative_idea):
        """DÉTERMINE LA PRIORITÉ D'UN PROJET"""
        content = creative_idea.get('content', '').lower()
        
        # Mots-clés haute priorité
        high_priority_keywords = ['urgent', 'important', 'critique', 'révolutionnaire', 'innovation']
        medium_priority_keywords = ['utile', 'intéressant', 'pratique', 'amélioration']
        
        if any(keyword in content for keyword in high_priority_keywords):
            return ProjectPriority.HIGH.value
        elif any(keyword in content for keyword in medium_priority_keywords):
            return ProjectPriority.MEDIUM.value
        else:
            return ProjectPriority.LOW.value

    def extract_duration(self, plan_content):
        """EXTRAIT LA DURÉE ESTIMÉE DU PLAN"""
        content = plan_content.lower()
        
        # Chercher des indications de temps
        if 'heure' in content:
            return "quelques heures"
        elif 'jour' in content:
            return "quelques jours"
        elif 'semaine' in content:
            return "quelques semaines"
        else:
            return "à définir"

    def extract_tasks(self, plan_content):
        """EXTRAIT LES TÂCHES DU PLAN"""
        tasks = []
        lines = plan_content.split('\n')
        
        current_section = ""
        for line in lines:
            line = line.strip()
            if 'étapes' in line.lower() or 'tâches' in line.lower():
                current_section = "tasks"
                continue
            
            if current_section == "tasks" and line:
                if line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '-', '*')):
                    task_text = line.split('.', 1)[-1].strip() if '.' in line else line[1:].strip()
                    if len(task_text) > 5:
                        tasks.append({
                            "id": f"task_{len(tasks) + 1}",
                            "description": task_text,
                            "completed": False,
                            "created_date": datetime.now().isoformat()
                        })
        
        return tasks

    def generate_tags(self, creative_idea):
        """GÉNÈRE DES TAGS POUR LE PROJET"""
        tags = [creative_idea.get('type', 'unknown')]
        
        content = creative_idea.get('content', '').lower()
        
        # Tags automatiques selon le contenu
        tag_keywords = {
            'code': ['python', 'javascript', 'script', 'programmation'],
            'music': ['mélodie', 'rythme', 'composition', 'audio'],
            'writing': ['texte', 'article', 'poème', 'écriture'],
            'innovation': ['révolutionnaire', 'nouveau', 'innovation'],
            'automation': ['automatique', 'automation', 'script'],
            'ai': ['intelligence', 'artificielle', 'ia', 'machine']
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in content for keyword in keywords):
                tags.append(tag)
        
        return list(set(tags))  # Supprimer les doublons

    def add_project_to_backlog(self, project):
        """AJOUTE UN PROJET AU BACKLOG"""
        try:
            self.projects.append(project)
            self.save_projects()
            
            print(f"📋 Projet ajouté au backlog: {project['title']}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur ajout backlog: {e}")
            return False

    def get_project_by_id(self, project_id):
        """RÉCUPÈRE UN PROJET PAR SON ID"""
        for project in self.projects:
            if project.get('id') == project_id:
                return project
        return None

    def update_project_status(self, project_id, new_status):
        """MET À JOUR LE STATUT D'UN PROJET"""
        project = self.get_project_by_id(project_id)
        if project:
            project['status'] = new_status
            project['last_updated'] = datetime.now().isoformat()
            self.save_projects()
            return True
        return False

    def complete_task(self, project_id, task_id):
        """MARQUE UNE TÂCHE COMME TERMINÉE"""
        project = self.get_project_by_id(project_id)
        if project:
            for task in project.get('tasks', []):
                if task.get('id') == task_id:
                    task['completed'] = True
                    task['completed_date'] = datetime.now().isoformat()
                    
                    # Mettre à jour le progrès du projet
                    total_tasks = len(project['tasks'])
                    completed_tasks = sum(1 for t in project['tasks'] if t.get('completed', False))
                    project['progress'] = int((completed_tasks / total_tasks) * 100) if total_tasks > 0 else 0
                    
                    # Si toutes les tâches sont terminées, marquer le projet comme terminé
                    if project['progress'] == 100:
                        project['status'] = ProjectStatus.COMPLETED.value
                        project['completed_date'] = datetime.now().isoformat()
                    
                    self.save_projects()
                    return True
        return False

    def get_active_projects(self):
        """RÉCUPÈRE LES PROJETS ACTIFS"""
        active_statuses = [ProjectStatus.PLANNED.value, ProjectStatus.IN_PROGRESS.value]
        return [p for p in self.projects if p.get('status') in active_statuses]

    def get_projects_by_priority(self, priority):
        """RÉCUPÈRE LES PROJETS PAR PRIORITÉ"""
        return [p for p in self.projects if p.get('priority') == priority]

    def get_overdue_projects(self):
        """RÉCUPÈRE LES PROJETS EN RETARD"""
        overdue = []
        current_date = datetime.now()
        
        for project in self.projects:
            deadline = project.get('deadline')
            if deadline and project.get('status') not in [ProjectStatus.COMPLETED.value, ProjectStatus.CANCELLED.value]:
                deadline_date = datetime.fromisoformat(deadline)
                if deadline_date < current_date:
                    overdue.append(project)
        
        return overdue

    def generate_daily_creative_agenda(self):
        """GÉNÈRE L'AGENDA CRÉATIF DU JOUR"""
        active_projects = self.get_active_projects()
        high_priority = self.get_projects_by_priority(ProjectPriority.HIGH.value)
        overdue = self.get_overdue_projects()
        
        agenda = {
            "date": datetime.now().isoformat(),
            "total_active_projects": len(active_projects),
            "high_priority_projects": len(high_priority),
            "overdue_projects": len(overdue),
            "recommended_actions": [],
            "daily_focus": None
        }
        
        # Recommandations
        if overdue:
            agenda["recommended_actions"].append(f"🚨 {len(overdue)} projet(s) en retard à traiter")
        
        if high_priority:
            agenda["recommended_actions"].append(f"⭐ {len(high_priority)} projet(s) haute priorité")
        
        # Focus du jour
        if high_priority:
            agenda["daily_focus"] = high_priority[0]
        elif active_projects:
            agenda["daily_focus"] = active_projects[0]
        
        return agenda

    def get_creative_stats(self):
        """STATISTIQUES DU PLANIFICATEUR"""
        total_projects = len(self.projects)
        
        stats_by_status = {}
        stats_by_type = {}
        stats_by_priority = {}
        
        for project in self.projects:
            # Par statut
            status = project.get('status', 'unknown')
            stats_by_status[status] = stats_by_status.get(status, 0) + 1
            
            # Par type
            ptype = project.get('type', 'unknown')
            stats_by_type[ptype] = stats_by_type.get(ptype, 0) + 1
            
            # Par priorité
            priority = project.get('priority', 'unknown')
            stats_by_priority[priority] = stats_by_priority.get(priority, 0) + 1
        
        return {
            "total_projects": total_projects,
            "by_status": stats_by_status,
            "by_type": stats_by_type,
            "by_priority": stats_by_priority,
            "active_projects": len(self.get_active_projects()),
            "completion_rate": (stats_by_status.get(ProjectStatus.COMPLETED.value, 0) / max(1, total_projects)) * 100
        }

if __name__ == "__main__":
    print("📋 JARVIS CREATIVE PLANNER")
    print("==========================")
    
    planner = JarvisCreativePlanner()
    
    # Test avec une idée créative
    test_idea = {
        "type": "code",
        "content": "Créer un script Python pour automatiser la sauvegarde des projets créatifs de JARVIS sur le disque T7",
        "topic": "automation",
        "style": "pratique"
    }
    
    print("🧪 Test de planification...")
    project = planner.create_project_plan(test_idea)
    
    if project:
        print(f"✅ Projet planifié: {project['title']}")
        print(f"📋 Tâches: {len(project['tasks'])}")
        planner.add_project_to_backlog(project)
    
    # Afficher les stats
    stats = planner.get_creative_stats()
    print(f"\n📊 Stats planificateur: {stats}")
    
    # Agenda du jour
    agenda = planner.generate_daily_creative_agenda()
    print(f"\n📅 Agenda créatif: {agenda}")
