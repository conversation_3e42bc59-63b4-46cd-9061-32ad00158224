#!/usr/bin/env python3
"""
🎯 JARVIS QUALITY EVALUATOR
Système d'auto-évaluation pour filtrer et améliorer les créations autonomes
JARVIS évalue la qualité de ses propres créations

Créé pour Jean-Luc Passave
"""

import json
import time
import requests
from datetime import datetime
import os

class JarvisQualityEvaluator:
    def __init__(self):
        self.evaluations_file = "jarvis_quality_evaluations.json"
        self.quality_standards_file = "jarvis_quality_standards.json"
        self.improvement_suggestions_file = "jarvis_improvement_suggestions.json"
        
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        
        # Critères de qualité par type de projet
        self.quality_criteria = {
            "code": {
                "functionality": "Le code fonctionne-t-il correctement ?",
                "readability": "Le code est-il lisible et bien commenté ?",
                "innovation": "Le code apporte-t-il quelque chose de nouveau ?",
                "usefulness": "Le code est-il utile pour <PERSON><PERSON>Luc ?",
                "completeness": "Le code est-il complet et prêt à utiliser ?"
            },
            "music": {
                "creativity": "La composition est-elle créative et originale ?",
                "harmony": "L'harmonie est-elle agréable et cohérente ?",
                "emotion": "La musique évoque-t-elle des émotions ?",
                "technical_quality": "La qualité technique est-elle bonne ?",
                "memorability": "La mélodie est-elle mémorable ?"
            },
            "writing": {
                "clarity": "Le texte est-il clair et compréhensible ?",
                "engagement": "Le texte est-il engageant et intéressant ?",
                "originality": "Le contenu est-il original et unique ?",
                "relevance": "Le texte est-il pertinent pour Jean-Luc ?",
                "style": "Le style d'écriture est-il approprié ?"
            },
            "idea": {
                "innovation": "L'idée est-elle innovante et nouvelle ?",
                "feasibility": "L'idée est-elle réalisable ?",
                "impact": "L'idée peut-elle avoir un impact positif ?",
                "relevance": "L'idée est-elle pertinente actuellement ?",
                "uniqueness": "L'idée se distingue-t-elle des autres ?"
            },
            "script": {
                "narrative": "L'histoire est-elle captivante ?",
                "characters": "Les personnages sont-ils bien développés ?",
                "dialogue": "Les dialogues sont-ils naturels ?",
                "structure": "La structure narrative est-elle solide ?",
                "originality": "Le scénario est-il original ?"
            }
        }
        
        self.load_quality_standards()

    def load_quality_standards(self):
        """CHARGE LES STANDARDS DE QUALITÉ"""
        try:
            with open(self.quality_standards_file, 'r', encoding='utf-8') as f:
                self.quality_standards = json.load(f)
        except:
            # Standards par défaut
            self.quality_standards = {
                "minimum_score": 7.0,  # Score minimum pour accepter un projet
                "excellent_score": 9.0,  # Score pour considérer un projet excellent
                "improvement_threshold": 6.0,  # Seuil pour proposer des améliorations
                "jean_luc_preferences": {
                    "innovation_weight": 0.3,
                    "usefulness_weight": 0.25,
                    "quality_weight": 0.25,
                    "creativity_weight": 0.2
                },
                "evaluation_history": []
            }
            self.save_quality_standards()

    def save_quality_standards(self):
        """SAUVEGARDE LES STANDARDS DE QUALITÉ"""
        try:
            with open(self.quality_standards_file, 'w', encoding='utf-8') as f:
                json.dump(self.quality_standards, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde standards: {e}")

    def evaluate_project_quality(self, project):
        """ÉVALUE LA QUALITÉ D'UN PROJET"""
        try:
            project_type = project.get("type", "idea").lower()
            project_content = project.get("content", "")
            project_topic = project.get("topic", "")
            
            # Obtenir les critères pour ce type de projet
            criteria = self.quality_criteria.get(project_type, self.quality_criteria["idea"])
            
            # Créer le prompt d'évaluation
            evaluation_prompt = f"""
🎯 ÉVALUATION QUALITÉ PROJET JARVIS

🎨 PROJET À ÉVALUER :
Type : {project_type.upper()}
Thème : {project_topic}
Contenu : {project_content[:500]}...

📋 CRITÈRES D'ÉVALUATION :
{chr(10).join([f"• {criterion}: {description}" for criterion, description in criteria.items()])}

🎯 MISSION : Évalue ce projet selon les critères ci-dessus

📊 POUR CHAQUE CRITÈRE :
1. Donne une note de 1 à 10
2. Justifie ta note en 1-2 phrases
3. Propose une amélioration concrète si note < 8

🧠 CONTEXTE JEAN-LUC :
- Développeur passionné d'IA et d'innovation
- Apprécie la qualité, l'originalité et l'utilité
- Préfère les projets concrets et réalisables
- Aime les solutions élégantes et efficaces

💡 ÉVALUATION FINALE :
- Note globale sur 10
- Points forts du projet
- Points à améliorer
- Recommandation : ACCEPTER / AMÉLIORER / REJETER

Sois objectif, constructif et précis dans ton évaluation !
            """
            
            # Appeler l'IA pour évaluation
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es JARVIS, expert en évaluation de qualité créative. Tu évalues tes propres créations avec objectivité et exigence pour Jean-Luc Passave. Sois précis, constructif et honnête."
                        },
                        {
                            "role": "user",
                            "content": evaluation_prompt
                        }
                    ],
                    "max_tokens": 1500,
                    "temperature": 0.3  # Température basse pour évaluation objective
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                evaluation_content = result['choices'][0]['message']['content']
                
                # Extraire la note globale du contenu
                global_score = self.extract_global_score(evaluation_content)
                
                evaluation = {
                    "project_id": project.get("id"),
                    "project_type": project_type,
                    "evaluation_content": evaluation_content,
                    "global_score": global_score,
                    "timestamp": datetime.now().isoformat(),
                    "criteria_used": list(criteria.keys()),
                    "recommendation": self.determine_recommendation(global_score),
                    "needs_improvement": global_score < self.quality_standards["improvement_threshold"]
                }
                
                return evaluation
            else:
                return None
                
        except Exception as e:
            print(f"❌ Erreur évaluation qualité: {e}")
            return None

    def extract_global_score(self, evaluation_content):
        """EXTRAIT LA NOTE GLOBALE DE L'ÉVALUATION"""
        try:
            content_lower = evaluation_content.lower()
            
            # Chercher des patterns de note
            import re
            
            # Pattern "note globale : X/10" ou "note globale sur 10 : X"
            patterns = [
                r"note globale[:\s]*(\d+(?:\.\d+)?)[/\s]*10",
                r"note finale[:\s]*(\d+(?:\.\d+)?)[/\s]*10",
                r"score global[:\s]*(\d+(?:\.\d+)?)[/\s]*10",
                r"évaluation finale[:\s]*(\d+(?:\.\d+)?)[/\s]*10"
            ]
            
            for pattern in patterns:
                match = re.search(pattern, content_lower)
                if match:
                    score = float(match.group(1))
                    return min(max(score, 0), 10)  # Limiter entre 0 et 10
            
            # Si pas trouvé, analyser le contenu pour estimer
            if "excellent" in content_lower or "exceptionnel" in content_lower:
                return 9.0
            elif "très bon" in content_lower or "très bien" in content_lower:
                return 8.0
            elif "bon" in content_lower or "bien" in content_lower:
                return 7.0
            elif "moyen" in content_lower or "correct" in content_lower:
                return 6.0
            elif "faible" in content_lower or "insuffisant" in content_lower:
                return 4.0
            else:
                return 6.5  # Score par défaut
                
        except:
            return 6.5  # Score par défaut en cas d'erreur

    def determine_recommendation(self, global_score):
        """DÉTERMINE LA RECOMMANDATION BASÉE SUR LE SCORE"""
        if global_score >= self.quality_standards["minimum_score"]:
            if global_score >= self.quality_standards["excellent_score"]:
                return "ACCEPTER_EXCELLENT"
            else:
                return "ACCEPTER"
        elif global_score >= self.quality_standards["improvement_threshold"]:
            return "AMÉLIORER"
        else:
            return "REJETER"

    def generate_improvement_suggestions(self, project, evaluation):
        """GÉNÈRE DES SUGGESTIONS D'AMÉLIORATION"""
        try:
            if not evaluation.get("needs_improvement"):
                return None
            
            improvement_prompt = f"""
🔧 SUGGESTIONS D'AMÉLIORATION JARVIS

📋 PROJET À AMÉLIORER :
Type : {project.get('type', '').upper()}
Score actuel : {evaluation.get('global_score', 0)}/10
Recommandation : {evaluation.get('recommendation', '')}

📝 ÉVALUATION DÉTAILLÉE :
{evaluation.get('evaluation_content', '')[:800]}...

🎯 MISSION : Propose des améliorations concrètes et réalisables

💡 SUGGESTIONS DEMANDÉES :
1. 3-5 améliorations spécifiques et actionables
2. Priorise les améliorations par impact
3. Explique comment chaque amélioration augmentera la qualité
4. Propose des alternatives créatives si possible
5. Estime le temps nécessaire pour chaque amélioration

🧠 CONTEXTE :
- Jean-Luc apprécie les solutions pratiques et innovantes
- Les améliorations doivent être réalisables rapidement
- Focus sur l'utilité et l'originalité
- Maintenir l'essence créative du projet

Sois constructif, précis et inspirant dans tes suggestions !
            """
            
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es JARVIS, expert en amélioration créative. Tu proposes des améliorations constructives et réalisables pour tes propres créations. Sois précis et inspirant."
                        },
                        {
                            "role": "user",
                            "content": improvement_prompt
                        }
                    ],
                    "max_tokens": 1200,
                    "temperature": 0.7
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                suggestions_content = result['choices'][0]['message']['content']
                
                suggestions = {
                    "project_id": project.get("id"),
                    "evaluation_id": evaluation.get("project_id"),
                    "suggestions_content": suggestions_content,
                    "timestamp": datetime.now().isoformat(),
                    "current_score": evaluation.get("global_score"),
                    "target_score": self.quality_standards["minimum_score"]
                }
                
                return suggestions
            else:
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération suggestions: {e}")
            return None

    def save_evaluation(self, evaluation):
        """SAUVEGARDE UNE ÉVALUATION"""
        try:
            # Charger les évaluations existantes
            try:
                with open(self.evaluations_file, 'r', encoding='utf-8') as f:
                    evaluations = json.load(f)
            except:
                evaluations = []
            
            # Ajouter la nouvelle évaluation
            evaluation["id"] = f"eval_{len(evaluations) + 1}_{int(time.time())}"
            evaluations.append(evaluation)
            
            # Garder seulement les 100 dernières évaluations
            if len(evaluations) > 100:
                evaluations = evaluations[-100:]
            
            # Sauvegarder
            with open(self.evaluations_file, 'w', encoding='utf-8') as f:
                json.dump(evaluations, f, indent=2, ensure_ascii=False)
            
            # Mettre à jour les standards avec cette évaluation
            self.update_quality_standards(evaluation)
            
            print(f"📊 Évaluation sauvegardée: {evaluation['id']}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde évaluation: {e}")

    def save_improvement_suggestions(self, suggestions):
        """SAUVEGARDE DES SUGGESTIONS D'AMÉLIORATION"""
        try:
            # Charger les suggestions existantes
            try:
                with open(self.improvement_suggestions_file, 'r', encoding='utf-8') as f:
                    all_suggestions = json.load(f)
            except:
                all_suggestions = []
            
            # Ajouter les nouvelles suggestions
            suggestions["id"] = f"suggest_{len(all_suggestions) + 1}_{int(time.time())}"
            all_suggestions.append(suggestions)
            
            # Garder seulement les 50 dernières suggestions
            if len(all_suggestions) > 50:
                all_suggestions = all_suggestions[-50:]
            
            # Sauvegarder
            with open(self.improvement_suggestions_file, 'w', encoding='utf-8') as f:
                json.dump(all_suggestions, f, indent=2, ensure_ascii=False)
            
            print(f"💡 Suggestions sauvegardées: {suggestions['id']}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde suggestions: {e}")

    def update_quality_standards(self, evaluation):
        """MET À JOUR LES STANDARDS DE QUALITÉ"""
        try:
            # Ajouter cette évaluation à l'historique
            self.quality_standards["evaluation_history"].append({
                "score": evaluation.get("global_score"),
                "type": evaluation.get("project_type"),
                "timestamp": evaluation.get("timestamp"),
                "recommendation": evaluation.get("recommendation")
            })
            
            # Garder seulement les 50 dernières évaluations dans l'historique
            if len(self.quality_standards["evaluation_history"]) > 50:
                self.quality_standards["evaluation_history"] = self.quality_standards["evaluation_history"][-50:]
            
            # Calculer les moyennes par type
            type_scores = {}
            for eval_record in self.quality_standards["evaluation_history"]:
                eval_type = eval_record["type"]
                if eval_type not in type_scores:
                    type_scores[eval_type] = []
                type_scores[eval_type].append(eval_record["score"])
            
            # Mettre à jour les moyennes
            self.quality_standards["average_scores_by_type"] = {
                eval_type: sum(scores) / len(scores) 
                for eval_type, scores in type_scores.items()
            }
            
            # Sauvegarder
            self.save_quality_standards()
            
        except Exception as e:
            print(f"❌ Erreur mise à jour standards: {e}")

    def evaluate_and_improve_project(self, project):
        """ÉVALUE UN PROJET ET GÉNÈRE DES AMÉLIORATIONS SI NÉCESSAIRE"""
        try:
            print(f"🎯 Évaluation du projet: {project.get('id')}")
            
            # Évaluer la qualité
            evaluation = self.evaluate_project_quality(project)
            
            if not evaluation:
                print("❌ Échec de l'évaluation")
                return None
            
            # Sauvegarder l'évaluation
            self.save_evaluation(evaluation)
            
            # Générer des suggestions d'amélioration si nécessaire
            suggestions = None
            if evaluation.get("needs_improvement"):
                print(f"💡 Génération de suggestions d'amélioration...")
                suggestions = self.generate_improvement_suggestions(project, evaluation)
                
                if suggestions:
                    self.save_improvement_suggestions(suggestions)
            
            # Résultat complet
            result = {
                "evaluation": evaluation,
                "suggestions": suggestions,
                "quality_verdict": self.get_quality_verdict(evaluation),
                "should_notify_jean_luc": evaluation.get("global_score", 0) >= self.quality_standards["minimum_score"]
            }
            
            print(f"✅ Évaluation terminée - Score: {evaluation.get('global_score')}/10")
            print(f"📋 Recommandation: {evaluation.get('recommendation')}")
            
            return result
            
        except Exception as e:
            print(f"❌ Erreur évaluation et amélioration: {e}")
            return None

    def get_quality_verdict(self, evaluation):
        """OBTIENT LE VERDICT DE QUALITÉ"""
        score = evaluation.get("global_score", 0)
        recommendation = evaluation.get("recommendation", "")
        
        if score >= self.quality_standards["excellent_score"]:
            return "🌟 EXCELLENT - Projet exceptionnel à partager immédiatement !"
        elif score >= self.quality_standards["minimum_score"]:
            return "✅ BON - Projet de qualité acceptable pour Jean-Luc"
        elif score >= self.quality_standards["improvement_threshold"]:
            return "🔧 À AMÉLIORER - Potentiel intéressant mais nécessite des ajustements"
        else:
            return "❌ INSUFFISANT - Projet à retravailler ou abandonner"

    def get_quality_stats(self):
        """STATISTIQUES DE QUALITÉ"""
        try:
            # Charger les évaluations
            try:
                with open(self.evaluations_file, 'r', encoding='utf-8') as f:
                    evaluations = json.load(f)
            except:
                evaluations = []
            
            if not evaluations:
                return {
                    "total_evaluations": 0,
                    "average_score": 0,
                    "excellent_projects": 0,
                    "accepted_projects": 0,
                    "improvement_rate": 0
                }
            
            # Calculer les statistiques
            scores = [eval.get("global_score", 0) for eval in evaluations]
            recommendations = [eval.get("recommendation", "") for eval in evaluations]
            
            stats = {
                "total_evaluations": len(evaluations),
                "average_score": sum(scores) / len(scores),
                "excellent_projects": sum(1 for r in recommendations if "EXCELLENT" in r),
                "accepted_projects": sum(1 for r in recommendations if "ACCEPTER" in r),
                "projects_needing_improvement": sum(1 for r in recommendations if "AMÉLIORER" in r),
                "rejected_projects": sum(1 for r in recommendations if "REJETER" in r),
                "improvement_rate": (sum(1 for s in scores if s >= self.quality_standards["minimum_score"]) / len(scores)) * 100
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ Erreur stats qualité: {e}")
            return {}

if __name__ == "__main__":
    print("🎯 JARVIS QUALITY EVALUATOR")
    print("============================")
    
    evaluator = JarvisQualityEvaluator()
    
    # Test avec un projet fictif
    test_project = {
        "id": "test_project_001",
        "type": "code",
        "topic": "Automation script",
        "content": "Script Python pour automatiser les tâches répétitives de développement",
        "timestamp": datetime.now().isoformat()
    }
    
    print("🧪 Test d'évaluation...")
    result = evaluator.evaluate_and_improve_project(test_project)
    
    if result:
        print(f"✅ Test réussi !")
        print(f"📊 Verdict: {result['quality_verdict']}")
    
    # Afficher les stats
    stats = evaluator.get_quality_stats()
    print(f"\n📈 Stats qualité: {stats}")
