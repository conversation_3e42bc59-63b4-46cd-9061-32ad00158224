#!/usr/bin/env python3
"""
💾 MÉMOIRE CONTINUE OPTIMISÉE JARVIS
Résout les ralentissements de la mémoire thermique avec sauvegarde continue
"""

import os
import json
import threading
import time
import gzip
import pickle
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

class MemoireContinueOptimisee:
    def __init__(self, memoire_path="thermal_memory_persistent.json"):
        self.memoire_path = memoire_path
        self.memoire_optimisee_path = f"{memoire_path}.optimized"
        self.cache_memoire = {}
        self.derniere_sauvegarde = 0
        self.sauvegarde_en_cours = False
        self.thread_pool = ThreadPoolExecutor(max_workers=2)
        self.lock = threading.Lock()
        
        # Configuration optimisation
        self.config = {
            'intervalle_sauvegarde': 30,  # secondes
            'taille_max_cache': 1000,    # éléments
            'compression_auto': True,
            'sauvegarde_asynchrone': True,
            'nettoyage_auto': True
        }
        
        print("💾 Mémoire continue optimisée initialisée")
    
    def charger_memoire_optimisee(self):
        """Charge la mémoire avec optimisations"""
        try:
            # Essayer d'abord la version optimisée
            if os.path.exists(self.memoire_optimisee_path):
                with gzip.open(self.memoire_optimisee_path, 'rb') as f:
                    data = pickle.load(f)
                print("✅ Mémoire optimisée chargée")
                return data
            
            # Fallback sur version normale
            elif os.path.exists(self.memoire_path):
                with open(self.memoire_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Optimiser immédiatement
                self.sauvegarder_memoire_optimisee(data)
                print("✅ Mémoire normale chargée et optimisée")
                return data
            
            else:
                print("⚠️ Aucune mémoire trouvée, création nouvelle")
                return {}
                
        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
            return {}
    
    def sauvegarder_memoire_optimisee(self, data, force=False):
        """Sauvegarde optimisée asynchrone"""
        try:
            current_time = time.time()
            
            # Vérifier si sauvegarde nécessaire
            if not force and (current_time - self.derniere_sauvegarde) < self.config['intervalle_sauvegarde']:
                return False
            
            # Éviter les sauvegardes simultanées
            if self.sauvegarde_en_cours:
                return False
            
            self.sauvegarde_en_cours = True
            
            if self.config['sauvegarde_asynchrone']:
                # Sauvegarde asynchrone
                self.thread_pool.submit(self._sauvegarder_async, data, current_time)
            else:
                # Sauvegarde synchrone
                self._sauvegarder_sync(data, current_time)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde optimisée: {e}")
            self.sauvegarde_en_cours = False
            return False
    
    def _sauvegarder_async(self, data, timestamp):
        """Sauvegarde asynchrone en arrière-plan"""
        try:
            with self.lock:
                # Compression et sauvegarde
                with gzip.open(self.memoire_optimisee_path, 'wb') as f:
                    pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
                
                # Sauvegarde JSON normale aussi (backup)
                with open(self.memoire_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, separators=(',', ':'))
                
                self.derniere_sauvegarde = timestamp
                
                # Statistiques
                size_optimized = os.path.getsize(self.memoire_optimisee_path)
                size_normal = os.path.getsize(self.memoire_path)
                ratio = (1 - size_optimized / size_normal) * 100
                
                print(f"💾 Sauvegarde async: {size_normal} → {size_optimized} bytes ({ratio:.1f}% économie)")
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde async: {e}")
        finally:
            self.sauvegarde_en_cours = False
    
    def _sauvegarder_sync(self, data, timestamp):
        """Sauvegarde synchrone rapide"""
        try:
            # Sauvegarde JSON rapide
            with open(self.memoire_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, separators=(',', ':'))
            
            self.derniere_sauvegarde = timestamp
            print("💾 Sauvegarde sync rapide effectuée")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde sync: {e}")
        finally:
            self.sauvegarde_en_cours = False
    
    def ajouter_memoire_optimisee(self, cle, valeur):
        """Ajoute à la mémoire avec optimisations"""
        try:
            # Cache en mémoire
            self.cache_memoire[cle] = {
                'valeur': valeur,
                'timestamp': time.time(),
                'acces': 1
            }
            
            # Nettoyage automatique du cache
            if len(self.cache_memoire) > self.config['taille_max_cache']:
                self._nettoyer_cache()
            
            # Sauvegarde si nécessaire
            if self.config['compression_auto']:
                memoire_complete = self.charger_memoire_optimisee()
                memoire_complete[cle] = valeur
                self.sauvegarder_memoire_optimisee(memoire_complete)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur ajout mémoire: {e}")
            return False
    
    def recuperer_memoire_optimisee(self, cle):
        """Récupère de la mémoire avec cache"""
        try:
            # Vérifier cache d'abord
            if cle in self.cache_memoire:
                self.cache_memoire[cle]['acces'] += 1
                return self.cache_memoire[cle]['valeur']
            
            # Charger depuis fichier
            memoire_complete = self.charger_memoire_optimisee()
            if cle in memoire_complete:
                # Ajouter au cache
                self.cache_memoire[cle] = {
                    'valeur': memoire_complete[cle],
                    'timestamp': time.time(),
                    'acces': 1
                }
                return memoire_complete[cle]
            
            return None
            
        except Exception as e:
            print(f"❌ Erreur récupération mémoire: {e}")
            return None
    
    def _nettoyer_cache(self):
        """Nettoie le cache selon LRU"""
        try:
            if len(self.cache_memoire) <= self.config['taille_max_cache']:
                return
            
            # Trier par accès et timestamp
            items_sorted = sorted(
                self.cache_memoire.items(),
                key=lambda x: (x[1]['acces'], x[1]['timestamp'])
            )
            
            # Garder seulement les plus récents/utilisés
            items_to_keep = items_sorted[-self.config['taille_max_cache']//2:]
            
            self.cache_memoire = dict(items_to_keep)
            print(f"🧹 Cache nettoyé: {len(self.cache_memoire)} éléments conservés")
            
        except Exception as e:
            print(f"❌ Erreur nettoyage cache: {e}")
    
    def optimiser_memoire_existante(self):
        """Optimise la mémoire thermique existante"""
        try:
            if not os.path.exists(self.memoire_path):
                return False
            
            # Charger mémoire existante
            with open(self.memoire_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Statistiques avant
            size_before = os.path.getsize(self.memoire_path)
            
            # Optimisations
            data_optimized = self._optimiser_structure(data)
            
            # Sauvegarder version optimisée
            self.sauvegarder_memoire_optimisee(data_optimized, force=True)
            
            # Statistiques après
            size_after = os.path.getsize(self.memoire_optimisee_path)
            ratio = (1 - size_after / size_before) * 100
            
            print(f"🚀 Optimisation terminée: {size_before} → {size_after} bytes ({ratio:.1f}% économie)")
            return True
            
        except Exception as e:
            print(f"❌ Erreur optimisation: {e}")
            return False
    
    def _optimiser_structure(self, data):
        """Optimise la structure des données"""
        try:
            if not isinstance(data, dict):
                return data
            
            optimized = {}
            
            for key, value in data.items():
                # Optimiser les clés longues
                if len(str(key)) > 50:
                    key_hash = str(hash(key))[:10]
                    optimized[key_hash] = value
                else:
                    optimized[key] = value
                
                # Optimiser les valeurs
                if isinstance(value, str) and len(value) > 1000:
                    # Compresser les longues chaînes
                    optimized[key] = gzip.compress(value.encode('utf-8'))
                elif isinstance(value, dict):
                    optimized[key] = self._optimiser_structure(value)
            
            return optimized
            
        except Exception as e:
            print(f"❌ Erreur optimisation structure: {e}")
            return data
    
    def get_stats_memoire(self):
        """Retourne les statistiques de la mémoire"""
        try:
            stats = {
                'cache_size': len(self.cache_memoire),
                'derniere_sauvegarde': self.derniere_sauvegarde,
                'sauvegarde_en_cours': self.sauvegarde_en_cours,
                'config': self.config
            }
            
            # Tailles fichiers
            if os.path.exists(self.memoire_path):
                stats['taille_normale'] = os.path.getsize(self.memoire_path)
            
            if os.path.exists(self.memoire_optimisee_path):
                stats['taille_optimisee'] = os.path.getsize(self.memoire_optimisee_path)
                if 'taille_normale' in stats:
                    stats['ratio_compression'] = (1 - stats['taille_optimisee'] / stats['taille_normale']) * 100
            
            return stats
            
        except Exception as e:
            print(f"❌ Erreur stats mémoire: {e}")
            return {}

# Instance globale
memoire_optimisee = MemoireContinueOptimisee()

def optimiser_memoire_continue_jarvis():
    """Optimise la mémoire continue de JARVIS"""
    try:
        # Optimiser la mémoire existante
        optimized = memoire_optimisee.optimiser_memoire_existante()
        
        # Statistiques
        stats = memoire_optimisee.get_stats_memoire()
        
        return f"""
        <div style="background: linear-gradient(45deg, #9c27b0, #673ab7); color: white; padding: 20px; border-radius: 15px;">
            <h3>💾 MÉMOIRE CONTINUE OPTIMISÉE</h3>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <h4>🚀 OPTIMISATION</h4>
                <p>{'✅ Mémoire thermique optimisée' if optimized else '⚠️ Optimisation non nécessaire'}</p>
                <p>✅ Cache mémoire: {stats.get('cache_size', 0)} éléments</p>
                <p>✅ Sauvegarde asynchrone: {'Active' if stats.get('config', {}).get('sauvegarde_asynchrone') else 'Inactive'}</p>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <h4>📊 PERFORMANCE</h4>
                <p>💾 Taille normale: {stats.get('taille_normale', 0) / 1024:.1f} KB</p>
                <p>⚡ Taille optimisée: {stats.get('taille_optimisee', 0) / 1024:.1f} KB</p>
                <p>🎯 Compression: {stats.get('ratio_compression', 0):.1f}% d'économie</p>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <h4>⚙️ CONFIGURATION</h4>
                <p>🔄 Intervalle sauvegarde: {stats.get('config', {}).get('intervalle_sauvegarde', 30)}s</p>
                <p>🗂️ Taille max cache: {stats.get('config', {}).get('taille_max_cache', 1000)} éléments</p>
                <p>🗜️ Compression auto: {'✅' if stats.get('config', {}).get('compression_auto') else '❌'}</p>
            </div>
            
            <p style="text-align: center; margin-top: 20px; font-weight: bold;">
                🎯 MÉMOIRE CONTINUE ULTRA-RAPIDE !
            </p>
        </div>
        """
        
    except Exception as e:
        return f"""
        <div style="background: linear-gradient(45deg, #f44336, #d32f2f); color: white; padding: 15px; border-radius: 10px;">
            <h4>❌ ERREUR OPTIMISATION MÉMOIRE</h4>
            <p>Impossible d'optimiser la mémoire: {str(e)}</p>
        </div>
        """
