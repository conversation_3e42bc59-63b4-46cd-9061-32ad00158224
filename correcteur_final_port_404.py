#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR FINAL PORT 404 - JEAN-LUC PASSAVE
Corrige définitivement le problème de port et erreur 404
"""

import subprocess
import requests
import time
import re
import os
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔧 [{timestamp}] {message}")

def detecter_port_jarvis_actuel():
    """Détecte sur quel port JARVIS fonctionne vraiment"""
    log("🔍 DÉTECTION PORT JARVIS ACTUEL")
    
    ports_actifs = []
    
    for port in range(7860, 7880):
        try:
            response = requests.get(f"http://localhost:{port}", timeout=2)
            if response.status_code == 200 and "JARVIS" in response.text:
                ports_actifs.append(port)
                log(f"✅ JARVIS trouvé sur port {port}")
        except:
            pass
    
    return ports_actifs

def corriger_port_dans_fichier():
    """Corrige le port dans le fichier pour forcer 7860"""
    log("🔧 CORRECTION PORT DANS FICHIER")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Forcer port 7860
        contenu = re.sub(r'server_port=\d+', 'server_port=7860', contenu)
        
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(contenu)
        
        log("✅ Port forcé à 7860 dans le fichier")
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction port: {e}")
        return False

def arreter_tous_jarvis():
    """Arrête TOUS les processus JARVIS"""
    log("🛑 ARRÊT COMPLET TOUS JARVIS")
    
    try:
        # Méthode 1: pkill
        subprocess.run(["pkill", "-f", "jarvis_interface_propre.py"], capture_output=True)
        time.sleep(3)
        
        # Méthode 2: kill par PID
        result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
        
        pids_tues = 0
        for ligne in result.stdout.split('\n'):
            if 'jarvis_interface_propre.py' in ligne.lower() and 'python' in ligne:
                parts = ligne.split()
                if len(parts) > 1:
                    pid = parts[1]
                    try:
                        subprocess.run(['kill', '-9', pid], capture_output=True)
                        pids_tues += 1
                    except:
                        pass
        
        # Méthode 3: kill par port
        for port in range(7860, 7880):
            try:
                result = subprocess.run(['lsof', '-ti', f':{port}'], capture_output=True, text=True)
                if result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    for pid in pids:
                        if pid:
                            subprocess.run(['kill', '-9', pid], capture_output=True)
                            pids_tues += 1
            except:
                pass
        
        time.sleep(5)
        log(f"✅ {pids_tues} processus arrêtés")
        return True
        
    except Exception as e:
        log(f"❌ Erreur arrêt: {e}")
        return False

def verifier_port_libre():
    """Vérifie que le port 7860 est libre"""
    log("🔍 VÉRIFICATION PORT 7860 LIBRE")
    
    try:
        response = requests.get("http://localhost:7860", timeout=2)
        log("❌ Port 7860 encore occupé")
        return False
    except:
        log("✅ Port 7860 libre")
        return True

def lancer_jarvis_port_7860():
    """Lance JARVIS spécifiquement sur port 7860"""
    log("🚀 LANCEMENT JARVIS PORT 7860")
    
    try:
        # Commande de lancement
        if os.path.exists("venv_deepseek/bin/activate"):
            cmd = "source venv_deepseek/bin/activate && python jarvis_interface_propre.py"
        else:
            cmd = "python3 jarvis_interface_propre.py"
        
        log(f"💻 Commande: {cmd}")
        
        # Lancer
        process = subprocess.Popen(cmd, shell=True, cwd=".")
        log(f"🚀 JARVIS lancé (PID: {process.pid})")
        
        # Attendre démarrage sur port 7860
        for i in range(30):
            time.sleep(1)
            try:
                response = requests.get("http://localhost:7860", timeout=2)
                if response.status_code == 200 and "JARVIS" in response.text:
                    log("✅ JARVIS opérationnel sur port 7860")
                    return True
            except:
                pass
        
        log("⚠️  JARVIS prend du temps à démarrer")
        return True
        
    except Exception as e:
        log(f"❌ Erreur lancement: {e}")
        return False

def tester_fonctionnement_complet():
    """Teste le fonctionnement complet de JARVIS"""
    log("🧪 TEST FONCTIONNEMENT COMPLET")
    
    try:
        # Test page principale
        response = requests.get("http://localhost:7860", timeout=5)
        if response.status_code != 200:
            log(f"❌ Page principale: {response.status_code}")
            return False
        
        if "JARVIS" not in response.text:
            log("❌ Contenu JARVIS manquant")
            return False
        
        log("✅ Page principale OK")
        
        # Test routes essentielles
        routes_essentielles = ["/config", "/theme.css"]
        
        for route in routes_essentielles:
            try:
                response = requests.get(f"http://localhost:7860{route}", timeout=3)
                if response.status_code == 200:
                    log(f"✅ Route {route} OK")
                else:
                    log(f"⚠️  Route {route}: {response.status_code}")
            except Exception as e:
                log(f"❌ Route {route}: {e}")
        
        log("✅ JARVIS fonctionne correctement")
        return True
        
    except Exception as e:
        log(f"❌ Erreur test: {e}")
        return False

def correction_complete_404():
    """Correction complète du problème 404"""
    log("🔧 CORRECTION COMPLÈTE PROBLÈME 404")
    print("=" * 60)
    
    # 1. Détecter ports actuels
    log("ÉTAPE 1: Détection ports actuels")
    ports_actuels = detecter_port_jarvis_actuel()
    if ports_actuels:
        log(f"JARVIS actif sur ports: {ports_actuels}")
    else:
        log("Aucun JARVIS détecté")
    
    # 2. Arrêter tous les processus
    log("ÉTAPE 2: Arrêt complet")
    if not arreter_tous_jarvis():
        log("❌ Échec arrêt processus")
        return False
    
    # 3. Vérifier port libre
    log("ÉTAPE 3: Vérification port libre")
    if not verifier_port_libre():
        log("❌ Port 7860 encore occupé")
        # Forcer libération
        subprocess.run(['lsof', '-ti', ':7860'], capture_output=True)
        time.sleep(2)
    
    # 4. Corriger fichier
    log("ÉTAPE 4: Correction fichier")
    if not corriger_port_dans_fichier():
        log("❌ Échec correction fichier")
        return False
    
    # 5. Lancer JARVIS
    log("ÉTAPE 5: Lancement JARVIS")
    if not lancer_jarvis_port_7860():
        log("❌ Échec lancement JARVIS")
        return False
    
    # 6. Test complet
    log("ÉTAPE 6: Test fonctionnement")
    time.sleep(5)
    if tester_fonctionnement_complet():
        log("🎉 CORRECTION 404 RÉUSSIE !")
        log("🌐 JARVIS accessible: http://localhost:7860")
        
        # Vérifier processus final
        result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
        processus_jarvis = [l for l in result.stdout.split('\n') if 'jarvis_interface_propre.py' in l.lower()]
        log(f"✅ {len(processus_jarvis)} processus JARVIS actifs")
        
        return True
    else:
        log("❌ Test fonctionnement échoué")
        return False

def ouvrir_navigateur():
    """Ouvre le navigateur sur la bonne URL"""
    log("🌐 OUVERTURE NAVIGATEUR")
    
    try:
        subprocess.run(["open", "http://localhost:7860"], check=True)
        log("✅ Navigateur ouvert sur http://localhost:7860")
    except:
        log("⚠️  Impossible d'ouvrir automatiquement le navigateur")
        log("🌐 Ouvrez manuellement: http://localhost:7860")

if __name__ == "__main__":
    print("🔧 CORRECTEUR FINAL PORT 404")
    print("Corrige définitivement le problème de port")
    print("=" * 50)
    
    if correction_complete_404():
        print("\n🎉 SUCCÈS TOTAL !")
        print("JARVIS est maintenant accessible sur http://localhost:7860")
        print("Le problème 404 est résolu définitivement")
        
        # Ouvrir navigateur
        ouvrir_navigateur()
        
    else:
        print("\n❌ ÉCHEC de la correction")
        print("Problème persistant - vérification manuelle nécessaire")
