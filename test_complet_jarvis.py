#!/usr/bin/env python3
"""
🔍 TEST COMPLET JARVIS
Vérifie tous les systèmes de <PERSON>
"""

import requests
import json
import os
import time
from datetime import datetime

def test_interface_jarvis():
    """Test interface JARVIS"""
    try:
        response = requests.get("http://localhost:7867", timeout=10)
        if response.status_code == 200 and len(response.text) > 1000:
            return "✅ Interface JARVIS opérationnelle"
        else:
            return f"❌ Interface JARVIS problème: {response.status_code}, taille: {len(response.text)}"
    except Exception as e:
        return f"❌ Interface JARVIS non accessible: {e}"

def test_deepseek_r1():
    """Test DeepSeek R1 8B"""
    try:
        response = requests.get("http://localhost:8000/v1/models", timeout=10)
        if response.status_code == 200:
            return "✅ DeepSeek R1 8B opérationnel"
        else:
            return f"❌ DeepSeek R1 8B erreur: {response.status_code}"
    except Exception as e:
        return f"❌ DeepSeek R1 8B non accessible: {e}"

def test_memoire_thermique():
    """Test mémoire thermique"""
    try:
        if os.path.exists("thermal_memory_persistent.json"):
            with open("thermal_memory_persistent.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conversations = data.get('conversations', [])
            size_mb = os.path.getsize("thermal_memory_persistent.json") / 1024 / 1024
            version = data.get('version', 'inconnue')
            
            return f"✅ Mémoire thermique: {len(conversations)} conversations, {size_mb:.2f} MB, v{version}"
        else:
            return "❌ Fichier mémoire thermique non trouvé"
    except Exception as e:
        return f"❌ Erreur mémoire thermique: {e}"

def test_t7_sync():
    """Test synchronisation T7"""
    try:
        t7_path = "/Volumes/T7/JARVIS_BRAIN"
        if os.path.exists(t7_path):
            files = os.listdir(t7_path)
            total_size = sum(os.path.getsize(os.path.join(t7_path, f)) for f in files if os.path.isfile(os.path.join(t7_path, f)))
            size_mb = total_size / 1024 / 1024
            return f"✅ T7 Sync: {len(files)} fichiers, {size_mb:.2f} MB"
        else:
            return "❌ T7 JARVIS_BRAIN non accessible"
    except Exception as e:
        return f"❌ Erreur T7 Sync: {e}"

def test_systeme_neuronal():
    """Test système neuronal"""
    try:
        from jarvis_systeme_neuronal_etages import systeme_neuronal
        stats = systeme_neuronal.get_stats_systeme_neuronal()
        
        total_neurones = stats.get('total_neurones', 0)
        active_neurones = stats.get('active_neurones', 0)
        etages = stats.get('total_etages', 0)
        
        return f"✅ Système neuronal: {total_neurones:,} neurones, {etages} étages, {active_neurones:,} actifs"
    except Exception as e:
        return f"❌ Erreur système neuronal: {e}"

def test_accelerateurs():
    """Test accélérateurs"""
    try:
        from jarvis_accelerateur_global_unifie import accelerateur_global
        stats = accelerateur_global.get_stats_acceleration_globale()
        
        compression = stats.get('compression_ratio', 0)
        conversations = stats.get('conversations_indexees', 0)
        
        return f"✅ Accélérateurs: {compression:.1f}% compression, {conversations} conversations indexées"
    except Exception as e:
        return f"❌ Erreur accélérateurs: {e}"

def test_chat_deepseek():
    """Test conversation avec DeepSeek"""
    try:
        payload = {
            "model": "deepseek-ai/DeepSeek-R1-0528",
            "messages": [
                {"role": "system", "content": "Tu es JARVIS, assistant de Jean-Luc Passave. Réponds brièvement."},
                {"role": "user", "content": "Bonjour JARVIS, test de fonctionnement"}
            ],
            "max_tokens": 100,
            "temperature": 0.7
        }
        
        response = requests.post("http://localhost:8000/v1/chat/completions", json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            reply = data['choices'][0]['message']['content']
            return f"✅ Chat DeepSeek: Réponse reçue ({len(reply)} caractères)"
        else:
            return f"❌ Chat DeepSeek erreur: {response.status_code}"
            
    except Exception as e:
        return f"❌ Chat DeepSeek non accessible: {e}"

def main():
    """Test complet de tous les systèmes JARVIS"""
    print("🔍 TEST COMPLET JARVIS - Jean-Luc Passave")
    print("=" * 60)
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Tests des composants
    tests = [
        ("🌐 Interface JARVIS", test_interface_jarvis),
        ("🧠 DeepSeek R1 8B", test_deepseek_r1),
        ("💾 Mémoire Thermique", test_memoire_thermique),
        ("📡 Synchronisation T7", test_t7_sync),
        ("🧠 Système Neuronal", test_systeme_neuronal),
        ("🚀 Accélérateurs", test_accelerateurs),
        ("💬 Chat DeepSeek", test_chat_deepseek)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"🔄 Test {name}...")
        result = test_func()
        results.append((name, result))
        print(f"   {result}")
        time.sleep(2)  # Pause entre tests
    
    print()
    print("📊 RÉSUMÉ FINAL:")
    print("-" * 40)
    
    success_count = 0
    for name, result in results:
        status = "✅" if result.startswith("✅") else "❌"
        print(f"{status} {name}")
        if status == "✅":
            success_count += 1
    
    print()
    print(f"🎯 STATUT GLOBAL: {success_count}/{len(tests)} composants opérationnels")
    
    if success_count == len(tests):
        print("🎉 JARVIS COMPLÈTEMENT OPÉRATIONNEL !")
        print("🌐 Interface: http://localhost:7867")
        print("🧠 DeepSeek: http://localhost:8000")
        print("💾 Mémoire: Optimisée et synchronisée")
        print("🚀 Tous systèmes: GO !")
    elif success_count >= len(tests) // 2:
        print("⚠️ JARVIS PARTIELLEMENT OPÉRATIONNEL")
        print("🔧 Quelques composants nécessitent attention")
    else:
        print("❌ JARVIS EN PANNE - INTERVENTION REQUISE")
        print("🚨 Plusieurs systèmes critiques défaillants")
    
    print()
    print("📋 Pour accéder à JARVIS:")
    print("   🌐 Interface: http://localhost:7867")
    print("   🧠 API DeepSeek: http://localhost:8000")
    
    return success_count == len(tests)

if __name__ == "__main__":
    main()
