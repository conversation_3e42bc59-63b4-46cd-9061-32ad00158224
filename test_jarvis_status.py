#!/usr/bin/env python3
"""
🔍 TEST STATUT JARVIS
Vérifie l'état de tous les composants JARVIS
"""

import requests
import time
import json
import os
from datetime import datetime

def test_deepseek_r1():
    """Test DeepSeek R1 8B"""
    try:
        response = requests.get("http://localhost:8000/v1/models", timeout=5)
        if response.status_code == 200:
            return "✅ DeepSeek R1 8B opérationnel"
        else:
            return f"❌ DeepSeek R1 8B erreur: {response.status_code}"
    except Exception as e:
        return f"❌ DeepSeek R1 8B non accessible: {e}"

def test_jarvis_interface():
    """Test interface JARVIS"""
    try:
        response = requests.get("http://localhost:7863", timeout=10)
        if response.status_code == 200:
            return "✅ Interface JARVIS opérationnelle"
        else:
            return f"❌ Interface JARVIS erreur: {response.status_code}"
    except Exception as e:
        return f"❌ Interface JARVIS non accessible: {e}"

def test_thermal_memory():
    """Test mémoire thermique"""
    try:
        if os.path.exists("thermal_memory_persistent.json"):
            with open("thermal_memory_persistent.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conversations = data.get('conversations', [])
            size_mb = os.path.getsize("thermal_memory_persistent.json") / 1024 / 1024
            
            return f"✅ Mémoire thermique: {len(conversations)} conversations, {size_mb:.2f} MB"
        else:
            return "❌ Fichier mémoire thermique non trouvé"
    except Exception as e:
        return f"❌ Erreur mémoire thermique: {e}"

def test_t7_sync():
    """Test synchronisation T7"""
    try:
        t7_path = "/Volumes/T7/JARVIS_BRAIN"
        if os.path.exists(t7_path):
            files = os.listdir(t7_path)
            return f"✅ T7 Sync: {len(files)} fichiers synchronisés"
        else:
            return "❌ T7 JARVIS_BRAIN non accessible"
    except Exception as e:
        return f"❌ Erreur T7 Sync: {e}"

def test_neural_system():
    """Test système neuronal"""
    try:
        # Importer et tester le système neuronal
        from jarvis_systeme_neuronal_etages import systeme_neuronal
        stats = systeme_neuronal.get_stats_systeme_neuronal()
        
        total_neurones = stats.get('total_neurones', 0)
        active_neurones = stats.get('active_neurones', 0)
        
        return f"✅ Système neuronal: {total_neurones:,} neurones ({active_neurones:,} actifs)"
    except Exception as e:
        return f"❌ Erreur système neuronal: {e}"

def test_neural_86b():
    """Test système neuronal 86B"""
    try:
        from jarvis_systeme_neuronal_86_milliards import systeme_neuronal_86b
        stats = systeme_neuronal_86b.get_neural_stats()
        
        total_neurones = stats.get('total_neurones', 0)
        active_neurones = stats.get('active_neurones', 0)
        
        return f"✅ Système 86B: {total_neurones:,} neurones ({active_neurones:,} actifs)"
    except Exception as e:
        return f"❌ Erreur système 86B: {e}"

def main():
    """Test complet JARVIS"""
    print("🔍 TEST COMPLET STATUT JARVIS")
    print("=" * 50)
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Tests des composants
    tests = [
        ("🧠 DeepSeek R1 8B", test_deepseek_r1),
        ("🌐 Interface JARVIS", test_jarvis_interface),
        ("💾 Mémoire Thermique", test_thermal_memory),
        ("📡 Synchronisation T7", test_t7_sync),
        ("🧠 Système Neuronal", test_neural_system),
        ("🧠 Système 86B", test_neural_86b)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"🔄 Test {name}...")
        result = test_func()
        results.append((name, result))
        print(f"   {result}")
        time.sleep(1)
    
    print()
    print("📊 RÉSUMÉ FINAL:")
    print("-" * 30)
    
    success_count = 0
    for name, result in results:
        status = "✅" if result.startswith("✅") else "❌"
        print(f"{status} {name}")
        if status == "✅":
            success_count += 1
    
    print()
    print(f"🎯 STATUT GLOBAL: {success_count}/{len(tests)} composants opérationnels")
    
    if success_count == len(tests):
        print("🎉 JARVIS COMPLÈTEMENT OPÉRATIONNEL !")
    elif success_count >= len(tests) // 2:
        print("⚠️ JARVIS PARTIELLEMENT OPÉRATIONNEL")
    else:
        print("❌ JARVIS EN PANNE - INTERVENTION REQUISE")
    
    return success_count == len(tests)

if __name__ == "__main__":
    main()
