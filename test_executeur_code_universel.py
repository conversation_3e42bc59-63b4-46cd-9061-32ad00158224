#!/usr/bin/env python3
"""
🔧 TESTEUR EXÉCUTEUR CODE UNIVERSEL
Test spécifique de l'exécuteur de code pour tous les langages
"""

import sys
import os

def test_code_execution():
    """Teste l'exécuteur de code universel"""
    print("🔧 ================================")
    print("💻 TESTEUR EXÉCUTEUR CODE UNIVERSEL")
    print("🔧 ================================")
    
    try:
        # Importer les fonctions d'exécution
        sys.path.append('.')
        from jarvis_interface_propre import (
            execute_universal_code,
            execute_python_code,
            change_code_language,
            save_code_to_file,
            format_universal_code_result
        )
        
        print("✅ Fonctions d'exécution importées avec succès")
        
        # Tests par langage
        test_cases = [
            {
                'language': 'python',
                'code': 'print("Hello JARVIS!")\nresult = 2 + 2\nprint(f"2 + 2 = {result}")',
                'expected': True
            },
            {
                'language': 'javascript',
                'code': 'console.log("Hello JARVIS!");\nconsole.log("2 + 2 =", 2 + 2);',
                'expected': True
            },
            {
                'language': 'bash',
                'code': 'echo "Hello JARVIS!"\necho "Date: $(date)"',
                'expected': True
            },
            {
                'language': 'json',
                'code': '{\n  "message": "Hello JARVIS!",\n  "status": "active"\n}',
                'expected': True
            },
            {
                'language': 'yaml',
                'code': 'message: Hello JARVIS!\nstatus: active\nversion: 1.0',
                'expected': True
            }
        ]
        
        print(f"\n🧪 TESTS D'EXÉCUTION ({len(test_cases)} langages):")
        
        for i, test in enumerate(test_cases, 1):
            print(f"\n🔍 TEST {i}: {test['language'].upper()}")
            print(f"📝 Code: {test['code'][:50]}...")
            
            try:
                result = execute_universal_code(test['code'], test['language'])
                
                if result['success']:
                    print(f"✅ {test['language']} - SUCCÈS")
                    if result.get('output'):
                        print(f"📤 Sortie: {result['output'][:100]}...")
                else:
                    print(f"❌ {test['language']} - ÉCHEC")
                    print(f"🚫 Erreur: {result.get('error', 'Erreur inconnue')}")
                    
            except Exception as e:
                print(f"❌ {test['language']} - EXCEPTION: {e}")
        
        # Test des templates
        print(f"\n🎨 TEST DES TEMPLATES:")
        languages = ['python', 'javascript', 'html', 'css', 'bash', 'sql']
        
        for lang in languages:
            try:
                template = change_code_language(lang)
                print(f"✅ {lang}: Template généré ({len(template)} caractères)")
            except Exception as e:
                print(f"❌ {lang}: Erreur template - {e}")
        
        # Test de sauvegarde
        print(f"\n💾 TEST DE SAUVEGARDE:")
        try:
            result = save_code_to_file('print("Test sauvegarde")', 'python')
            print(f"✅ Sauvegarde: {result}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
        
        # Test de formatage
        print(f"\n🎨 TEST DE FORMATAGE:")
        try:
            test_result = {'success': True, 'output': 'Hello JARVIS!', 'language': 'python'}
            formatted = format_universal_code_result(test_result)
            print(f"✅ Formatage: {len(formatted)} caractères HTML générés")
        except Exception as e:
            print(f"❌ Erreur formatage: {e}")
        
        print("\n🎉 TOUS LES TESTS D'EXÉCUTION TERMINÉS")
        
    except ImportError as e:
        print(f"❌ Erreur d'importation: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return False
    
    return True

def check_system_requirements():
    """Vérifie les prérequis système"""
    print("\n🔍 VÉRIFICATION PRÉREQUIS SYSTÈME:")
    
    # Vérifier Python
    print(f"✅ Python: {sys.version}")
    
    # Vérifier les interpréteurs disponibles
    interpreters = {
        'node': 'Node.js (JavaScript)',
        'bash': 'Bash (Scripts shell)',
        'python3': 'Python 3',
        'java': 'Java',
        'gcc': 'GCC (C)',
        'g++': 'G++ (C++)',
        'go': 'Go',
        'rustc': 'Rust',
        'php': 'PHP',
        'ruby': 'Ruby'
    }
    
    import subprocess
    
    for cmd, name in interpreters.items():
        try:
            result = subprocess.run([cmd, '--version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.split('\n')[0][:50]
                print(f"✅ {name}: {version}")
            else:
                print(f"❌ {name}: Non disponible")
        except:
            print(f"❌ {name}: Non trouvé")

def test_specific_errors():
    """Teste les cas d'erreur spécifiques"""
    print("\n🚨 TEST DES CAS D'ERREUR:")
    
    try:
        from jarvis_interface_propre import execute_universal_code
        
        # Test langage non supporté
        result = execute_universal_code('test', 'langage_inexistant')
        if not result['success']:
            print("✅ Langage non supporté: Erreur correctement gérée")
        else:
            print("❌ Langage non supporté: Erreur non détectée")
        
        # Test code Python invalide
        result = execute_universal_code('print(invalid syntax', 'python')
        if not result['success']:
            print("✅ Code Python invalide: Erreur correctement gérée")
        else:
            print("❌ Code Python invalide: Erreur non détectée")
        
        # Test JSON invalide
        result = execute_universal_code('{"invalid": json}', 'json')
        if not result['success']:
            print("✅ JSON invalide: Erreur correctement gérée")
        else:
            print("❌ JSON invalide: Erreur non détectée")
            
    except Exception as e:
        print(f"❌ Erreur test d'erreurs: {e}")

def main():
    """Fonction principale"""
    print("💻 TESTEUR COMPLET EXÉCUTEUR CODE UNIVERSEL")
    print("=" * 60)
    
    # Vérifier les prérequis
    check_system_requirements()
    
    # Tester l'exécution
    success = test_code_execution()
    
    # Tester les erreurs
    test_specific_errors()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TESTS EXÉCUTEUR CODE RÉUSSIS !")
        print("✅ L'exécuteur de code universel est opérationnel")
    else:
        print("❌ ÉCHEC DES TESTS EXÉCUTEUR CODE")
        print("Vérifiez les erreurs ci-dessus")
    
    print("\n💡 LANGAGES SUPPORTÉS:")
    print("✅ Python, JavaScript, HTML, CSS, Bash, SQL, JSON, YAML")
    print("✅ Java, C++, C, Go, Rust, PHP, Ruby, Swift, Kotlin, TypeScript")
    print("✅ R, MATLAB, Perl, Lua, Markdown, XML")

if __name__ == "__main__":
    main()
