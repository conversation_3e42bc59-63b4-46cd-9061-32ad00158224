#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR FINAL AFFICHAGE - JEAN-LUC PASSAVE
Correction finale et complète de l'affichage horizontal
"""

import re

def corriger_affichage_final():
    """Correction finale de l'affichage"""
    fichier = "jarvis_interface_propre.py"
    
    print("🔧 CORRECTION FINALE AFFICHAGE")
    print("=" * 40)
    
    with open(fichier, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    corrections = 0
    
    # 1. Corriger le type deprecated
    if 'type="tuples"' in contenu:
        print("📝 Correction type deprecated...")
        contenu = contenu.replace('type="tuples"', 'type="messages"')
        corrections += 1
        print("✅ Type messages appliqué")
    
    # 2. Forcer CSS anti-horizontal plus agressif
    css_pattern = r'custom_css = """.*?"""'
    css_anti_horizontal = '''custom_css = """
/* CSS ANTI-AFFICHAGE HORIZONTAL RENFORCÉ */
.gradio-container .chatbot {
    width: 100% !important;
    max-width: 100% !important;
}

.gradio-container .chatbot .message {
    display: block !important;
    width: auto !important;
    max-width: 85% !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    margin: 8px 0 !important;
    padding: 12px 16px !important;
    border-radius: 12px !important;
    line-height: 1.5 !important;
}

.gradio-container .chatbot .user {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: white !important;
    margin-left: auto !important;
    margin-right: 0 !important;
    text-align: left !important;
}

.gradio-container .chatbot .bot {
    background: #f8f9fa !important;
    color: #333 !important;
    border: 1px solid #dee2e6 !important;
    margin-left: 0 !important;
    margin-right: auto !important;
    text-align: left !important;
}

/* Force vertical layout */
.gradio-container .chatbot .message-row {
    display: block !important;
    flex-direction: column !important;
}

.gradio-container .chatbot .message-bubble {
    display: block !important;
    width: auto !important;
}
"""'''
    
    if re.search(css_pattern, contenu, re.DOTALL):
        print("📝 Application CSS anti-horizontal renforcé...")
        contenu = re.sub(css_pattern, css_anti_horizontal, contenu, re.DOTALL)
        corrections += 1
        print("✅ CSS renforcé appliqué")
    
    # 3. Simplifier la configuration chatbot
    chatbot_pattern = r'conversation_display = gr\.Chatbot\([^)]*\)'
    chatbot_simple = '''conversation_display = gr.Chatbot(
                    label="💬 Conversation avec JARVIS",
                    height=500,
                    show_label=True,
                    show_copy_button=True,
                    type="messages"
                )'''
    
    if re.search(chatbot_pattern, contenu, re.DOTALL):
        print("📝 Simplification configuration chatbot...")
        contenu = re.sub(chatbot_pattern, chatbot_simple, contenu, re.DOTALL)
        corrections += 1
        print("✅ Configuration chatbot simplifiée")
    
    # Sauvegarder
    with open(fichier, 'w', encoding='utf-8') as f:
        f.write(contenu)
    
    print(f"\n🎉 {corrections} CORRECTIONS FINALES APPLIQUÉES !")
    print("✅ Affichage horizontal définitivement corrigé")
    
    return corrections > 0

if __name__ == "__main__":
    corriger_affichage_final()
    print("\n🚀 JARVIS prêt à être lancé avec affichage corrigé !")
