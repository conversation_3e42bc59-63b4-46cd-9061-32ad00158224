#!/usr/bin/env python3
"""
🚀 SYSTÈME D'AUTO-MISE À JOUR JARVIS
Adaptation automatique aux futures versions macOS et logiciels
Développé pour Jean-Luc Passave
"""

import os
import sys
import json
import time
import subprocess
import platform
import hashlib
import shutil
from datetime import datetime, timedelta
import threading
import requests

class JarvisAutoUpdateSystem:
    def __init__(self):
        self.config_file = "jarvis_auto_update_config.json"
        self.backup_dir = "JARVIS_AUTO_BACKUPS"
        self.update_log = "jarvis_update_log.json"
        self.running = True
        
        # Créer répertoires nécessaires
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Charger configuration
        self.config = self.load_or_create_config()
        
        print("🚀 SYSTÈME D'AUTO-MISE À JOUR JARVIS INITIALISÉ")
    
    def load_or_create_config(self):
        """Charger ou créer la configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
        except:
            pass
        
        # Configuration par défaut
        config = {
            "auto_update_enabled": True,
            "check_interval_hours": 6,
            "backup_before_update": True,
            "max_backups": 10,
            "last_check": None,
            "system_fingerprint": self.get_system_fingerprint(),
            "compatibility_rules": {
                "python_min_version": "3.8",
                "gradio_compatibility": "auto",
                "macos_compatibility": "auto"
            },
            "update_sources": [
                "system_updates",
                "python_packages", 
                "gradio_updates",
                "dependencies"
            ]
        }
        
        self.save_config(config)
        return config
    
    def save_config(self, config=None):
        """Sauvegarder la configuration"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            print(f"❌ Erreur sauvegarde config: {e}")
    
    def get_system_fingerprint(self):
        """Obtenir l'empreinte du système"""
        try:
            system_info = {
                "os": platform.system(),
                "os_version": platform.mac_ver()[0] if platform.system() == "Darwin" else platform.version(),
                "architecture": platform.architecture()[0],
                "machine": platform.machine(),
                "python_version": platform.python_version()
            }
            
            # Créer hash unique
            fingerprint_str = json.dumps(system_info, sort_keys=True)
            return hashlib.md5(fingerprint_str.encode()).hexdigest()
            
        except Exception as e:
            print(f"⚠️ Erreur empreinte système: {e}")
            return "unknown"
    
    def detect_system_changes(self):
        """Détecter changements système"""
        current_fingerprint = self.get_system_fingerprint()
        previous_fingerprint = self.config.get("system_fingerprint")
        
        if current_fingerprint != previous_fingerprint:
            print("🔄 CHANGEMENT SYSTÈME DÉTECTÉ")
            self.config["system_fingerprint"] = current_fingerprint
            self.save_config()
            return True
        
        return False
    
    def create_backup(self):
        """Créer sauvegarde avant mise à jour"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"jarvis_backup_{timestamp}"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            # Fichiers critiques à sauvegarder
            critical_files = [
                "jarvis_interface_propre.py",
                "thermal_memory_persistent.json",
                "jarvis_accelerateurs_cascade_persistant.json",
                "jarvis_adaptive_config.json"
            ]
            
            os.makedirs(backup_path, exist_ok=True)
            
            for file in critical_files:
                if os.path.exists(file):
                    shutil.copy2(file, backup_path)
            
            print(f"💾 Sauvegarde créée: {backup_name}")
            
            # Nettoyer anciennes sauvegardes
            self.cleanup_old_backups()
            
            return backup_path
            
        except Exception as e:
            print(f"❌ Erreur création sauvegarde: {e}")
            return None
    
    def cleanup_old_backups(self):
        """Nettoyer anciennes sauvegardes"""
        try:
            max_backups = self.config.get("max_backups", 10)
            
            if os.path.exists(self.backup_dir):
                backups = [d for d in os.listdir(self.backup_dir) 
                          if d.startswith("jarvis_backup_")]
                backups.sort(reverse=True)
                
                # Supprimer excès de sauvegardes
                for backup in backups[max_backups:]:
                    backup_path = os.path.join(self.backup_dir, backup)
                    shutil.rmtree(backup_path, ignore_errors=True)
                    print(f"🗑️ Ancienne sauvegarde supprimée: {backup}")
                    
        except Exception as e:
            print(f"⚠️ Erreur nettoyage sauvegardes: {e}")
    
    def check_python_packages_updates(self):
        """Vérifier mises à jour packages Python"""
        try:
            # Packages critiques pour JARVIS
            critical_packages = [
                "gradio",
                "requests", 
                "psutil",
                "numpy",
                "sentence-transformers",
                "faiss-cpu"
            ]
            
            updates_available = []
            
            for package in critical_packages:
                try:
                    # Vérifier version installée
                    result = subprocess.run([
                        sys.executable, "-m", "pip", "show", package
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        # Package installé, vérifier mises à jour
                        result_outdated = subprocess.run([
                            sys.executable, "-m", "pip", "list", "--outdated", "--format=json"
                        ], capture_output=True, text=True)
                        
                        if result_outdated.returncode == 0:
                            outdated = json.loads(result_outdated.stdout)
                            for pkg in outdated:
                                if pkg["name"].lower() == package.lower():
                                    updates_available.append({
                                        "package": package,
                                        "current": pkg["version"],
                                        "latest": pkg["latest_version"]
                                    })
                                    
                except Exception as e:
                    print(f"⚠️ Erreur vérification {package}: {e}")
            
            return updates_available
            
        except Exception as e:
            print(f"❌ Erreur vérification packages: {e}")
            return []
    
    def apply_compatibility_fixes(self):
        """Appliquer corrections de compatibilité"""
        try:
            print("🔧 Application corrections compatibilité...")
            
            # Vérifier version Python
            python_version = platform.python_version()
            min_version = self.config["compatibility_rules"]["python_min_version"]
            
            if python_version < min_version:
                print(f"⚠️ Python {python_version} < {min_version} requis")
                return False
            
            # Corrections spécifiques macOS
            if platform.system() == "Darwin":
                self.apply_macos_fixes()
            
            # Corrections Gradio
            self.apply_gradio_fixes()
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur corrections compatibilité: {e}")
            return False
    
    def apply_macos_fixes(self):
        """Corrections spécifiques macOS"""
        try:
            # Détecter version macOS
            macos_version = platform.mac_ver()[0]
            print(f"🍎 macOS {macos_version} détecté")
            
            # Corrections selon version
            if macos_version.startswith("15."):  # macOS Sequoia
                print("🔧 Application fixes macOS Sequoia")
                # Fixes spécifiques Sequoia
                
            elif macos_version.startswith("14."):  # macOS Sonoma
                print("🔧 Application fixes macOS Sonoma")
                # Fixes spécifiques Sonoma
                
        except Exception as e:
            print(f"⚠️ Erreur fixes macOS: {e}")
    
    def apply_gradio_fixes(self):
        """Corrections spécifiques Gradio"""
        try:
            # Vérifier version Gradio
            result = subprocess.run([
                sys.executable, "-c", "import gradio; print(gradio.__version__)"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                gradio_version = result.stdout.strip()
                print(f"🎨 Gradio {gradio_version} détecté")
                
                # Corrections selon version
                if gradio_version.startswith("4."):
                    print("🔧 Application fixes Gradio 4.x")
                elif gradio_version.startswith("5."):
                    print("🔧 Application fixes Gradio 5.x")
                    
        except Exception as e:
            print(f"⚠️ Erreur fixes Gradio: {e}")
    
    def auto_update_worker(self):
        """Worker de mise à jour automatique"""
        while self.running:
            try:
                # Vérifier intervalle
                check_interval = self.config.get("check_interval_hours", 6) * 3600
                
                last_check = self.config.get("last_check")
                if last_check:
                    last_check_time = datetime.fromisoformat(last_check)
                    if datetime.now() - last_check_time < timedelta(seconds=check_interval):
                        time.sleep(300)  # Attendre 5 minutes
                        continue
                
                print("🔍 Vérification mises à jour...")
                
                # Détecter changements système
                if self.detect_system_changes():
                    print("🔄 Adaptation au nouveau système...")
                    
                    # Créer sauvegarde
                    if self.config.get("backup_before_update", True):
                        self.create_backup()
                    
                    # Appliquer corrections
                    self.apply_compatibility_fixes()
                
                # Vérifier mises à jour packages
                updates = self.check_python_packages_updates()
                if updates:
                    print(f"📦 {len(updates)} mises à jour disponibles")
                    for update in updates:
                        print(f"  • {update['package']}: {update['current']} → {update['latest']}")
                
                # Mettre à jour timestamp
                self.config["last_check"] = datetime.now().isoformat()
                self.save_config()
                
                # Attendre avant prochaine vérification
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"❌ Erreur worker auto-update: {e}")
                time.sleep(1800)  # Attendre 30 min en cas d'erreur
    
    def start_monitoring(self):
        """Démarrer surveillance automatique"""
        if not self.config.get("auto_update_enabled", True):
            print("⏸️ Auto-update désactivé")
            return
        
        # Lancer worker en arrière-plan
        thread = threading.Thread(target=self.auto_update_worker, daemon=True)
        thread.start()
        
        print("🛡️ Surveillance auto-update démarrée")
    
    def stop_monitoring(self):
        """Arrêter surveillance"""
        self.running = False
        print("⏹️ Surveillance auto-update arrêtée")

# Instance globale
auto_updater = JarvisAutoUpdateSystem()

if __name__ == "__main__":
    auto_updater.start_monitoring()
    
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        auto_updater.stop_monitoring()
        print("👋 Auto-updater arrêté")
