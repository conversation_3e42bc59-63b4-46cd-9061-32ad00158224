#!/usr/bin/env python3
"""
🚀 INTERFACE JARVIS RAPIDE
Lance immédiatement l'interface sans attendre la génération neuronale
"""

import gradio as gr
import requests
import json
import time
import os
import threading
from datetime import datetime

# Configuration
DEEPSEEK_URL = "http://localhost:8000/v1/chat/completions"
MEMORY_FILE = "thermal_memory_persistent.json"

def send_message_rapide(message):
    """Envoi message rapide à DeepSeek"""
    try:
        if not message.strip():
            return "Veuillez saisir un message."
        
        # Prompt simple
        payload = {
            "model": "deepseek-ai/DeepSeek-R1-0528",
            "messages": [
                {"role": "system", "content": "Tu es JARVIS, assistant <PERSON><PERSON>. Réponds en français."},
                {"role": "user", "content": message}
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }
        
        response = requests.post(DEEPSEEK_URL, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            agent_response = data['choices'][0]['message']['content']
            
            # Sauvegarder en mémoire thermique
            save_to_memory_simple(message, agent_response)
            
            return agent_response
        else:
            return f"❌ Erreur DeepSeek: {response.status_code}"
            
    except Exception as e:
        return f"❌ Erreur: {str(e)}"

def save_to_memory_simple(user_message, agent_response):
    """Sauvegarde simple en mémoire thermique"""
    try:
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                memory = json.load(f)
        else:
            memory = {"conversations": []}
        
        timestamp = time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        
        memory["conversations"].extend([
            {
                "id": int(time.time() * 1000),
                "timestamp": timestamp,
                "sender": "user",
                "content": user_message,
                "agent": "agent1"
            },
            {
                "id": int(time.time() * 1000) + 1,
                "timestamp": timestamp,
                "sender": "agent1",
                "content": agent_response,
                "agent": "agent1"
            }
        ])
        
        memory["lastUpdate"] = timestamp
        memory["totalEntries"] = len(memory["conversations"])
        
        with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(memory, f, ensure_ascii=False, indent=2)
            
        print(f"💾 Mémoire sauvegardée: {memory['totalEntries']} entrées")
        
    except Exception as e:
        print(f"❌ Erreur sauvegarde: {e}")

def test_deepseek():
    """Test connexion DeepSeek"""
    try:
        response = requests.get("http://localhost:8000/v1/models", timeout=5)
        if response.status_code == 200:
            return "✅ DeepSeek R1 8B opérationnel"
        else:
            return f"❌ DeepSeek erreur: {response.status_code}"
    except Exception as e:
        return f"❌ DeepSeek non accessible: {e}"

def activer_systeme_neuronal_86_milliards():
    """Active le système 86B en arrière-plan"""
    try:
        # Import et activation en arrière-plan
        def activation_background():
            try:
                from jarvis_systeme_neuronal_86_milliards import systeme_neuronal_86b
                activated = systeme_neuronal_86b.activate_neural_pathway("conversation", 0.8)
                print(f"🧠 Système 86B activé: {activated:,} neurones")
            except Exception as e:
                print(f"❌ Erreur activation 86B: {e}")
        
        # Lancer en arrière-plan
        threading.Thread(target=activation_background, daemon=True).start()
        
        return """
        <div style="background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%); color: white; padding: 20px; border-radius: 15px;">
            <h3>🧠 SYSTÈME NEURONAL 86+ MILLIARDS</h3>
            <p>🔄 <strong>Activation en arrière-plan...</strong></p>
            <p>⚡ Interface disponible immédiatement</p>
            <p>🧠 Génération neuronale en cours</p>
            <p><em>Le système complet sera disponible sous peu</em></p>
        </div>
        """
        
    except Exception as e:
        return f"❌ Erreur système 86B: {e}"

def get_memory_stats():
    """Statistiques mémoire thermique"""
    try:
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conversations = data.get('conversations', [])
            size_mb = os.path.getsize(MEMORY_FILE) / 1024 / 1024
            
            return f"""
            <div style="background: #2e7d32; color: white; padding: 15px; border-radius: 10px;">
                <h4>💾 MÉMOIRE THERMIQUE</h4>
                <p>📊 Conversations: {len(conversations)}</p>
                <p>💾 Taille: {size_mb:.2f} MB</p>
                <p>🕒 Dernière MAJ: {data.get('lastUpdate', 'N/A')}</p>
            </div>
            """
        else:
            return "❌ Fichier mémoire non trouvé"
    except Exception as e:
        return f"❌ Erreur mémoire: {e}"

# Interface Gradio rapide
def create_interface():
    """Crée l'interface Gradio rapide"""
    
    with gr.Blocks(
        title="🚀 JARVIS RAPIDE",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container { max-width: 1200px !important; }
        .chat-message { padding: 10px; margin: 5px; border-radius: 10px; }
        """
    ) as interface:
        
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #1976d2, #42a5f5); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
            <h1>🚀 JARVIS INTERFACE RAPIDE</h1>
            <p>Interface immédiate • Système neuronal 86B en arrière-plan</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=3):
                # Chat
                chatbot = gr.Chatbot(
                    label="💬 Conversation JARVIS",
                    height=500,
                    show_copy_button=True
                )
                
                with gr.Row():
                    message_input = gr.Textbox(
                        label="Votre message",
                        placeholder="Tapez votre message...",
                        scale=4
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary")
                
                clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
            
            with gr.Column(scale=1):
                # Statut
                gr.HTML("<h3>🔧 CONTRÔLES</h3>")
                
                test_btn = gr.Button("🔗 Test DeepSeek", variant="secondary")
                neural_86b_btn = gr.Button("🧠 Activer 86B", variant="primary")
                memory_btn = gr.Button("💾 Stats Mémoire", variant="secondary")
                
                status_output = gr.HTML(
                    value="""
                    <div style="background: #4caf50; color: white; padding: 10px; border-radius: 5px;">
                        <h4>🟢 JARVIS PRÊT</h4>
                        <p>Interface rapide active</p>
                    </div>
                    """
                )
        
        # Événements
        def chat_response(message, history):
            if not message.strip():
                return history, ""
            
            # Ajouter message utilisateur
            history.append([message, None])
            
            # Obtenir réponse
            response = send_message_rapide(message)
            
            # Ajouter réponse
            history[-1][1] = response
            
            return history, ""
        
        send_btn.click(
            chat_response,
            inputs=[message_input, chatbot],
            outputs=[chatbot, message_input]
        )
        
        message_input.submit(
            chat_response,
            inputs=[message_input, chatbot],
            outputs=[chatbot, message_input]
        )
        
        clear_btn.click(lambda: [], outputs=[chatbot])
        
        test_btn.click(test_deepseek, outputs=[status_output])
        neural_86b_btn.click(activer_systeme_neuronal_86_milliards, outputs=[status_output])
        memory_btn.click(get_memory_stats, outputs=[status_output])
    
    return interface

if __name__ == "__main__":
    print("🚀 LANCEMENT JARVIS INTERFACE RAPIDE")
    print("=" * 40)
    
    # Test DeepSeek
    print("🔗 Test DeepSeek...")
    deepseek_status = test_deepseek()
    print(f"   {deepseek_status}")
    
    # Lancer interface
    print("🌐 Lancement interface web...")
    interface = create_interface()
    
    print("✅ Interface prête !")
    print("🌐 URL: http://localhost:7863")
    
    interface.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        show_error=True,
        quiet=False
    )
