#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 LANCEUR JARVIS AVEC TOUS LES CORRECTEURS - JEAN-LUC PASSAVE
Lance JARVIS avec tous les systèmes de correction automatique intégrés
"""

import subprocess
import time
import os
import threading
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🚀 [{timestamp}] {message}")

def lancer_super_correcteur():
    """Lance le super correcteur automatique"""
    log("🔧 LANCEMENT SUPER CORRECTEUR...")
    
    try:
        result = subprocess.run([
            "python3", "super_correcteur_automatique_jarvis.py"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            log("✅ Super correcteur terminé avec succès")
            return True
        else:
            log(f"❌ Super correcteur échoué: {result.stderr}")
            return False
            
    except Exception as e:
        log(f"❌ Erreur super correcteur: {e}")
        return False

def lancer_gardien_stabilite():
    """Lance le gardien de stabilité en arrière-plan"""
    log("🛡️ LANCEMENT GARDIEN DE STABILITÉ...")
    
    try:
        # Lancer le gardien en arrière-plan
        process = subprocess.Popen([
            "python3", "gardien_stabilite_jarvis.py"
        ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Envoyer choix "1" pour surveillance continue
        process.stdin.write(b"1\n")
        process.stdin.flush()
        
        time.sleep(2)
        log("✅ Gardien de stabilité actif")
        return process
        
    except Exception as e:
        log(f"❌ Erreur gardien: {e}")
        return None

def verifier_jarvis_fonctionne():
    """Vérifie que JARVIS fonctionne correctement"""
    import requests
    
    for i in range(30):
        try:
            response = requests.get("http://localhost:7860", timeout=2)
            if response.status_code == 200:
                log("✅ JARVIS interface accessible")
                return True
        except:
            pass
        
        time.sleep(1)
    
    log("⚠️  JARVIS prend du temps à démarrer")
    return False

def lancement_complet_jarvis():
    """Lancement complet avec tous les correcteurs"""
    log("🚀 LANCEMENT COMPLET JARVIS AVEC CORRECTEURS")
    print("=" * 60)
    
    # 1. Super correcteur (corrige tout)
    log("ÉTAPE 1: Super correction automatique")
    if not lancer_super_correcteur():
        log("❌ Échec super correcteur")
        return False
    
    # 2. Gardien de stabilité (surveillance continue)
    log("ÉTAPE 2: Activation gardien de stabilité")
    gardien_process = lancer_gardien_stabilite()
    
    # 3. Vérification finale
    log("ÉTAPE 3: Vérification fonctionnement")
    if verifier_jarvis_fonctionne():
        log("✅ JARVIS opérationnel")
    else:
        log("⚠️  JARVIS en cours de démarrage")
    
    # 4. Résumé final
    print("\n" + "=" * 60)
    log("🎉 LANCEMENT COMPLET TERMINÉ")
    print("=" * 60)
    
    log("📋 SYSTÈMES ACTIFS:")
    log("   ✅ Super correcteur automatique (exécuté)")
    log("   ✅ Gardien de stabilité (surveillance continue)")
    log("   ✅ JARVIS interface (http://localhost:7860)")
    log("   ✅ DeepSeek R1 8B (http://localhost:8000)")
    
    log("🛡️ PROTECTION ACTIVE:")
    log("   ✅ Auto-correction des erreurs")
    log("   ✅ Surveillance continue du code")
    log("   ✅ Redémarrage automatique si nécessaire")
    log("   ✅ Backup automatique avant corrections")
    
    log("🌐 INTERFACE: http://localhost:7860")
    log("💬 Vous pouvez maintenant utiliser JARVIS en toute sécurité")
    
    return True

def creer_script_demarrage_rapide():
    """Crée un script de démarrage rapide"""
    script_content = '''#!/bin/bash
# DÉMARRAGE RAPIDE JARVIS AVEC CORRECTEURS
echo "🚀 DÉMARRAGE RAPIDE JARVIS"
cd "/Volumes/seagate/Louna_Electron_Latest"
python3 lancer_jarvis_avec_tous_correcteurs.py
'''
    
    with open("demarrage_rapide_jarvis.sh", "w") as f:
        f.write(script_content)
    
    os.chmod("demarrage_rapide_jarvis.sh", 0o755)
    log("✅ Script démarrage rapide créé: demarrage_rapide_jarvis.sh")

def menu_principal():
    """Menu principal"""
    print("🚀 LANCEUR JARVIS AVEC TOUS LES CORRECTEURS")
    print("=" * 50)
    print("1. Lancement complet (recommandé)")
    print("2. Super correcteur seulement")
    print("3. Gardien de stabilité seulement")
    print("4. Créer script démarrage rapide")
    print("5. Statut système")
    
    choix = input("\nVotre choix (1-5): ").strip()
    
    if choix == "1":
        return lancement_complet_jarvis()
    
    elif choix == "2":
        return lancer_super_correcteur()
    
    elif choix == "3":
        gardien = lancer_gardien_stabilite()
        if gardien:
            log("Gardien actif - Ctrl+C pour arrêter")
            try:
                gardien.wait()
            except KeyboardInterrupt:
                log("Gardien arrêté")
        return True
    
    elif choix == "4":
        creer_script_demarrage_rapide()
        return True
    
    elif choix == "5":
        # Statut système
        import requests
        
        print("\n📊 STATUT SYSTÈME")
        print("=" * 30)
        
        # JARVIS
        try:
            response = requests.get("http://localhost:7860", timeout=2)
            print(f"🧠 JARVIS: {'✅ Actif' if response.status_code == 200 else '❌ Erreur'}")
        except:
            print("🧠 JARVIS: ❌ Arrêté")
        
        # DeepSeek
        try:
            response = requests.get("http://localhost:8000/v1/models", timeout=2)
            print(f"🤖 DeepSeek: {'✅ Actif' if response.status_code == 200 else '❌ Erreur'}")
        except:
            print("🤖 DeepSeek: ❌ Arrêté")
        
        # Fichiers
        print(f"📁 Fichier JARVIS: {'✅ Présent' if os.path.exists('jarvis_interface_propre.py') else '❌ Manquant'}")
        print(f"🔧 Super correcteur: {'✅ Présent' if os.path.exists('super_correcteur_automatique_jarvis.py') else '❌ Manquant'}")
        print(f"🛡️ Gardien: {'✅ Présent' if os.path.exists('gardien_stabilite_jarvis.py') else '❌ Manquant'}")
        
        return True
    
    else:
        print("❌ Choix invalide")
        return False

if __name__ == "__main__":
    try:
        menu_principal()
    except KeyboardInterrupt:
        log("\n🛑 Arrêt demandé par l'utilisateur")
    except Exception as e:
        log(f"❌ Erreur: {e}")
