#!/bin/bash

echo "📊 =================================="
echo "📱 STATUT WHATSAPP JARVIS"
echo "📊 =================================="

# Vérifier le processus WhatsApp
if pgrep -f "jarvis_whatsapp_integration.js" > /dev/null; then
    echo "✅ Service WhatsApp JARVIS: ACTIF"
    echo "🔢 PID: $(pgrep -f jarvis_whatsapp_integration.js)"
else
    echo "❌ Service WhatsApp JARVIS: INACTIF"
fi

# Vérifier les dépendances
if [ -d "node_modules/whatsapp-web.js" ]; then
    echo "✅ Dépendances WhatsApp: INSTALLÉES"
else
    echo "❌ Dépendances WhatsApp: MANQUANTES"
fi

# Vérifier JARVIS principal
if curl -s http://localhost:8000/v1/models > /dev/null; then
    echo "✅ JARVIS DeepSeek: OPÉRATIONNEL"
else
    echo "❌ JARVIS DeepSeek: INACTIF"
fi

if curl -s http://localhost:7863 > /dev/null; then
    echo "✅ Interface JARVIS: ACCESSIBLE"
else
    echo "❌ Interface JARVIS: INACCESSIBLE"
fi

echo ""
echo "📋 COMMANDES DISPONIBLES:"
echo "🚀 Démarrer: ./demarrer_whatsapp_jarvis.sh"
echo "🛑 Arrêter: ./arreter_whatsapp_jarvis.sh"
echo "📊 Statut: ./statut_whatsapp_jarvis.sh"
