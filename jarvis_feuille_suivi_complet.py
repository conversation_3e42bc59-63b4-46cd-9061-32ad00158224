#!/usr/bin/env python3
"""
📊 FEUILLE DE SUIVI COMPLET JARVIS
Surveillance 24h/24 - Heure par heure, minute par minute
Mode Sommeil Intelligent inclus
Créé pour Jean-Luc Passave
"""

import json
import time
import threading
import os
import psutil
import requests
from datetime import datetime, timedelta
import subprocess
import signal
import sys

class JarvisSuiviComplet:
    def __init__(self):
        self.suivi_file = "jarvis_feuille_suivi_24h.json"
        self.running = True
        self.mode_sommeil = False
        self.derniere_activite = time.time()
        self.seuil_sommeil = 300  # 5 minutes sans activité = sommeil
        
        # Initialiser la feuille de suivi
        self.init_feuille_suivi()
        
        print("📊 SYSTÈME DE SUIVI JARVIS 24H/24 INITIALISÉ")
        print("🔍 Surveillance: Heure par heure, minute par minute")
        print("😴 Mode sommeil: Activé automatiquement")
    
    def init_feuille_suivi(self):
        """Initialiser la structure de suivi"""
        if not os.path.exists(self.suivi_file):
            structure_initiale = {
                "date_creation": datetime.now().isoformat(),
                "derniere_mise_a_jour": datetime.now().isoformat(),
                "mode_sommeil_actif": False,
                "statistiques_globales": {
                    "temps_total_actif": 0,
                    "temps_total_sommeil": 0,
                    "nombre_reveils": 0,
                    "nombre_endormissements": 0
                },
                "suivi_par_jour": {},
                "activites_temps_reel": [],
                "alertes_systeme": []
            }
            
            with open(self.suivi_file, 'w') as f:
                json.dump(structure_initiale, f, indent=2)
    
    def charger_suivi(self):
        """Charger les données de suivi"""
        try:
            with open(self.suivi_file, 'r') as f:
                return json.load(f)
        except:
            self.init_feuille_suivi()
            return self.charger_suivi()
    
    def sauvegarder_suivi(self, data):
        """Sauvegarder les données de suivi"""
        try:
            data["derniere_mise_a_jour"] = datetime.now().isoformat()
            with open(self.suivi_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"❌ Erreur sauvegarde suivi: {e}")
    
    def detecter_activite_utilisateur(self):
        """Détecter si l'utilisateur est actif"""
        try:
            # Vérifier les connexions récentes à JARVIS
            response = requests.get("http://127.0.0.1:7867", timeout=2)
            if response.status_code == 200:
                return True
        except:
            pass
        
        # Vérifier les processus actifs
        try:
            result = subprocess.run(["pgrep", "-f", "jarvis_interface_propre.py"], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def obtenir_statut_systeme(self):
        """Obtenir le statut complet du système"""
        statut = {
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "processus_jarvis": False,
            "processus_electron": False,
            "interface_accessible": False,
            "accelerateurs_actifs": 0,
            "mode_sommeil": self.mode_sommeil
        }
        
        # Vérifier processus JARVIS
        try:
            result = subprocess.run(["pgrep", "-f", "jarvis_interface_propre.py"], 
                                  capture_output=True, text=True)
            statut["processus_jarvis"] = result.returncode == 0
        except:
            pass
        
        # Vérifier processus Electron
        try:
            result = subprocess.run(["pgrep", "-f", "jarvis_electron_force.js"], 
                                  capture_output=True, text=True)
            statut["processus_electron"] = result.returncode == 0
        except:
            pass
        
        # Vérifier interface accessible
        try:
            response = requests.get("http://127.0.0.1:7867", timeout=2)
            statut["interface_accessible"] = response.status_code == 200
        except:
            pass
        
        # Vérifier accélérateurs
        try:
            with open("jarvis_accelerateurs_cascade_persistant.json", 'r') as f:
                accelerateurs = json.load(f)
                statut["accelerateurs_actifs"] = sum(1 for acc in accelerateurs.values() 
                                                   if acc.get("active", False))
        except:
            pass
        
        return statut
    
    def enregistrer_activite(self, type_activite, description, details=None):
        """Enregistrer une activité dans la feuille de suivi"""
        data = self.charger_suivi()
        
        # Obtenir la date actuelle
        maintenant = datetime.now()
        date_str = maintenant.strftime("%Y-%m-%d")
        heure_str = maintenant.strftime("%H:%M")
        
        # Initialiser le jour si nécessaire
        if date_str not in data["suivi_par_jour"]:
            data["suivi_par_jour"][date_str] = {
                "activites_par_heure": {},
                "resume_journee": {
                    "debut_activite": None,
                    "fin_activite": None,
                    "temps_actif_total": 0,
                    "temps_sommeil_total": 0,
                    "nombre_activites": 0
                }
            }
        
        # Initialiser l'heure si nécessaire
        if heure_str not in data["suivi_par_jour"][date_str]["activites_par_heure"]:
            data["suivi_par_jour"][date_str]["activites_par_heure"][heure_str] = []
        
        # Ajouter l'activité
        activite = {
            "timestamp": maintenant.isoformat(),
            "type": type_activite,
            "description": description,
            "details": details or {},
            "mode_sommeil": self.mode_sommeil
        }
        
        data["suivi_par_jour"][date_str]["activites_par_heure"][heure_str].append(activite)
        data["suivi_par_jour"][date_str]["resume_journee"]["nombre_activites"] += 1
        
        # Ajouter aux activités temps réel (garder seulement les 100 dernières)
        data["activites_temps_reel"].append(activite)
        if len(data["activites_temps_reel"]) > 100:
            data["activites_temps_reel"] = data["activites_temps_reel"][-100:]
        
        # Mettre à jour les statistiques
        if not data["suivi_par_jour"][date_str]["resume_journee"]["debut_activite"]:
            data["suivi_par_jour"][date_str]["resume_journee"]["debut_activite"] = maintenant.isoformat()
        
        data["suivi_par_jour"][date_str]["resume_journee"]["fin_activite"] = maintenant.isoformat()
        
        self.sauvegarder_suivi(data)
    
    def gerer_mode_sommeil(self):
        """Gérer le passage en mode sommeil et réveil"""
        activite_detectee = self.detecter_activite_utilisateur()
        
        if activite_detectee:
            self.derniere_activite = time.time()
            
            # Réveil si en sommeil
            if self.mode_sommeil:
                self.mode_sommeil = False
                self.enregistrer_activite(
                    "REVEIL",
                    "🌅 JARVIS se réveille - Activité utilisateur détectée",
                    {"cause_reveil": "activite_utilisateur"}
                )
                
                # Mettre à jour statistiques
                data = self.charger_suivi()
                data["statistiques_globales"]["nombre_reveils"] += 1
                data["mode_sommeil_actif"] = False
                self.sauvegarder_suivi(data)
                
                print("🌅 JARVIS RÉVEILLÉ - Activité détectée")
        
        else:
            # Vérifier si doit s'endormir
            temps_inactif = time.time() - self.derniere_activite
            
            if temps_inactif > self.seuil_sommeil and not self.mode_sommeil:
                self.mode_sommeil = True
                self.enregistrer_activite(
                    "SOMMEIL",
                    "😴 JARVIS passe en mode sommeil - Inactivité prolongée",
                    {
                        "duree_inactivite": temps_inactif,
                        "seuil_sommeil": self.seuil_sommeil
                    }
                )
                
                # Mettre à jour statistiques
                data = self.charger_suivi()
                data["statistiques_globales"]["nombre_endormissements"] += 1
                data["mode_sommeil_actif"] = True
                self.sauvegarder_suivi(data)
                
                print("😴 JARVIS EN SOMMEIL - Mode économie d'énergie")
    
    def surveillance_continue(self):
        """Surveillance continue du système"""
        while self.running:
            try:
                # Gérer le mode sommeil
                self.gerer_mode_sommeil()
                
                # Obtenir statut système
                statut = self.obtenir_statut_systeme()
                
                # Enregistrer l'activité selon le mode
                if self.mode_sommeil:
                    # En mode sommeil : surveillance réduite (toutes les 5 minutes)
                    self.enregistrer_activite(
                        "SURVEILLANCE_SOMMEIL",
                        "😴 Surveillance en mode sommeil",
                        statut
                    )
                    time.sleep(300)  # 5 minutes
                else:
                    # Mode actif : surveillance détaillée (toutes les minutes)
                    self.enregistrer_activite(
                        "SURVEILLANCE_ACTIVE",
                        "🔍 Surveillance active du système",
                        statut
                    )
                    time.sleep(60)  # 1 minute
                
            except Exception as e:
                self.enregistrer_activite(
                    "ERREUR",
                    f"❌ Erreur surveillance: {str(e)}",
                    {"erreur": str(e)}
                )
                time.sleep(60)
    
    def generer_rapport_temps_reel(self):
        """Générer un rapport temps réel"""
        data = self.charger_suivi()
        
        print("\n📊 ================================")
        print("🤖 FEUILLE DE SUIVI JARVIS 24H/24")
        print("📊 ================================")
        
        # Statut actuel
        maintenant = datetime.now()
        print(f"🕐 Heure actuelle: {maintenant.strftime('%H:%M:%S')}")
        print(f"😴 Mode sommeil: {'🟢 ACTIF' if self.mode_sommeil else '🔴 INACTIF'}")
        
        # Statistiques globales
        stats = data["statistiques_globales"]
        print(f"\n📈 STATISTIQUES GLOBALES:")
        print(f"  🔄 Nombre de réveils: {stats['nombre_reveils']}")
        print(f"  😴 Nombre d'endormissements: {stats['nombre_endormissements']}")
        
        # Activités récentes
        print(f"\n⏰ ACTIVITÉS RÉCENTES (10 dernières):")
        activites_recentes = data["activites_temps_reel"][-10:]
        for activite in activites_recentes:
            timestamp = activite["timestamp"].split("T")[1][:8]
            type_act = activite["type"]
            description = activite["description"]
            mode = "😴" if activite.get("mode_sommeil") else "🔍"
            print(f"  {timestamp} {mode} {type_act}: {description}")
        
        print("\n" + "="*50)
    
    def arreter_surveillance(self):
        """Arrêter la surveillance proprement"""
        self.running = False
        self.enregistrer_activite(
            "ARRET",
            "🛑 Arrêt du système de surveillance",
            {"arret_propre": True}
        )
        print("🛑 Surveillance JARVIS arrêtée proprement")

# Instance globale
suivi_jarvis = JarvisSuiviComplet()

def signal_handler(sig, frame):
    """Gestionnaire de signal pour arrêt propre"""
    print("\n🛑 Arrêt demandé...")
    suivi_jarvis.arreter_surveillance()
    sys.exit(0)

def main():
    """Fonction principale"""
    # Capturer Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🚀 Démarrage surveillance JARVIS 24h/24...")
    
    # Enregistrer le démarrage
    suivi_jarvis.enregistrer_activite(
        "DEMARRAGE",
        "🚀 Démarrage du système de surveillance 24h/24",
        {"version": "1.0", "mode_sommeil_disponible": True}
    )
    
    # Lancer surveillance en arrière-plan
    thread_surveillance = threading.Thread(target=suivi_jarvis.surveillance_continue, daemon=True)
    thread_surveillance.start()
    
    # Boucle principale avec rapports périodiques
    try:
        while True:
            time.sleep(300)  # Rapport toutes les 5 minutes
            suivi_jarvis.generer_rapport_temps_reel()
    except KeyboardInterrupt:
        signal_handler(None, None)

if __name__ == "__main__":
    main()
