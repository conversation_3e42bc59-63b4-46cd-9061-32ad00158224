#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 SCANNER PROBLÈMES SPÉCIFIQUES - JEAN-LUC PASSAVE
Scanne le code actuel pour identifier et corriger SEULEMENT les problèmes
"""

import re
import os
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔍 [{timestamp}] {message}")

def scanner_problemes_code():
    """Scanne le code pour identifier les problèmes spécifiques"""
    log("🔍 SCAN PROBLÈMES SPÉCIFIQUES")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
    except Exception as e:
        log(f"❌ Erreur lecture fichier: {e}")
        return []
    
    problemes = []
    
    # 1. Problème configuration chatbot
    chatbot_configs = re.findall(r'gr\.Chatbot\([^)]*\)', code, re.DOTALL)
    for config in chatbot_configs:
        if 'type=' in config and ('messages' in config or 'tuples' in config):
            if 'bubble_full_width' in config or len(config) > 200:
                problemes.append({
                    'type': 'CHATBOT_CONFIG_COMPLEXE',
                    'ligne': code[:code.find(config)].count('\n') + 1,
                    'probleme': 'Configuration chatbot trop complexe',
                    'config_actuelle': config.strip(),
                    'correction': 'gr.Chatbot(label="💬 Conversation", height=500)'
                })
    
    # 2. Problème CSS surchargé
    css_match = re.search(r'custom_css = """(.*?)"""', code, re.DOTALL)
    if css_match and len(css_match.group(1)) > 500:
        problemes.append({
            'type': 'CSS_SURCHARGE',
            'ligne': code[:css_match.start()].count('\n') + 1,
            'probleme': f'CSS trop complexe ({len(css_match.group(1))} caractères)',
            'correction': 'custom_css = ""'
        })
    
    # 3. Problème imports inutiles
    imports_problematiques = [
        'import threading',
        'import uuid', 
        'import subprocess',
        'import psutil',
        'from urllib3',
        'from requests.adapters'
    ]
    
    for imp in imports_problematiques:
        if imp in code and f'# {imp}' not in code:
            ligne_num = code[:code.find(imp)].count('\n') + 1
            problemes.append({
                'type': 'IMPORT_INUTILE',
                'ligne': ligne_num,
                'probleme': f'Import inutile: {imp}',
                'import': imp,
                'correction': f'# {imp}  # DÉSACTIVÉ'
            })
    
    # 4. Problème configuration launch
    launch_match = re.search(r'demo\.launch\([^)]*\)', code, re.DOTALL)
    if launch_match and len(launch_match.group()) > 150:
        problemes.append({
            'type': 'LAUNCH_CONFIG_COMPLEXE',
            'ligne': code[:launch_match.start()].count('\n') + 1,
            'probleme': 'Configuration launch trop complexe',
            'config_actuelle': launch_match.group(),
            'correction': 'demo.launch(server_name="0.0.0.0", server_port=7860)'
        })
    
    # 5. Problème fonctions dupliquées
    fonctions = re.findall(r'^def ([a-zA-Z_][a-zA-Z0-9_]*)', code, re.MULTILINE)
    fonctions_dupliquees = []
    for fonction in set(fonctions):
        if fonctions.count(fonction) > 1:
            fonctions_dupliquees.append(fonction)
    
    if fonctions_dupliquees:
        problemes.append({
            'type': 'FONCTIONS_DUPLIQUEES',
            'ligne': 0,
            'probleme': f'Fonctions dupliquées: {fonctions_dupliquees}',
            'correction': 'Supprimer les doublons'
        })
    
    # 6. Problème variables globales multiples
    variables_globales = re.findall(r'^([A-Z_]+) = ', code, re.MULTILINE)
    if len(set(variables_globales)) != len(variables_globales):
        problemes.append({
            'type': 'VARIABLES_DUPLIQUEES',
            'ligne': 0,
            'probleme': 'Variables globales dupliquées',
            'correction': 'Nettoyer les doublons'
        })
    
    # 7. Problème gestion erreurs
    if 'conversation_history.append([message, ' in code:
        # Vérifier format correct
        patterns_incorrects = [
            r'conversation_history\.append\(\[message, [^]]*\]\)',
        ]
        for pattern in patterns_incorrects:
            matches = re.finditer(pattern, code)
            for match in matches:
                ligne_num = code[:match.start()].count('\n') + 1
                problemes.append({
                    'type': 'FORMAT_MESSAGE_INCORRECT',
                    'ligne': ligne_num,
                    'probleme': 'Format message conversation incorrect',
                    'correction': 'Vérifier format [user_msg, bot_response]'
                })
    
    return problemes

def corriger_problemes_specifiques(problemes):
    """Corrige SEULEMENT les problèmes identifiés"""
    log("🔧 CORRECTION PROBLÈMES SPÉCIFIQUES")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
    except Exception as e:
        log(f"❌ Erreur lecture: {e}")
        return False
    
    corrections_appliquees = 0
    
    for probleme in problemes:
        if probleme['type'] == 'CSS_SURCHARGE':
            log("🎨 Correction CSS surchargé...")
            code = re.sub(r'custom_css = """.*?"""', 'custom_css = ""', code, flags=re.DOTALL)
            corrections_appliquees += 1
        
        elif probleme['type'] == 'CHATBOT_CONFIG_COMPLEXE':
            log("💬 Correction config chatbot...")
            code = re.sub(
                r'gr\.Chatbot\([^)]*\)',
                'gr.Chatbot(label="💬 Conversation", height=500)',
                code,
                flags=re.DOTALL
            )
            corrections_appliquees += 1
        
        elif probleme['type'] == 'IMPORT_INUTILE':
            log(f"📦 Désactivation import: {probleme['import']}")
            code = code.replace(probleme['import'], f"# {probleme['import']}  # DÉSACTIVÉ")
            corrections_appliquees += 1
        
        elif probleme['type'] == 'LAUNCH_CONFIG_COMPLEXE':
            log("🚀 Simplification config launch...")
            code = re.sub(
                r'demo\.launch\([^)]*\)',
                'demo.launch(server_name="0.0.0.0", server_port=7860)',
                code,
                flags=re.DOTALL
            )
            corrections_appliquees += 1
    
    # Sauvegarder les corrections
    if corrections_appliquees > 0:
        # Backup avant correction
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"jarvis_interface_propre_avant_correction_{timestamp}.py"
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as original:
                f.write(original.read())
        
        log(f"💾 Backup créé: {backup_file}")
        
        # Appliquer corrections
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log(f"✅ {corrections_appliquees} corrections appliquées")
        return True
    else:
        log("ℹ️  Aucune correction nécessaire")
        return True

def verifier_apres_correction():
    """Vérifie que les corrections n'ont pas cassé le code"""
    log("🧪 VÉRIFICATION POST-CORRECTION")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Vérifications de base
        verifications = []
        
        # 1. Syntaxe Python basique
        if 'def ' in code and 'import ' in code:
            verifications.append("✅ Structure Python OK")
        else:
            verifications.append("❌ Structure Python problématique")
        
        # 2. Interface Gradio
        if 'gr.Blocks' in code and 'demo.launch' in code:
            verifications.append("✅ Interface Gradio OK")
        else:
            verifications.append("❌ Interface Gradio manquante")
        
        # 3. Fonction principale
        if 'def create_interface' in code or 'if __name__ == "__main__"' in code:
            verifications.append("✅ Point d'entrée OK")
        else:
            verifications.append("❌ Point d'entrée manquant")
        
        # 4. Taille raisonnable
        lignes = len(code.split('\n'))
        if lignes < 5000:
            verifications.append(f"✅ Taille raisonnable ({lignes} lignes)")
        else:
            verifications.append(f"⚠️  Code encore volumineux ({lignes} lignes)")
        
        for verif in verifications:
            print(f"   {verif}")
        
        return all("✅" in v for v in verifications)
        
    except Exception as e:
        log(f"❌ Erreur vérification: {e}")
        return False

def scan_et_correction_complete():
    """Scan complet et correction des problèmes"""
    log("🔍 SCAN ET CORRECTION COMPLÈTE")
    print("=" * 60)
    
    # 1. Scanner problèmes
    problemes = scanner_problemes_code()
    
    if not problemes:
        log("✅ Aucun problème détecté")
        return True
    
    # 2. Afficher problèmes
    print(f"\n🚨 PROBLÈMES DÉTECTÉS: {len(problemes)}")
    print("=" * 40)
    
    for i, probleme in enumerate(problemes, 1):
        print(f"{i}. {probleme['type']} (ligne {probleme['ligne']})")
        print(f"   Problème: {probleme['probleme']}")
        print(f"   Correction: {probleme['correction']}")
        print()
    
    # 3. Corriger
    print("🔧 APPLICATION CORRECTIONS...")
    if corriger_problemes_specifiques(problemes):
        print("✅ Corrections appliquées")
    else:
        print("❌ Échec corrections")
        return False
    
    # 4. Vérifier
    print("\n🧪 VÉRIFICATION...")
    if verifier_apres_correction():
        print("✅ Code corrigé et fonctionnel")
        return True
    else:
        print("❌ Problèmes subsistent")
        return False

if __name__ == "__main__":
    print("🔍 SCANNER PROBLÈMES SPÉCIFIQUES")
    print("Corrige SEULEMENT ce qui ne va pas")
    print("=" * 50)
    
    if scan_et_correction_complete():
        print("\n🎉 SCAN ET CORRECTION RÉUSSIS")
        print("Le code a été corrigé sans tout changer")
        print("🚀 Vous pouvez maintenant lancer JARVIS")
    else:
        print("\n❌ PROBLÈMES PERSISTANTS")
        print("Vérification manuelle nécessaire")
