#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 CHERCHEUR SAUVEGARDES FONCTIONNELLES - JEAN-LUC PASSAVE
Trouve toutes les sauvegardes JARVIS et teste leur fonctionnalité
"""

import os
import subprocess
import time
import requests
from datetime import datetime
import shutil

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔍 [{timestamp}] {message}")

def scanner_toutes_sauvegardes():
    """Scanne tous les répertoires pour trouver les sauvegardes JARVIS"""
    log("🔍 SCAN COMPLET SAUVEGARDES JARVIS")
    
    # Répertoires à scanner
    repertoires_a_scanner = [
        ".",
        "SAUVEGARDES",
        "/Volumes/T7",
        "/Volumes/T7/JARVIS_BRAIN",
        "/Volumes/seagate"
    ]
    
    sauvegardes_trouvees = []
    
    for repertoire in repertoires_a_scanner:
        if os.path.exists(repertoire):
            log(f"📁 Scan: {repertoire}")
            
            try:
                for root, dirs, files in os.walk(repertoire):
                    for file in files:
                        if 'jarvis' in file.lower() and file.endswith('.py'):
                            chemin_complet = os.path.join(root, file)
                            taille = os.path.getsize(chemin_complet)
                            modif = os.path.getmtime(chemin_complet)
                            
                            sauvegardes_trouvees.append({
                                'fichier': file,
                                'chemin': chemin_complet,
                                'taille': taille,
                                'modification': datetime.fromtimestamp(modif),
                                'repertoire': root
                            })
                            
                            print(f"   ✅ {file} ({taille} bytes)")
            except Exception as e:
                log(f"❌ Erreur scan {repertoire}: {e}")
    
    # Trier par date de modification
    sauvegardes_trouvees.sort(key=lambda x: x['modification'], reverse=True)
    
    log(f"📊 TOTAL: {len(sauvegardes_trouvees)} sauvegardes trouvées")
    return sauvegardes_trouvees

def analyser_qualite_sauvegarde(chemin_fichier):
    """Analyse la qualité d'une sauvegarde"""
    try:
        with open(chemin_fichier, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        score_qualite = 0
        problemes = []
        
        # Critères de qualité
        if 'def create_interface' in contenu:
            score_qualite += 20
        else:
            problemes.append("Fonction create_interface manquante")
        
        if 'gr.Blocks' in contenu:
            score_qualite += 20
        else:
            problemes.append("Interface Gradio manquante")
        
        if 'demo.launch' in contenu:
            score_qualite += 20
        else:
            problemes.append("Lancement demo manquant")
        
        if 'thermal_memory' in contenu.lower():
            score_qualite += 15
        else:
            problemes.append("Mémoire thermique manquante")
        
        if 'deepseek' in contenu.lower():
            score_qualite += 10
        else:
            problemes.append("Intégration DeepSeek manquante")
        
        # Pénalités
        if len(contenu.split('\n')) > 5000:
            score_qualite -= 10
            problemes.append("Code trop long")
        
        if contenu.count('import') > 20:
            score_qualite -= 5
            problemes.append("Trop d'imports")
        
        lignes = len(contenu.split('\n'))
        
        return {
            'score': max(0, score_qualite),
            'problemes': problemes,
            'lignes': lignes,
            'analysable': True
        }
        
    except Exception as e:
        return {
            'score': 0,
            'problemes': [f"Erreur lecture: {e}"],
            'lignes': 0,
            'analysable': False
        }

def tester_sauvegarde_fonctionnelle(chemin_fichier):
    """Teste si une sauvegarde est fonctionnelle"""
    log(f"🧪 TEST FONCTIONNALITÉ: {os.path.basename(chemin_fichier)}")
    
    try:
        # Copier temporairement
        fichier_test = "jarvis_test_temp.py"
        shutil.copy2(chemin_fichier, fichier_test)
        
        # Tenter de lancer
        process = subprocess.Popen([
            "python3", fichier_test
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre 10 secondes
        time.sleep(10)
        
        # Vérifier si l'interface répond
        try:
            response = requests.get("http://localhost:7860", timeout=3)
            if response.status_code == 200:
                log("✅ Interface accessible")
                
                # Arrêter le test
                process.terminate()
                time.sleep(2)
                
                # Nettoyer
                os.remove(fichier_test)
                
                return True
        except:
            pass
        
        # Arrêter le processus
        process.terminate()
        time.sleep(2)
        
        # Nettoyer
        if os.path.exists(fichier_test):
            os.remove(fichier_test)
        
        log("❌ Interface non accessible")
        return False
        
    except Exception as e:
        log(f"❌ Erreur test: {e}")
        return False

def evaluer_toutes_sauvegardes():
    """Évalue toutes les sauvegardes trouvées"""
    log("📊 ÉVALUATION TOUTES SAUVEGARDES")
    print("=" * 60)
    
    sauvegardes = scanner_toutes_sauvegardes()
    
    if not sauvegardes:
        log("❌ Aucune sauvegarde trouvée")
        return []
    
    sauvegardes_evaluees = []
    
    for i, sauvegarde in enumerate(sauvegardes[:10], 1):  # Limiter à 10
        log(f"📋 ÉVALUATION {i}/10: {sauvegarde['fichier']}")
        
        # Analyser qualité
        qualite = analyser_qualite_sauvegarde(sauvegarde['chemin'])
        
        sauvegarde.update(qualite)
        sauvegardes_evaluees.append(sauvegarde)
        
        print(f"   Score: {qualite['score']}/85")
        print(f"   Lignes: {qualite['lignes']}")
        print(f"   Modifié: {sauvegarde['modification'].strftime('%Y-%m-%d %H:%M')}")
        
        if qualite['problemes']:
            print(f"   Problèmes: {', '.join(qualite['problemes'][:3])}")
        
        print()
    
    # Trier par score
    sauvegardes_evaluees.sort(key=lambda x: x['score'], reverse=True)
    
    return sauvegardes_evaluees

def recommander_meilleure_sauvegarde():
    """Recommande la meilleure sauvegarde"""
    log("🎯 RECOMMANDATION MEILLEURE SAUVEGARDE")
    
    sauvegardes = evaluer_toutes_sauvegardes()
    
    if not sauvegardes:
        log("❌ Aucune sauvegarde disponible")
        return None
    
    print("=" * 60)
    log("🏆 TOP 3 MEILLEURES SAUVEGARDES")
    print("=" * 60)
    
    for i, sauvegarde in enumerate(sauvegardes[:3], 1):
        print(f"{i}. {sauvegarde['fichier']}")
        print(f"   📊 Score: {sauvegarde['score']}/85")
        print(f"   📁 Chemin: {sauvegarde['chemin']}")
        print(f"   📏 Taille: {sauvegarde['lignes']} lignes")
        print(f"   📅 Modifié: {sauvegarde['modification'].strftime('%Y-%m-%d %H:%M')}")
        
        if sauvegarde['score'] >= 70:
            print(f"   ✅ EXCELLENTE QUALITÉ")
        elif sauvegarde['score'] >= 50:
            print(f"   ⚠️  QUALITÉ CORRECTE")
        else:
            print(f"   ❌ QUALITÉ FAIBLE")
        
        print()
    
    return sauvegardes[0] if sauvegardes else None

def restaurer_sauvegarde(sauvegarde):
    """Restaure une sauvegarde spécifique"""
    log(f"🔄 RESTAURATION: {sauvegarde['fichier']}")
    
    try:
        # Sauvegarder l'actuel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_actuel = f"jarvis_interface_propre_backup_{timestamp}.py"
        
        if os.path.exists("jarvis_interface_propre.py"):
            shutil.copy2("jarvis_interface_propre.py", backup_actuel)
            log(f"💾 Actuel sauvé: {backup_actuel}")
        
        # Restaurer la sauvegarde
        shutil.copy2(sauvegarde['chemin'], "jarvis_interface_propre.py")
        log("✅ Sauvegarde restaurée")
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur restauration: {e}")
        return False

def menu_sauvegardes():
    """Menu interactif pour gérer les sauvegardes"""
    print("🔍 CHERCHEUR SAUVEGARDES FONCTIONNELLES")
    print("=" * 50)
    print("1. Scanner toutes les sauvegardes")
    print("2. Évaluer et recommander")
    print("3. Tester fonctionnalité d'une sauvegarde")
    print("4. Restaurer meilleure sauvegarde")
    
    choix = input("\nVotre choix (1-4): ").strip()
    
    if choix == "1":
        sauvegardes = scanner_toutes_sauvegardes()
        print(f"\n📊 {len(sauvegardes)} sauvegardes trouvées")
        
    elif choix == "2":
        meilleure = recommander_meilleure_sauvegarde()
        if meilleure:
            print(f"\n🏆 RECOMMANDATION: {meilleure['fichier']}")
            print(f"Score: {meilleure['score']}/85")
        
    elif choix == "3":
        sauvegardes = scanner_toutes_sauvegardes()
        if sauvegardes:
            print("\nSauvegardes disponibles:")
            for i, s in enumerate(sauvegardes[:5], 1):
                print(f"{i}. {s['fichier']}")
            
            try:
                idx = int(input("Numéro à tester: ")) - 1
                if 0 <= idx < len(sauvegardes):
                    tester_sauvegarde_fonctionnelle(sauvegardes[idx]['chemin'])
            except:
                print("❌ Choix invalide")
        
    elif choix == "4":
        meilleure = recommander_meilleure_sauvegarde()
        if meilleure:
            confirm = input(f"\nRestaurer {meilleure['fichier']} ? (o/n): ")
            if confirm.lower() == 'o':
                if restaurer_sauvegarde(meilleure):
                    print("✅ Sauvegarde restaurée avec succès")
                    print("🚀 Vous pouvez maintenant lancer JARVIS")
                else:
                    print("❌ Échec restauration")
    
    else:
        print("❌ Choix invalide")
        recommander_meilleure_sauvegarde()

if __name__ == "__main__":
    menu_sauvegardes()
