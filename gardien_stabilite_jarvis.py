#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ GARDIEN DE STABILITÉ JARVIS - JEAN-LUC PASSAVE
Surveille et maintient automatiquement la stabilité du code JARVIS
"""

import os
import time
import threading
import requests
import subprocess
import re
from datetime import datetime
import hashlib

class GardienStabiliteJarvis:
    def __init__(self):
        self.fichier_jarvis = "jarvis_interface_propre.py"
        self.hash_fichier = None
        self.surveillance_active = False
        self.derniere_verification = None
        
    def log(self, message):
        """Log avec timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"🛡️ [{timestamp}] {message}")
    
    def calculer_hash_fichier(self):
        """Calcule le hash du fichier JARVIS"""
        if not os.path.exists(self.fichier_jarvis):
            return None
        
        with open(self.fichier_jarvis, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def verifier_integrite_code(self):
        """Vérifie l'intégrité du code"""
        if not os.path.exists(self.fichier_jarvis):
            self.log("❌ FICHIER JARVIS MANQUANT !")
            return False
        
        # Vérifications critiques
        with open(self.fichier_jarvis, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        problemes = []
        
        # 1. Format messages
        if 'type="messages"' in contenu:
            problemes.append("FORMAT_MESSAGES_CORROMPU")
        
        # 2. CSS trop complexe
        if contenu.count('custom_css') > 1:
            problemes.append("CSS_DUPLIQUE")
        
        # 3. Imports problématiques
        if 'import threading' in contenu and '# import threading' not in contenu:
            problemes.append("IMPORTS_CORROMPUS")
        
        # 4. Configuration chatbot
        if 'gr.Chatbot(' not in contenu:
            problemes.append("CHATBOT_MANQUANT")
        
        if problemes:
            self.log(f"❌ PROBLÈMES DÉTECTÉS: {problemes}")
            return False
        
        return True
    
    def verifier_services(self):
        """Vérifie que les services fonctionnent"""
        services_ok = True

        # DeepSeek
        try:
            response = requests.get("http://localhost:8000/v1/models", timeout=3)
            if response.status_code != 200:
                self.log("⚠️  DeepSeek non accessible")
                services_ok = False
        except:
            self.log("⚠️  DeepSeek arrêté")
            services_ok = False

        # Interface JARVIS - SURVEILLANCE ERREUR 404
        try:
            response = requests.get("http://localhost:7860", timeout=2)
            if response.status_code == 404:
                self.log("🚨 ERREUR 404 DÉTECTÉE - Interface JARVIS non trouvée")
                services_ok = False
            elif response.status_code != 200:
                self.log(f"⚠️  Interface JARVIS erreur {response.status_code}")
                services_ok = False
            else:
                # Vérifier que la page contient bien JARVIS
                if "JARVIS" not in response.text:
                    self.log("🚨 Page JARVIS corrompue - contenu invalide")
                    services_ok = False
        except requests.exceptions.ConnectionError:
            self.log("🚨 ERREUR 404/CONNEXION - Interface JARVIS inaccessible")
            services_ok = False
        except Exception as e:
            self.log(f"⚠️  Interface JARVIS erreur: {e}")
            services_ok = False

        return services_ok
    
    def auto_corriger(self):
        """Auto-correction automatique"""
        self.log("🔧 AUTO-CORRECTION EN COURS...")
        
        try:
            # Lancer le super correcteur
            result = subprocess.run([
                "python3", "super_correcteur_automatique_jarvis.py"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log("✅ Auto-correction réussie")
                return True
            else:
                self.log(f"❌ Auto-correction échouée: {result.stderr}")
                return False
                
        except Exception as e:
            self.log(f"❌ Erreur auto-correction: {e}")
            return False
    
    def corriger_erreur_404(self):
        """Corrige spécifiquement l'erreur 404"""
        self.log("🚨 CORRECTION ERREUR 404")

        try:
            # 1. Vérifier les ports utilisés
            import socket
            ports_a_tester = [7860, 7861, 7862, 7863]
            port_libre = None

            for port in ports_a_tester:
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.connect(('localhost', port))
                        self.log(f"🔍 Port {port} occupé - test connexion")
                        try:
                            response = requests.get(f"http://localhost:{port}", timeout=2)
                            if response.status_code == 200 and "JARVIS" in response.text:
                                self.log(f"✅ JARVIS trouvé sur port {port}")
                                return True
                        except:
                            pass
                except:
                    if not port_libre:
                        port_libre = port

            # 2. Corriger le port dans le fichier si nécessaire
            if port_libre and port_libre != 7860:
                self.log(f"🔧 Correction port vers {port_libre}")
                with open(self.fichier_jarvis, 'r', encoding='utf-8') as f:
                    contenu = f.read()

                contenu = re.sub(r'server_port=\d+', f'server_port={port_libre}', contenu)

                with open(self.fichier_jarvis, 'w', encoding='utf-8') as f:
                    f.write(contenu)

            # 3. Redémarrer JARVIS
            return self.redemarrer_jarvis()

        except Exception as e:
            self.log(f"❌ Erreur correction 404: {e}")
            return False

    def redemarrer_jarvis(self):
        """Redémarre JARVIS si nécessaire"""
        self.log("🔄 REDÉMARRAGE JARVIS...")

        try:
            # Arrêter processus existants
            subprocess.run(["pkill", "-f", "jarvis_interface_propre.py"],
                         capture_output=True)
            time.sleep(2)

            # Relancer
            if os.path.exists("venv_deepseek/bin/activate"):
                cmd = "source venv_deepseek/bin/activate && python jarvis_interface_propre.py"
            else:
                cmd = "python3 jarvis_interface_propre.py"

            subprocess.Popen(cmd, shell=True, cwd=".")

            # Vérifier démarrage sur plusieurs ports
            ports_a_verifier = [7860, 7861, 7862, 7863]

            for tentative in range(20):
                time.sleep(1)
                for port in ports_a_verifier:
                    try:
                        response = requests.get(f"http://localhost:{port}", timeout=2)
                        if response.status_code == 200 and "JARVIS" in response.text:
                            self.log(f"✅ JARVIS redémarré sur port {port}")
                            return True
                    except:
                        pass

            self.log("⚠️  Redémarrage prend du temps")
            return True

        except Exception as e:
            self.log(f"❌ Erreur redémarrage: {e}")
            return False
    
    def surveillance_continue(self):
        """Surveillance continue du système"""
        self.log("🛡️ SURVEILLANCE CONTINUE ACTIVÉE")
        self.surveillance_active = True
        
        while self.surveillance_active:
            try:
                # Vérification toutes les 30 secondes
                time.sleep(30)
                
                self.derniere_verification = datetime.now()
                
                # 1. Vérifier intégrité du code
                if not self.verifier_integrite_code():
                    self.log("🚨 INTÉGRITÉ COMPROMISE - AUTO-CORRECTION")
                    if self.auto_corriger():
                        self.log("✅ Code restauré")
                    else:
                        self.log("❌ Auto-correction échouée")
                
                # 2. Vérifier services et erreur 404
                if not self.verifier_services():
                    self.log("🚨 SERVICES DÉFAILLANTS")

                    # Vérifier spécifiquement l'erreur 404
                    try:
                        response = requests.get("http://localhost:7860", timeout=2)
                        if response.status_code == 404:
                            self.log("🚨 ERREUR 404 DÉTECTÉE - CORRECTION SPÉCIALISÉE")
                            if self.corriger_erreur_404():
                                self.log("✅ Erreur 404 corrigée")
                            else:
                                self.log("❌ Correction 404 échouée")
                        else:
                            self.log("🔄 REDÉMARRAGE STANDARD")
                            if self.redemarrer_jarvis():
                                self.log("✅ Services restaurés")
                            else:
                                self.log("❌ Redémarrage échoué")
                    except requests.exceptions.ConnectionError:
                        self.log("🚨 CONNEXION IMPOSSIBLE - CORRECTION 404")
                        if self.corriger_erreur_404():
                            self.log("✅ Connexion restaurée")
                        else:
                            self.log("❌ Correction connexion échouée")
                    except:
                        self.log("🔄 REDÉMARRAGE STANDARD")
                        if self.redemarrer_jarvis():
                            self.log("✅ Services restaurés")
                        else:
                            self.log("❌ Redémarrage échoué")
                
                # 3. Vérifier changements fichier
                hash_actuel = self.calculer_hash_fichier()
                if self.hash_fichier and hash_actuel != self.hash_fichier:
                    self.log("📝 Fichier modifié - vérification intégrité")
                    if not self.verifier_integrite_code():
                        self.auto_corriger()
                
                self.hash_fichier = hash_actuel
                
            except Exception as e:
                self.log(f"❌ Erreur surveillance: {e}")
                time.sleep(10)
    
    def demarrer_gardien(self):
        """Démarre le gardien en arrière-plan"""
        self.log("🛡️ DÉMARRAGE GARDIEN DE STABILITÉ")
        
        # Hash initial
        self.hash_fichier = self.calculer_hash_fichier()
        
        # Vérification initiale
        if not self.verifier_integrite_code():
            self.log("🔧 Correction initiale nécessaire")
            self.auto_corriger()
        
        # Démarrer surveillance en thread
        thread_surveillance = threading.Thread(
            target=self.surveillance_continue,
            daemon=True
        )
        thread_surveillance.start()
        
        self.log("✅ Gardien actif - surveillance continue")
        return thread_surveillance
    
    def arreter_gardien(self):
        """Arrête le gardien"""
        self.surveillance_active = False
        self.log("🛑 Gardien arrêté")
    
    def status_gardien(self):
        """Affiche le statut du gardien"""
        print("🛡️ STATUT GARDIEN DE STABILITÉ")
        print("=" * 40)
        
        if self.surveillance_active:
            print("🟢 Surveillance: ACTIVE")
            if self.derniere_verification:
                print(f"🕐 Dernière vérif: {self.derniere_verification.strftime('%H:%M:%S')}")
        else:
            print("🔴 Surveillance: INACTIVE")
        
        # Vérifications actuelles
        integrite = self.verifier_integrite_code()
        services = self.verifier_services()
        
        print(f"📁 Intégrité code: {'✅ OK' if integrite else '❌ KO'}")
        print(f"🔧 Services: {'✅ OK' if services else '❌ KO'}")
        
        if self.hash_fichier:
            print(f"🔐 Hash fichier: {self.hash_fichier[:8]}...")

if __name__ == "__main__":
    gardien = GardienStabiliteJarvis()
    
    print("🛡️ GARDIEN DE STABILITÉ JARVIS")
    print("1. Démarrer surveillance continue")
    print("2. Vérification unique")
    print("3. Auto-correction forcée")
    print("4. Statut gardien")
    
    choix = input("\nVotre choix (1-4): ").strip()
    
    if choix == "1":
        thread = gardien.demarrer_gardien()
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            gardien.arreter_gardien()
            print("\n🛑 Gardien arrêté")
    
    elif choix == "2":
        gardien.status_gardien()
    
    elif choix == "3":
        gardien.auto_corriger()
    
    elif choix == "4":
        gardien.status_gardien()
    
    else:
        print("❌ Choix invalide")
