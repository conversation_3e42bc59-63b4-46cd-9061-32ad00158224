#!/usr/bin/env python3
"""
🧹 NETTOYAGE MÉMOIRE THERMIQUE JARVIS
Unifie et déduplique la mémoire thermique de Jean-Luc
"""

import json
import time
from datetime import datetime

def nettoyer_memoire_thermique():
    """Nettoie et unifie la mémoire thermique"""
    
    print("🧹 NETTOYAGE MÉMOIRE THERMIQUE JARVIS")
    print("=" * 50)
    
    # Charger mémoire actuelle
    try:
        with open("thermal_memory_persistent.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Mémoire chargée: {len(data.get('conversations', []))} entrées")
    except Exception as e:
        print(f"❌ Erreur chargement: {e}")
        return
    
    # Analyser structure
    conversations = data.get('conversations', [])
    print(f"📊 Analyse: {len(conversations)} conversations trouvées")
    
    # Structures détectées
    formats = {
        'user_message': 0,
        'sender_content': 0,
        'jarvis_only': 0,
        'mcp_format': 0
    }
    
    for conv in conversations:
        if 'user_message' in conv and 'assistant_response' in conv:
            formats['user_message'] += 1
        elif 'sender' in conv and 'content' in conv:
            formats['sender_content'] += 1
        elif 'jarvis' in conv:
            formats['jarvis_only'] += 1
        elif 'user' in conv and 'mcp_detected' in conv:
            formats['mcp_format'] += 1
    
    print("📋 Formats détectés:")
    for fmt, count in formats.items():
        print(f"   {fmt}: {count} entrées")
    
    # Unifier format
    conversations_unifiees = []
    id_counter = int(time.time() * 1000)
    
    for conv in conversations:
        try:
            # Format unifié
            entry_unifiee = {
                "id": id_counter,
                "timestamp": conv.get('timestamp', time.strftime("%Y-%m-%dT%H:%M:%S.000Z")),
                "agent": "agent1"
            }
            
            # Traiter selon format
            if 'user_message' in conv and 'assistant_response' in conv:
                # Format ancien
                entry_unifiee.update({
                    "user_message": conv['user_message'],
                    "assistant_response": conv['assistant_response'],
                    "thermal_zone": conv.get('thermal_zone', 'zone_agent1'),
                    "keywords": conv.get('keywords', []),
                    "length": conv.get('length', len(conv['user_message'])),
                    "complexity": conv.get('complexity', 0.5)
                })
                conversations_unifiees.append(entry_unifiee)
                id_counter += 1
                
            elif 'sender' in conv and 'content' in conv:
                # Format nouveau
                if conv['sender'] == 'user':
                    # Chercher réponse correspondante
                    entry_unifiee.update({
                        "user_message": conv['content'],
                        "assistant_response": "Réponse en attente...",
                        "thermal_zone": conv.get('thermal_zone', 'input_processing'),
                        "keywords": conv.get('keywords', []),
                        "length": conv.get('length', len(conv['content'])),
                        "complexity": conv.get('complexity', 0.5)
                    })
                    conversations_unifiees.append(entry_unifiee)
                    id_counter += 1
                    
            elif 'jarvis' in conv:
                # Format JARVIS seul
                entry_unifiee.update({
                    "user_message": "Message système",
                    "assistant_response": conv['jarvis'],
                    "thermal_zone": "system_response",
                    "keywords": ["jarvis", "système"],
                    "length": len(conv['jarvis']),
                    "complexity": 0.3,
                    "qi": conv.get('qi', 392.0)
                })
                conversations_unifiees.append(entry_unifiee)
                id_counter += 1
                
        except Exception as e:
            print(f"⚠️ Erreur traitement entrée: {e}")
            continue
    
    print(f"🔄 Unification: {len(conversations_unifiees)} conversations unifiées")
    
    # Dédupliquer
    conversations_dedupliquees = []
    contenus_vus = set()
    
    for conv in conversations_unifiees:
        # Clé de déduplication
        cle = f"{conv.get('user_message', '')}_{conv.get('assistant_response', '')}"[:100]
        
        if cle not in contenus_vus:
            contenus_vus.add(cle)
            conversations_dedupliquees.append(conv)
    
    print(f"🗑️ Déduplication: {len(conversations_dedupliquees)} conversations uniques")
    
    # Structure finale propre
    memoire_propre = {
        "conversations": conversations_dedupliquees,
        "conversations_by_date": {},
        "thermal_stats": {
            "total_entries": len(conversations_dedupliquees),
            "memory_size_mb": 0,
            "active_zones": ["zone_agent1", "input_processing", "system_response"],
            "thermal_zone_priority": {
                "zone_agent1": 1.0,
                "input_processing": 0.8,
                "system_response": 0.6
            },
            "user_habits": {
                "preferred_language": "français",
                "interaction_style": "technique_détaillé",
                "frequent_topics": ["jarvis", "mémoire", "système"]
            }
        },
        "learning_data": {
            "recurring_queries": {},
            "user_preferences": {
                "response_style": "détaillé",
                "technical_level": "avancé"
            },
            "conversation_patterns": {}
        },
        "lastUpdate": time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
        "totalEntries": len(conversations_dedupliquees),
        "version": "2.0_unified"
    }
    
    # Organiser par date
    for conv in conversations_dedupliquees:
        try:
            timestamp = conv.get('timestamp', '')
            if timestamp:
                date_key = timestamp[:10]  # YYYY-MM-DD
                if date_key not in memoire_propre["conversations_by_date"]:
                    memoire_propre["conversations_by_date"][date_key] = []
                memoire_propre["conversations_by_date"][date_key].append(conv)
        except:
            continue
    
    # Sauvegarder backup
    backup_name = f"thermal_memory_backup_{int(time.time())}.json"
    try:
        with open(backup_name, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 Backup sauvé: {backup_name}")
    except Exception as e:
        print(f"⚠️ Erreur backup: {e}")
    
    # Sauvegarder version propre
    try:
        with open("thermal_memory_persistent.json", 'w', encoding='utf-8') as f:
            json.dump(memoire_propre, f, ensure_ascii=False, indent=2)
        print(f"✅ Mémoire nettoyée sauvée: {len(conversations_dedupliquees)} conversations")
    except Exception as e:
        print(f"❌ Erreur sauvegarde: {e}")
        return
    
    # Statistiques finales
    print("\n📊 RÉSULTAT FINAL:")
    print(f"   🗑️ Doublons supprimés: {len(conversations) - len(conversations_dedupliquees)}")
    print(f"   ✅ Conversations uniques: {len(conversations_dedupliquees)}")
    print(f"   📅 Dates organisées: {len(memoire_propre['conversations_by_date'])}")
    print(f"   🧠 Structure unifiée: Version 2.0")
    print(f"   💾 Backup disponible: {backup_name}")
    
    return True

if __name__ == "__main__":
    nettoyer_memoire_thermique()
