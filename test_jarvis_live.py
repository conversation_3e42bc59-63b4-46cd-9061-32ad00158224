#!/usr/bin/env python3
"""
🧪 TEST LIVE COMPLET DE JARVIS
Test toutes les capacités de JARVIS en temps réel
"""

import requests
import json
import time
import os

def test_jarvis_interface():
    """Test l'interface JARVIS"""
    print("🧪 TEST 1: Interface JARVIS")
    try:
        response = requests.get('http://localhost:7863', timeout=5)
        if response.status_code == 200:
            print("✅ Interface accessible sur localhost:7863")
            return True
        else:
            print(f"❌ Interface erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Interface inaccessible: {e}")
        return False

def test_deepseek_model():
    """Test le modèle DeepSeek R1"""
    print("\n🧪 TEST 2: Modèle DeepSeek R1 8B")
    try:
        response = requests.post('http://localhost:8000/v1/chat/completions', 
            json={
                'model': 'deepseek-ai/DeepSeek-R1-0528',
                'messages': [{'role': 'user', 'content': 'Bonjour, réponds juste "JARVIS OK"'}],
                'max_tokens': 10
            }, 
            timeout=15
        )
        if response.status_code == 200:
            print("✅ DeepSeek R1 8B opérationnel")
            data = response.json()
            if 'choices' in data:
                content = data['choices'][0]['message']['content']
                print(f"💬 Réponse: {content}")
            return True
        else:
            print(f"❌ DeepSeek erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ DeepSeek inaccessible: {e}")
        return False

def test_jarvis_modules():
    """Test les modules JARVIS"""
    print("\n🧪 TEST 3: Modules JARVIS")
    modules = {
        'jarvis_button_diagnostics.py': 'Diagnostic boutons',
        'jarvis_security_biometric.py': 'Sécurité biométrique', 
        'jarvis_whatsapp_api_real.py': 'WhatsApp API',
        'jarvis_creative_autonomy.py': 'Créativité autonome',
        'jarvis_music_module.py': 'Module musical',
        'jarvis_t7_sync_engine.py': 'Synchronisation T7',
        'jarvis_vpn_security.py': 'Sécurité VPN',
        'jarvis_monitoring_dashboard.py': 'Monitoring'
    }
    
    present = 0
    for module, description in modules.items():
        if os.path.exists(module):
            print(f"✅ {description}")
            present += 1
        else:
            print(f"❌ {description}")
    
    print(f"\n📊 Modules: {present}/{len(modules)} présents ({present/len(modules)*100:.0f}%)")
    return present >= len(modules) * 0.8  # 80% minimum

def test_memory_system():
    """Test le système de mémoire"""
    print("\n🧪 TEST 4: Système de mémoire")
    
    # Mémoire thermique
    if os.path.exists('thermal_memory_persistent.json'):
        size = os.path.getsize('thermal_memory_persistent.json') / (1024*1024)
        print(f"✅ Mémoire thermique: {size:.1f} MB")
    else:
        print("❌ Mémoire thermique manquante")
        return False
    
    # Sauvegardes
    if os.path.exists('SAUVEGARDES'):
        backups = len([f for f in os.listdir('SAUVEGARDES') if f.startswith('AUTO_JARVIS')])
        print(f"✅ Sauvegardes automatiques: {backups} fichiers")
    else:
        print("❌ Dossier sauvegardes manquant")
        return False
    
    return True

def test_diagnostic_system():
    """Test le système de diagnostic"""
    print("\n🧪 TEST 5: Système de diagnostic")
    try:
        from jarvis_button_diagnostics import test_all_functions
        results = test_all_functions()
        total = len(results)
        working = sum(1 for r in results.values() if r['success'])
        percentage = (working / total) * 100
        
        print(f"📊 Diagnostic: {working}/{total} boutons fonctionnels ({percentage:.0f}%)")
        
        for func, result in results.items():
            status = '✅' if result['success'] else '❌'
            print(f"  {status} {func}")
        
        return percentage >= 80  # 80% minimum
    except Exception as e:
        print(f"❌ Erreur diagnostic: {e}")
        return False

def test_electron_app():
    """Test l'application Electron"""
    print("\n🧪 TEST 6: Application Electron")
    
    electron_files = [
        'jarvis_electron_launcher.js',
        'jarvis_launcher.html',
        'package.json'
    ]
    
    present = 0
    for file in electron_files:
        if os.path.exists(file):
            print(f"✅ {file}")
            present += 1
        else:
            print(f"❌ {file}")
    
    return present == len(electron_files)

def main():
    """Test complet de JARVIS"""
    print("🚀 TEST LIVE COMPLET DE JARVIS")
    print("=" * 50)
    
    tests = [
        ("Interface", test_jarvis_interface),
        ("DeepSeek R1", test_deepseek_model),
        ("Modules", test_jarvis_modules),
        ("Mémoire", test_memory_system),
        ("Diagnostic", test_diagnostic_system),
        ("Electron", test_electron_app)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur test {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ FINAL DES TESTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSÉ" if result else "❌ ÉCHEC"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    total_score = (passed / len(results)) * 100
    print(f"\n🎯 SCORE GLOBAL: {passed}/{len(results)} ({total_score:.0f}%)")
    
    if total_score >= 90:
        print("🎉 JARVIS EST EXCELLENT !")
    elif total_score >= 70:
        print("✅ JARVIS EST OPÉRATIONNEL !")
    else:
        print("⚠️ JARVIS NÉCESSITE DES CORRECTIONS")
    
    return total_score

if __name__ == "__main__":
    score = main()
    exit(0 if score >= 70 else 1)
