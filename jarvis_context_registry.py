#!/usr/bin/env python3
"""
🧠 JARVIS CONTEXT REGISTRY
Système de registre de contexte pour éviter l'hésitation de JARVIS
Permet de tracer les sujets de conversation et d'y faire référence avec confiance

Créé pour <PERSON>
"""

import json
import time
import os
from datetime import datetime, timedelta
import hashlib

class JarvisContextRegistry:
    def __init__(self):
        self.context_file = "jarvis_context_registry.json"
        self.max_context_entries = 50  # Garder les 50 derniers contextes
        self.context_registry = self.load_context_registry()
        
    def load_context_registry(self):
        """CHARGE LE REGISTRE DE CONTEXTE"""
        try:
            if os.path.exists(self.context_file):
                with open(self.context_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    "contexts": [],
                    "current_session": {
                        "session_id": self.generate_session_id(),
                        "start_time": datetime.now().isoformat(),
                        "topics": [],
                        "last_question": None,
                        "last_response": None
                    },
                    "stats": {
                        "total_contexts": 0,
                        "successful_references": 0,
                        "confidence_level": 0.0
                    }
                }
        except Exception as e:
            print(f"❌ Erreur chargement registre contexte: {e}")
            return {"contexts": [], "current_session": {}, "stats": {}}
    
    def save_context_registry(self):
        """SAUVEGARDE LE REGISTRE DE CONTEXTE"""
        try:
            with open(self.context_file, 'w', encoding='utf-8') as f:
                json.dump(self.context_registry, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde registre contexte: {e}")
    
    def generate_session_id(self):
        """GÉNÈRE UN ID DE SESSION UNIQUE"""
        timestamp = str(int(time.time()))
        return hashlib.md5(timestamp.encode()).hexdigest()[:8]
    
    def register_context(self, user_message, agent_response, topic_keywords=None):
        """ENREGISTRE UN NOUVEAU CONTEXTE"""
        try:
            timestamp = datetime.now().isoformat()
            
            # Extraire les mots-clés automatiquement si non fournis
            if not topic_keywords:
                topic_keywords = self.extract_keywords(user_message)
            
            # Générer un ID de contexte
            context_id = hashlib.md5(f"{timestamp}{user_message}".encode()).hexdigest()[:12]
            
            # Créer l'entrée de contexte
            context_entry = {
                "id": context_id,
                "timestamp": timestamp,
                "user_message": user_message,
                "agent_response": agent_response[:200],  # Résumé de la réponse
                "topic_keywords": topic_keywords,
                "session_id": self.context_registry["current_session"]["session_id"],
                "confidence_score": self.calculate_confidence_score(user_message, agent_response),
                "message_type": self.classify_message_type(user_message)
            }
            
            # Ajouter au registre
            self.context_registry["contexts"].append(context_entry)
            
            # Mettre à jour la session courante
            self.context_registry["current_session"]["last_question"] = user_message
            self.context_registry["current_session"]["last_response"] = agent_response[:100]
            self.context_registry["current_session"]["topics"].extend(topic_keywords)
            
            # Garder seulement les derniers contextes
            if len(self.context_registry["contexts"]) > self.max_context_entries:
                self.context_registry["contexts"] = self.context_registry["contexts"][-self.max_context_entries:]
            
            # Mettre à jour les stats
            self.context_registry["stats"]["total_contexts"] = len(self.context_registry["contexts"])
            
            self.save_context_registry()
            return context_id
            
        except Exception as e:
            print(f"❌ Erreur enregistrement contexte: {e}")
            return None
    
    def extract_keywords(self, text):
        """EXTRAIT LES MOTS-CLÉS D'UN TEXTE"""
        try:
            # Mots vides à ignorer
            stop_words = {
                'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais', 'donc', 'car',
                'que', 'qui', 'quoi', 'où', 'quand', 'comment', 'pourquoi', 'est', 'sont', 'était',
                'tu', 'vous', 'je', 'nous', 'il', 'elle', 'ils', 'elles', 'ce', 'cette', 'ces',
                'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'votre', 'leur',
                'dans', 'sur', 'avec', 'sans', 'pour', 'par', 'vers', 'chez', 'entre', 'sous', 'over'
            }
            
            # Nettoyer et extraire les mots
            words = text.lower().replace('?', '').replace('!', '').replace('.', '').replace(',', '').split()
            keywords = [word for word in words if len(word) > 3 and word not in stop_words]
            
            # Garder les 5 mots-clés les plus pertinents
            return keywords[:5]
            
        except Exception as e:
            print(f"❌ Erreur extraction mots-clés: {e}")
            return []
    
    def classify_message_type(self, message):
        """CLASSIFIE LE TYPE DE MESSAGE"""
        try:
            message_lower = message.lower()
            
            if any(word in message_lower for word in ['?', 'comment', 'pourquoi', 'quoi', 'où', 'quand']):
                return "question"
            elif any(word in message_lower for word in ['merci', 'bravo', 'excellent', 'parfait']):
                return "appreciation"
            elif any(word in message_lower for word in ['crée', 'génère', 'fais', 'écris', 'développe']):
                return "creation_request"
            elif any(word in message_lower for word in ['explique', 'montre', 'présente', 'décris']):
                return "explanation_request"
            elif any(word in message_lower for word in ['souviens', 'rappelle', 'précédent', 'avant']):
                return "memory_reference"
            else:
                return "general"
                
        except Exception as e:
            print(f"❌ Erreur classification message: {e}")
            return "unknown"
    
    def calculate_confidence_score(self, user_message, agent_response):
        """CALCULE UN SCORE DE CONFIANCE POUR LE CONTEXTE"""
        try:
            score = 5.0  # Score de base
            
            # Bonus pour longueur du message
            if len(user_message) > 50:
                score += 1.0
            
            # Bonus pour réponse détaillée
            if len(agent_response) > 200:
                score += 1.0
            
            # Bonus pour mots-clés techniques
            tech_keywords = ['code', 'python', 'jarvis', 'ia', 'intelligence', 'artificielle', 'module']
            if any(keyword in user_message.lower() for keyword in tech_keywords):
                score += 2.0
            
            # Malus pour salutations simples
            simple_greetings = ['salut', 'bonjour', 'hello', 'hi', 'bonsoir']
            if any(greeting in user_message.lower() for greeting in simple_greetings) and len(user_message.split()) <= 3:
                score -= 2.0
            
            return min(10.0, max(1.0, score))  # Score entre 1 et 10
            
        except Exception as e:
            print(f"❌ Erreur calcul score confiance: {e}")
            return 5.0
    
    def find_relevant_context(self, current_message):
        """TROUVE LE CONTEXTE PERTINENT POUR LE MESSAGE ACTUEL"""
        try:
            current_keywords = self.extract_keywords(current_message)
            message_type = self.classify_message_type(current_message)
            
            # Recherche par mots-clés
            relevant_contexts = []
            
            for context in reversed(self.context_registry["contexts"][-10:]):  # 10 derniers contextes
                # Score de pertinence
                relevance_score = 0
                
                # Correspondance mots-clés
                common_keywords = set(current_keywords) & set(context["topic_keywords"])
                relevance_score += len(common_keywords) * 2
                
                # Bonus pour même type de message
                if context["message_type"] == message_type:
                    relevance_score += 1
                
                # Bonus pour récence (dernières 24h)
                context_time = datetime.fromisoformat(context["timestamp"])
                if datetime.now() - context_time < timedelta(hours=24):
                    relevance_score += 1
                
                # Bonus pour score de confiance élevé
                if context["confidence_score"] > 7:
                    relevance_score += 1
                
                if relevance_score > 2:  # Seuil de pertinence
                    relevant_contexts.append({
                        "context": context,
                        "relevance_score": relevance_score
                    })
            
            # Trier par pertinence
            relevant_contexts.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            return relevant_contexts[:3]  # Top 3 contextes pertinents
            
        except Exception as e:
            print(f"❌ Erreur recherche contexte pertinent: {e}")
            return []
    
    def generate_confident_response_context(self, current_message):
        """GÉNÈRE UN CONTEXTE POUR UNE RÉPONSE CONFIANTE"""
        try:
            relevant_contexts = self.find_relevant_context(current_message)
            
            if not relevant_contexts:
                return ""
            
            # Construire le contexte de confiance
            context_text = "\n🧠 CONTEXTE DE CONFIANCE (pour répondre avec assurance) :\n"
            
            for i, item in enumerate(relevant_contexts, 1):
                context = item["context"]
                context_text += f"\n{i}. CONTEXTE RÉCENT (Score: {item['relevance_score']}) :\n"
                context_text += f"   📅 {context['timestamp'][:16]}\n"
                context_text += f"   💬 Question: {context['user_message'][:100]}...\n"
                context_text += f"   🎯 Mots-clés: {', '.join(context['topic_keywords'])}\n"
                context_text += f"   📝 Type: {context['message_type']}\n"
            
            context_text += f"\n✅ INSTRUCTION : Utilise ces contextes pour répondre avec CONFIANCE et PRÉCISION. Ne dis pas 'je suppose' ou 'je ne peux pas vérifier' - tu as ces informations dans ta mémoire contextuelle !\n"
            
            return context_text
            
        except Exception as e:
            print(f"❌ Erreur génération contexte confiant: {e}")
            return ""
    
    def get_context_stats(self):
        """RETOURNE LES STATISTIQUES DU REGISTRE DE CONTEXTE"""
        try:
            stats = self.context_registry.get("stats", {})
            current_session = self.context_registry.get("current_session", {})
            
            return {
                "total_contexts": len(self.context_registry["contexts"]),
                "current_session_topics": len(set(current_session.get("topics", []))),
                "session_id": current_session.get("session_id", "unknown"),
                "confidence_level": stats.get("confidence_level", 0.0),
                "last_context_time": self.context_registry["contexts"][-1]["timestamp"] if self.context_registry["contexts"] else None
            }
            
        except Exception as e:
            print(f"❌ Erreur stats contexte: {e}")
            return {}

# Instance globale
context_registry = JarvisContextRegistry()

def register_conversation_context(user_message, agent_response):
    """FONCTION UTILITAIRE POUR ENREGISTRER UN CONTEXTE"""
    return context_registry.register_context(user_message, agent_response)

def get_confident_context_for_message(message):
    """FONCTION UTILITAIRE POUR OBTENIR UN CONTEXTE CONFIANT"""
    return context_registry.generate_confident_response_context(message)

if __name__ == "__main__":
    print("🧠 JARVIS CONTEXT REGISTRY")
    print("==========================")
    
    # Test du système
    registry = JarvisContextRegistry()
    
    # Enregistrer un contexte de test
    context_id = registry.register_context(
        "Comment fonctionne le module musical de JARVIS ?",
        "Le module musical de JARVIS connaît vos goûts : Funk, Blues, R&B, Pop, Reggae, Dancehall. Il peut suggérer des chansons et en créer.",
        ["musical", "jarvis", "funk", "blues"]
    )
    
    print(f"✅ Contexte enregistré: {context_id}")
    
    # Tester la recherche de contexte
    relevant = registry.find_relevant_context("Peux-tu me créer une chanson funk ?")
    print(f"🔍 Contextes pertinents trouvés: {len(relevant)}")
    
    # Afficher les stats
    stats = registry.get_context_stats()
    print(f"📊 Stats: {stats}")
