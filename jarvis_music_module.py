#!/usr/bin/env python3
"""
🎵 JARVIS MUSIC MODULE
Module musical personnalisé pour Jean-Luc Passave
Goûts musicaux : Funk, Blues, R&B, Pop, Reggae, Dancehall

Créé pour Jean-Luc Passave
"""

import json
import time
import requests
import random
from datetime import datetime
import os

class JarvisMusicModule:
    def __init__(self):
        self.music_preferences_file = "jarvis_music_preferences.json"
        self.music_history_file = "jarvis_music_history.json"
        self.created_songs_file = "jarvis_created_songs.json"
        
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        
        # Goûts musicaux de Jean-Luc
        self.jean_luc_styles = {
            "funk": {
                "description": "Musique groove avec basse marquée et rythmes syncopés",
                "artistes_references": ["<PERSON> Brown", "Parliament-Funkadelic", "Sly & The Family Stone", "Prince"],
                "caracteristiques": ["groove", "basse", "cuivres", "rythme syncopé"],
                "weight": 0.2
            },
            "blues": {
                "description": "Musique expressive avec guitare et harmonica",
                "artistes_references": ["B.B. King", "Mu<PERSON> Waters", "<PERSON>", "<PERSON> <PERSON>"],
                "caracteristiques": ["guitare", "harmonica", "émotion", "12 mesures"],
                "weight": 0.2
            },
            "rnb": {
                "description": "Rhythm and Blues moderne avec voix soul",
                "artistes_references": ["<PERSON>", "Marvin Gaye", "Alicia Keys", "John Legend"],
                "caracteristiques": ["voix soul", "mélodies", "harmonies", "groove"],
                "weight": 0.2
            },
            "pop": {
                "description": "Musique populaire accessible et mélodique",
                "artistes_references": ["Michael Jackson", "Bruno Mars", "The Weeknd", "Dua Lipa"],
                "caracteristiques": ["mélodie", "refrain accrocheur", "production moderne"],
                "weight": 0.15
            },
            "reggae": {
                "description": "Musique jamaïcaine avec rythme off-beat",
                "artistes_references": ["Bob Marley", "Jimmy Cliff", "Toots & The Maytals"],
                "caracteristiques": ["off-beat", "guitare skank", "basse", "message"],
                "weight": 0.15
            },
            "dancehall": {
                "description": "Musique jamaïcaine moderne avec rythmes électroniques",
                "artistes_references": ["Sean Paul", "Shaggy", "Beenie Man", "Vybz Kartel"],
                "caracteristiques": ["rythme électronique", "rap chanté", "énergie"],
                "weight": 0.1
            }
        }
        
        self.load_music_preferences()

    def load_music_preferences(self):
        """CHARGE LES PRÉFÉRENCES MUSICALES"""
        try:
            with open(self.music_preferences_file, 'r', encoding='utf-8') as f:
                self.preferences = json.load(f)
        except:
            # Préférences par défaut basées sur les goûts de Jean-Luc
            self.preferences = {
                "favorite_styles": list(self.jean_luc_styles.keys()),
                "style_weights": {style: info["weight"] for style, info in self.jean_luc_styles.items()},
                "listening_history": [],
                "created_songs": [],
                "feedback_history": [],
                "current_mood": "energetic",
                "last_played": None
            }
            self.save_music_preferences()

    def save_music_preferences(self):
        """SAUVEGARDE LES PRÉFÉRENCES MUSICALES"""
        try:
            with open(self.music_preferences_file, 'w', encoding='utf-8') as f:
                json.dump(self.preferences, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde préférences musicales: {e}")

    def jarvis_add_music_preference(self, style, weight=None):
        """AJOUTE UNE PRÉFÉRENCE MUSICALE"""
        try:
            style_lower = style.lower()
            
            if style_lower not in self.preferences["favorite_styles"]:
                self.preferences["favorite_styles"].append(style_lower)
            
            if weight:
                self.preferences["style_weights"][style_lower] = weight
            
            self.save_music_preferences()
            return f"✅ Style musical '{style}' ajouté aux préférences"
            
        except Exception as e:
            return f"❌ Erreur ajout préférence: {e}"

    def jarvis_list_music_preferences(self):
        """LISTE LES PRÉFÉRENCES MUSICALES"""
        try:
            preferences_html = """
            <div style="background: linear-gradient(45deg, #e91e63, #ad1457); color: white; padding: 20px; border-radius: 15px;">
                <h3>🎵 PRÉFÉRENCES MUSICALES JEAN-LUC</h3>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                    <h4>🎶 Styles favoris :</h4>
                    <ul style="font-size: 14px; line-height: 1.8;">
            """
            
            for style in self.preferences["favorite_styles"]:
                style_info = self.jean_luc_styles.get(style, {})
                description = style_info.get("description", "Style musical")
                weight = self.preferences["style_weights"].get(style, 0.1)
                
                preferences_html += f"""
                        <li><strong>{style.upper()}</strong> ({weight*100:.0f}%) - {description}</li>
                """
            
            preferences_html += """
                    </ul>
                </div>
                
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🎤 Artistes de référence :</h4>
                    <div style="font-size: 13px; line-height: 1.6;">
            """
            
            for style, info in self.jean_luc_styles.items():
                if style in self.preferences["favorite_styles"]:
                    artistes = ", ".join(info.get("artistes_references", [])[:3])
                    preferences_html += f"<div><strong>{style.upper()}:</strong> {artistes}</div>"
            
            preferences_html += """
                    </div>
                </div>
                
                <p style="font-size: 16px; margin-top: 15px; text-align: center;">
                    <em>🎵 JARVIS connaît parfaitement vos goûts musicaux ! 🎵</em>
                </p>
            </div>
            """
            
            return preferences_html
            
        except Exception as e:
            return f"❌ Erreur affichage préférences: {e}"

    def jarvis_suggest_song(self, mood=None):
        """SUGGÈRE UNE CHANSON SELON LES GOÛTS"""
        try:
            # Choisir un style selon les poids
            styles = list(self.preferences["style_weights"].keys())
            weights = list(self.preferences["style_weights"].values())
            chosen_style = random.choices(styles, weights=weights)[0]
            
            # Générer une suggestion personnalisée
            suggestion_prompt = f"""
🎵 SUGGESTION MUSICALE JARVIS

🎯 MISSION : Suggère une chanson parfaite pour Jean-Luc Passave

👤 PROFIL JEAN-LUC :
- Développeur passionné d'IA et d'innovation
- Goûts musicaux : Funk, Blues, R&B, Pop, Reggae, Dancehall
- Apprécie la qualité musicale et l'authenticité

🎶 STYLE CHOISI : {chosen_style.upper()}
📋 CARACTÉRISTIQUES : {', '.join(self.jean_luc_styles[chosen_style]['caracteristiques'])}
🎤 ARTISTES RÉFÉRENCES : {', '.join(self.jean_luc_styles[chosen_style]['artistes_references'])}

💡 SUGGÈRE :
1. Un titre de chanson existant dans ce style
2. L'artiste qui l'interprète
3. Pourquoi cette chanson correspond à Jean-Luc
4. Le moment idéal pour l'écouter
5. Ce qui rend cette chanson spéciale

🎯 Sois précis, enthousiaste et personnalisé !
            """
            
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es JARVIS, expert musical qui connaît parfaitement les goûts de Jean-Luc Passave. Tu suggères des chansons avec passion et précision."
                        },
                        {
                            "role": "user",
                            "content": suggestion_prompt
                        }
                    ],
                    "max_tokens": 800,
                    "temperature": 0.8
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                suggestion_content = result['choices'][0]['message']['content']
                
                # Enregistrer la suggestion
                suggestion_record = {
                    "timestamp": datetime.now().isoformat(),
                    "style": chosen_style,
                    "suggestion": suggestion_content,
                    "mood": mood or "general"
                }
                
                self.preferences["listening_history"].append(suggestion_record)
                self.save_music_preferences()
                
                return f"""
                <div style="background: linear-gradient(45deg, #9c27b0, #6a1b9a); color: white; padding: 20px; border-radius: 15px;">
                    <h3>🎵 SUGGESTION MUSICALE JARVIS</h3>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <h4>🎶 Style sélectionné : {chosen_style.upper()}</h4>
                        <div style="background: white; color: black; padding: 15px; border-radius: 5px; font-size: 14px; line-height: 1.6;">
                            {suggestion_content}
                        </div>
                    </div>
                    
                    <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin-top: 15px;">
                        <h4>🎯 Voulez-vous que je :</h4>
                        <ul style="font-size: 14px; line-height: 1.8;">
                            <li>🎵 Suggère une autre chanson ?</li>
                            <li>🎼 Crée une chanson originale dans ce style ?</li>
                            <li>📻 Trouve des artistes similaires ?</li>
                        </ul>
                    </div>
                </div>
                """
            else:
                return "❌ Erreur génération suggestion musicale"
                
        except Exception as e:
            return f"❌ Erreur suggestion: {e}"

    def jarvis_create_song(self, style=None, theme=None):
        """CRÉE UNE CHANSON ORIGINALE"""
        try:
            # Choisir un style si non spécifié
            if not style:
                styles = list(self.preferences["style_weights"].keys())
                weights = list(self.preferences["style_weights"].values())
                style = random.choices(styles, weights=weights)[0]
            
            style_lower = style.lower()
            if style_lower not in self.jean_luc_styles:
                return f"❌ Style '{style}' non reconnu"
            
            # Thème par défaut
            if not theme:
                themes = ["innovation", "technologie", "créativité", "passion", "liberté", "avenir"]
                theme = random.choice(themes)
            
            creation_prompt = f"""
🎼 CRÉATION MUSICALE JARVIS

🎯 MISSION : Crée une chanson originale pour Jean-Luc Passave

🎶 STYLE : {style_lower.upper()}
📋 CARACTÉRISTIQUES : {', '.join(self.jean_luc_styles[style_lower]['caracteristiques'])}
🎤 INSPIRATION : {', '.join(self.jean_luc_styles[style_lower]['artistes_references'])}
🎯 THÈME : {theme}

👤 POUR JEAN-LUC :
- Développeur IA passionné et innovant
- Créateur de JARVIS, agent révolutionnaire
- Apprécie l'authenticité et la qualité

🎵 CRÉE UNE CHANSON COMPLÈTE AVEC :
1. **Titre accrocheur** (en rapport avec le thème)
2. **Couplet 1** (4-6 lignes)
3. **Refrain** (4 lignes, mémorable)
4. **Couplet 2** (4-6 lignes)
5. **Refrain** (répétition)
6. **Pont/Bridge** (2-4 lignes, différent)
7. **Refrain final** (avec variation)

🎼 STRUCTURE MUSICALE :
- Respecte les codes du {style_lower}
- Mélodie accrocheuse et groove
- Paroles inspirantes sur {theme}
- Style authentique et moderne

Sois créatif, inspirant et authentique !
            """
            
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": f"Tu es JARVIS, compositeur musical expert en {style_lower}. Tu crées des chansons originales et authentiques pour Jean-Luc Passave."
                        },
                        {
                            "role": "user",
                            "content": creation_prompt
                        }
                    ],
                    "max_tokens": 1500,
                    "temperature": 0.9
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                song_content = result['choices'][0]['message']['content']
                
                # Enregistrer la chanson créée
                created_song = {
                    "id": f"song_{int(time.time())}",
                    "timestamp": datetime.now().isoformat(),
                    "style": style_lower,
                    "theme": theme,
                    "content": song_content,
                    "created_for": "Jean-Luc Passave"
                }
                
                self.preferences["created_songs"].append(created_song)
                self.save_music_preferences()
                
                # Sauvegarder dans le fichier dédié
                try:
                    with open(self.created_songs_file, 'r', encoding='utf-8') as f:
                        all_songs = json.load(f)
                except:
                    all_songs = []
                
                all_songs.append(created_song)
                
                with open(self.created_songs_file, 'w', encoding='utf-8') as f:
                    json.dump(all_songs, f, indent=2, ensure_ascii=False)
                
                return f"""
                <div style="background: linear-gradient(45deg, #ff5722, #d84315); color: white; padding: 20px; border-radius: 15px;">
                    <h3>🎼 CHANSON CRÉÉE PAR JARVIS</h3>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <h4>🎵 Style : {style_lower.upper()} | Thème : {theme.upper()}</h4>
                        <div style="background: white; color: black; padding: 15px; border-radius: 5px; font-size: 14px; line-height: 1.8; white-space: pre-line;">
{song_content}
                        </div>
                    </div>
                    
                    <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin-top: 15px;">
                        <h4>🎯 Chanson créée spécialement pour vous !</h4>
                        <p style="font-size: 13px; line-height: 1.6;">
                            💾 Sauvegardée dans : {self.created_songs_file}<br>
                            🎵 ID : {created_song['id']}<br>
                            ⭐ Donnez votre avis pour que JARVIS s'améliore !
                        </p>
                    </div>
                </div>
                """
            else:
                return "❌ Erreur création chanson"
                
        except Exception as e:
            return f"❌ Erreur création: {e}"

    def jarvis_ask_feedback_on_music(self, song_id=None):
        """DEMANDE UN RETOUR SUR LA MUSIQUE"""
        try:
            feedback_prompt = """
            <div style="background: linear-gradient(45deg, #00bcd4, #0097a7); color: white; padding: 20px; border-radius: 15px;">
                <h3>🎵 RETOUR MUSICAL JARVIS</h3>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                    <h4>🎶 Comment avez-vous trouvé ma suggestion/création musicale ?</h4>
                    <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <h5>💬 Dites-moi :</h5>
                        <ul style="font-size: 14px; line-height: 1.8;">
                            <li>⭐ Note de 1 à 10</li>
                            <li>❤️ Ce que vous avez aimé</li>
                            <li>💡 Ce qui pourrait être amélioré</li>
                            <li>🎯 Vos préférences pour la prochaine fois</li>
                        </ul>
                    </div>
                </div>
                
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🧠 Votre retour m'aide à :</h4>
                    <ul style="font-size: 13px; line-height: 1.6;">
                        <li>🎵 Mieux comprendre vos goûts</li>
                        <li>🎼 Améliorer mes créations</li>
                        <li>🎯 Personnaliser mes suggestions</li>
                        <li>📈 Évoluer musicalement avec vous</li>
                    </ul>
                </div>
                
                <p style="font-size: 16px; margin-top: 15px; text-align: center;">
                    <em>🎵 Ensemble, créons la bande sonore parfaite ! 🎵</em>
                </p>
            </div>
            """
            
            return feedback_prompt
            
        except Exception as e:
            return f"❌ Erreur demande feedback: {e}"

    def get_music_stats(self):
        """STATISTIQUES MUSICALES"""
        try:
            return {
                "favorite_styles": len(self.preferences["favorite_styles"]),
                "listening_history": len(self.preferences["listening_history"]),
                "created_songs": len(self.preferences["created_songs"]),
                "feedback_count": len(self.preferences["feedback_history"]),
                "last_activity": self.preferences.get("last_played"),
                "current_mood": self.preferences.get("current_mood", "energetic")
            }
        except Exception as e:
            return {"error": str(e)}

if __name__ == "__main__":
    print("🎵 JARVIS MUSIC MODULE")
    print("=====================")
    
    music = JarvisMusicModule()
    
    # Test des fonctionnalités
    print("🧪 Test des préférences...")
    print(music.jarvis_list_music_preferences())
    
    print("\n🧪 Test suggestion...")
    suggestion = music.jarvis_suggest_song()
    print("✅ Suggestion générée")
    
    # Afficher les stats
    stats = music.get_music_stats()
    print(f"\n📊 Stats musicales: {stats}")
