#!/usr/bin/env python3
"""
🧠 SYSTÈME NEURONAL PAR ÉTAGES JARVIS
Génération automatique de neurones organisés par étages
Activation/veille intelligente pour optimiser la mémoire
"""

import os
import json
import time
import threading
import random
from datetime import datetime
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor

class NeuroneJarvis:
    """Neurone individuel JARVIS avec états activation/veille"""
    
    def __init__(self, neurone_id: str, etage: int, type_neurone: str):
        self.id = neurone_id
        self.etage = etage
        self.type = type_neurone
        self.etat = "veille"  # "actif", "veille", "stockage"
        self.donnees_stockees = {}
        self.connexions = []
        self.activations_count = 0
        self.derniere_activation = 0
        self.seuil_activation = 0.5
        self.energie = 1.0
        
    def activer(self, stimulus: float = 1.0):
        """Active le neurone si le stimulus dépasse le seuil"""
        if stimulus >= self.seuil_activation and self.energie > 0.1:
            self.etat = "actif"
            self.activations_count += 1
            self.derniere_activation = time.time()
            self.energie -= 0.1  # Consommation énergétique
            return True
        return False
    
    def mettre_en_veille(self):
        """Met le neurone en veille pour économiser la mémoire"""
        if self.etat == "actif" and (time.time() - self.derniere_activation) > 30:
            self.etat = "veille"
            self.energie = min(1.0, self.energie + 0.05)  # Récupération
    
    def stocker_donnees(self, cle: str, valeur: Any):
        """Stocke des données même en veille"""
        self.donnees_stockees[cle] = {
            'valeur': valeur,
            'timestamp': time.time(),
            'acces_count': 0
        }
    
    def recuperer_donnees(self, cle: str) -> Optional[Any]:
        """Récupère des données et active le neurone si nécessaire"""
        if cle in self.donnees_stockees:
            self.donnees_stockees[cle]['acces_count'] += 1
            if self.etat == "veille":
                self.activer(0.8)  # Activation pour accès données
            return self.donnees_stockees[cle]['valeur']
        return None

class EtageNeuronal:
    """Étage neuronal avec gestion collective des neurones"""
    
    def __init__(self, etage_id: int, nb_neurones: int, specialisation: str):
        self.id = etage_id
        self.specialisation = specialisation
        self.neurones = {}
        self.neurones_actifs = set()
        self.neurones_veille = set()
        self.charge_memoire = 0.0
        
        # Générer neurones automatiquement
        self.generer_neurones(nb_neurones)
    
    def generer_neurones(self, nb_neurones: int):
        """Génère automatiquement les neurones de l'étage"""
        for i in range(nb_neurones):
            neurone_id = f"N{self.id}_{i:04d}"
            type_neurone = self.determiner_type_neurone(i, nb_neurones)
            
            neurone = NeuroneJarvis(neurone_id, self.id, type_neurone)
            self.neurones[neurone_id] = neurone
            self.neurones_veille.add(neurone_id)
    
    def determiner_type_neurone(self, index: int, total: int) -> str:
        """Détermine le type de neurone selon sa position"""
        ratio = index / total
        
        if ratio < 0.2:
            return "recepteur"  # Neurones d'entrée
        elif ratio < 0.4:
            return "traitement"  # Neurones de traitement
        elif ratio < 0.6:
            return "memoire"  # Neurones de mémoire
        elif ratio < 0.8:
            return "decision"  # Neurones de décision
        else:
            return "sortie"  # Neurones de sortie
    
    def activer_neurones_necessaires(self, stimulus_global: float, nb_max: int = None):
        """Active seulement les neurones nécessaires"""
        if nb_max is None:
            nb_max = len(self.neurones) // 4  # Max 25% actifs
        
        # Sélectionner neurones à activer
        neurones_a_activer = []
        
        # Priorité aux neurones spécialisés selon le stimulus
        for neurone_id in self.neurones_veille:
            neurone = self.neurones[neurone_id]
            
            # Probabilité d'activation selon type et stimulus
            prob_activation = self.calculer_probabilite_activation(neurone, stimulus_global)
            
            if random.random() < prob_activation:
                neurones_a_activer.append(neurone_id)
                
                if len(neurones_a_activer) >= nb_max:
                    break
        
        # Activer les neurones sélectionnés
        for neurone_id in neurones_a_activer:
            neurone = self.neurones[neurone_id]
            if neurone.activer(stimulus_global):
                self.neurones_actifs.add(neurone_id)
                self.neurones_veille.discard(neurone_id)
        
        self.mettre_a_jour_charge_memoire()
    
    def calculer_probabilite_activation(self, neurone: NeuroneJarvis, stimulus: float) -> float:
        """Calcule la probabilité d'activation d'un neurone"""
        base_prob = stimulus * 0.3
        
        # Bonus selon type de neurone
        type_bonus = {
            "recepteur": 0.4,
            "traitement": 0.3,
            "memoire": 0.2,
            "decision": 0.3,
            "sortie": 0.2
        }
        
        # Bonus selon historique
        if neurone.activations_count > 10:
            base_prob += 0.1  # Neurone expérimenté
        
        # Malus si récemment actif
        if (time.time() - neurone.derniere_activation) < 10:
            base_prob -= 0.2
        
        return min(1.0, base_prob + type_bonus.get(neurone.type, 0.1))
    
    def mettre_neurones_en_veille(self):
        """Met en veille les neurones inactifs"""
        neurones_a_endormir = []
        
        for neurone_id in self.neurones_actifs:
            neurone = self.neurones[neurone_id]
            
            # Critères de mise en veille
            if (time.time() - neurone.derniere_activation) > 30 or neurone.energie < 0.2:
                neurone.mettre_en_veille()
                neurones_a_endormir.append(neurone_id)
        
        # Transférer vers veille
        for neurone_id in neurones_a_endormir:
            self.neurones_actifs.discard(neurone_id)
            self.neurones_veille.add(neurone_id)
        
        self.mettre_a_jour_charge_memoire()
    
    def mettre_a_jour_charge_memoire(self):
        """Met à jour la charge mémoire de l'étage"""
        nb_actifs = len(self.neurones_actifs)
        nb_total = len(self.neurones)
        self.charge_memoire = nb_actifs / nb_total if nb_total > 0 else 0

class SystemeNeuronalJarvis:
    """Système neuronal complet JARVIS avec gestion par étages"""
    
    def __init__(self):
        self.etages = {}
        self.generation_active = True
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.lock = threading.Lock()
        
        # Configuration étages
        self.config_etages = {
            0: {"nb_neurones": 2048, "specialisation": "perception"},
            1: {"nb_neurones": 1024, "specialisation": "traitement_primaire"},
            2: {"nb_neurones": 512, "specialisation": "memoire_court_terme"},
            3: {"nb_neurones": 256, "specialisation": "analyse_semantique"},
            4: {"nb_neurones": 128, "specialisation": "decision_cognitive"},
            5: {"nb_neurones": 64, "specialisation": "generation_reponse"},
            6: {"nb_neurones": 32, "specialisation": "controle_execution"}
        }
        
        print("🧠 Système neuronal JARVIS initialisé")
        self.generer_tous_etages()
        self.demarrer_gestion_automatique()
    
    def generer_tous_etages(self):
        """Génère automatiquement tous les étages neuronaux"""
        try:
            with self.lock:
                for etage_id, config in self.config_etages.items():
                    etage = EtageNeuronal(
                        etage_id, 
                        config["nb_neurones"], 
                        config["specialisation"]
                    )
                    self.etages[etage_id] = etage
                    
                    print(f"✅ Étage {etage_id} généré: {config['nb_neurones']} neurones ({config['specialisation']})")
            
            total_neurones = sum(len(etage.neurones) for etage in self.etages.values())
            print(f"🧠 Total: {total_neurones} neurones générés sur {len(self.etages)} étages")
            
        except Exception as e:
            print(f"❌ Erreur génération étages: {e}")
    
    def traiter_stimulus(self, stimulus_data: Dict, intensite: float = 1.0):
        """Traite un stimulus en activant les étages nécessaires"""
        try:
            # Propager le stimulus à travers les étages
            for etage_id in sorted(self.etages.keys()):
                etage = self.etages[etage_id]
                
                # Adapter l'intensité selon l'étage
                intensite_etage = intensite * (0.9 ** etage_id)  # Diminution progressive
                
                # Activer neurones nécessaires
                etage.activer_neurones_necessaires(intensite_etage)
                
                # Stocker données si nécessaire
                if stimulus_data and etage.specialisation in ["memoire_court_terme", "analyse_semantique"]:
                    self.stocker_dans_etage(etage_id, stimulus_data)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur traitement stimulus: {e}")
            return False
    
    def stocker_dans_etage(self, etage_id: int, donnees: Dict):
        """Stocke des données dans un étage spécifique"""
        try:
            etage = self.etages.get(etage_id)
            if not etage:
                return False
            
            # Sélectionner neurones de type mémoire
            neurones_memoire = [
                n for n in etage.neurones.values() 
                if n.type in ["memoire", "traitement"]
            ]
            
            if neurones_memoire:
                # Distribuer les données
                for i, (cle, valeur) in enumerate(donnees.items()):
                    neurone = neurones_memoire[i % len(neurones_memoire)]
                    neurone.stocker_donnees(cle, valeur)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur stockage étage {etage_id}: {e}")
            return False
    
    def rechercher_dans_neurones(self, requete: str) -> List[Dict]:
        """Recherche dans tous les neurones"""
        try:
            resultats = []
            
            for etage_id, etage in self.etages.items():
                for neurone in etage.neurones.values():
                    for cle, data in neurone.donnees_stockees.items():
                        if requete.lower() in str(data['valeur']).lower():
                            resultats.append({
                                'etage': etage_id,
                                'neurone': neurone.id,
                                'cle': cle,
                                'valeur': data['valeur'],
                                'timestamp': data['timestamp']
                            })
            
            return resultats
            
        except Exception as e:
            print(f"❌ Erreur recherche neurones: {e}")
            return []
    
    def demarrer_gestion_automatique(self):
        """Démarre la gestion automatique activation/veille"""
        try:
            def gestion_worker():
                while self.generation_active:
                    try:
                        # Cycle de gestion toutes les 15 secondes
                        time.sleep(15)
                        
                        # Mettre en veille les neurones inactifs
                        for etage in self.etages.values():
                            etage.mettre_neurones_en_veille()
                        
                        # Générer nouveaux neurones si nécessaire
                        self.generer_neurones_adaptatifs()
                        
                    except Exception as e:
                        print(f"⚠️ Erreur gestion automatique: {e}")
                        time.sleep(30)
            
            self.thread_pool.submit(gestion_worker)
            print("✅ Gestion automatique neurones démarrée")
            
        except Exception as e:
            print(f"❌ Erreur démarrage gestion: {e}")
    
    def generer_neurones_adaptatifs(self):
        """Génère de nouveaux neurones selon les besoins"""
        try:
            for etage_id, etage in self.etages.items():
                # Si charge mémoire > 80%, générer plus de neurones
                if etage.charge_memoire > 0.8:
                    nb_nouveaux = min(64, len(etage.neurones) // 10)
                    
                    for i in range(nb_nouveaux):
                        neurone_id = f"N{etage_id}_{len(etage.neurones):04d}"
                        type_neurone = etage.determiner_type_neurone(
                            len(etage.neurones), 
                            len(etage.neurones) + nb_nouveaux
                        )
                        
                        neurone = NeuroneJarvis(neurone_id, etage_id, type_neurone)
                        etage.neurones[neurone_id] = neurone
                        etage.neurones_veille.add(neurone_id)
                    
                    print(f"🧠 Étage {etage_id}: {nb_nouveaux} neurones générés (charge: {etage.charge_memoire:.1%})")
            
        except Exception as e:
            print(f"❌ Erreur génération adaptative: {e}")
    
    def get_stats_systeme_neuronal(self) -> Dict:
        """Retourne les statistiques du système neuronal"""
        try:
            stats = {
                'nb_etages': len(self.etages),
                'total_neurones': 0,
                'neurones_actifs': 0,
                'neurones_veille': 0,
                'charge_memoire_moyenne': 0.0,
                'etages_details': {}
            }
            
            for etage_id, etage in self.etages.items():
                nb_neurones = len(etage.neurones)
                nb_actifs = len(etage.neurones_actifs)
                nb_veille = len(etage.neurones_veille)
                
                stats['total_neurones'] += nb_neurones
                stats['neurones_actifs'] += nb_actifs
                stats['neurones_veille'] += nb_veille
                stats['charge_memoire_moyenne'] += etage.charge_memoire
                
                stats['etages_details'][etage_id] = {
                    'specialisation': etage.specialisation,
                    'nb_neurones': nb_neurones,
                    'nb_actifs': nb_actifs,
                    'nb_veille': nb_veille,
                    'charge_memoire': etage.charge_memoire
                }
            
            stats['charge_memoire_moyenne'] /= len(self.etages) if self.etages else 1
            
            return stats
            
        except Exception as e:
            print(f"❌ Erreur stats système: {e}")
            return {}

# Instance globale
systeme_neuronal = SystemeNeuronalJarvis()

def activer_systeme_neuronal_jarvis():
    """Active et affiche le système neuronal complet JARVIS"""
    try:
        # Traiter un stimulus de test
        stimulus_test = {
            "type": "conversation",
            "contenu": "Test activation système neuronal",
            "timestamp": datetime.now().isoformat()
        }
        
        systeme_neuronal.traiter_stimulus(stimulus_test, intensite=0.8)
        
        # Statistiques
        stats = systeme_neuronal.get_stats_systeme_neuronal()
        
        return f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 20px;">
            <h2>🧠 SYSTÈME NEURONAL JARVIS ACTIVÉ</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <h3>📊 STATISTIQUES GLOBALES</h3>
                    <p>🧠 Total neurones: {stats.get('total_neurones', 0):,}</p>
                    <p>⚡ Neurones actifs: {stats.get('neurones_actifs', 0):,}</p>
                    <p>😴 Neurones en veille: {stats.get('neurones_veille', 0):,}</p>
                    <p>📈 Charge mémoire: {stats.get('charge_memoire_moyenne', 0):.1%}</p>
                </div>
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <h3>🏗️ ARCHITECTURE ÉTAGES</h3>
                    <p>📚 Étages neuronaux: {stats.get('nb_etages', 0)}</p>
                    <p>🎯 Spécialisations: Perception → Décision</p>
                    <p>🔄 Génération automatique: Active</p>
                    <p>⚡ Activation intelligente: Active</p>
                </div>
                
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 20px 0;">
                <h3>🎯 ÉTAGES SPÉCIALISÉS</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
        """
        
        # Ajouter détails des étages
        etages_html = ""
        for etage_id, details in stats.get('etages_details', {}).items():
            etages_html += f"""
                    <div style="background: rgba(255,255,255,0.05); padding: 10px; border-radius: 5px;">
                        <h4>Étage {etage_id}</h4>
                        <p>{details['specialisation']}</p>
                        <p>🧠 {details['nb_neurones']} neurones</p>
                        <p>⚡ {details['nb_actifs']} actifs</p>
                        <p>📊 {details['charge_memoire']:.1%} charge</p>
                    </div>
            """
        
        return f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 20px;">
            <h2>🧠 SYSTÈME NEURONAL JARVIS ACTIVÉ</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <h3>📊 STATISTIQUES GLOBALES</h3>
                    <p>🧠 Total neurones: {stats.get('total_neurones', 0):,}</p>
                    <p>⚡ Neurones actifs: {stats.get('neurones_actifs', 0):,}</p>
                    <p>😴 Neurones en veille: {stats.get('neurones_veille', 0):,}</p>
                    <p>📈 Charge mémoire: {stats.get('charge_memoire_moyenne', 0):.1%}</p>
                </div>
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <h3>🏗️ ARCHITECTURE ÉTAGES</h3>
                    <p>📚 Étages neuronaux: {stats.get('nb_etages', 0)}</p>
                    <p>🎯 Spécialisations: Perception → Décision</p>
                    <p>🔄 Génération automatique: Active</p>
                    <p>⚡ Activation intelligente: Active</p>
                </div>
                
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 20px 0;">
                <h3>🎯 ÉTAGES SPÉCIALISÉS</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    {etages_html}
                </div>
            </div>
            
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px; margin-top: 20px; text-align: center;">
                <h3>🎉 SYSTÈME NEURONAL COMPLET OPÉRATIONNEL !</h3>
                <p>Génération automatique • Activation intelligente • Stockage en veille</p>
                <p><strong>Architecture optimisée pour performance maximale</strong></p>
            </div>
        </div>
        """
        
    except Exception as e:
        return f"""
        <div style="background: linear-gradient(45deg, #f44336, #d32f2f); color: white; padding: 15px; border-radius: 10px;">
            <h4>❌ ERREUR SYSTÈME NEURONAL</h4>
            <p>Impossible d'activer le système neuronal: {str(e)}</p>
        </div>
        """
