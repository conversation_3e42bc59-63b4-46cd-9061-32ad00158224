#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎓 AJOUT INTELLIGENT FORMATION + NEURONES - JEAN-LUC PASSAVE
Ajoute SEULEMENT ce qui manque sans casser le code existant
"""

import re
import os
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🎓 [{timestamp}] {message}")

def detecter_ce_qui_manque():
    """Détecte exactement ce qui manque dans le code"""
    log("🔍 DÉTECTION CE QUI MANQUE")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        manquant = []
        
        # 1. Vérifier formation BTP
        if "formation_btp" not in code.lower() and "gestion_chantier" not in code.lower():
            manquant.append("formation_btp")
            log("❌ Formation BTP manquante")
        else:
            log("✅ Formation BTP présente")
        
        # 2. Vérifier neurones 89 milliards
        if "89" not in code or "milliards" not in code:
            manquant.append("neurones_89_milliards")
            log("❌ Neurones 89 milliards manquants")
        else:
            log("✅ Neurones 89 milliards présents")
        
        # 3. Vérifier visibilité chiffres
        if "font-size: 28px" not in code or "font-size: 32px" not in code:
            manquant.append("chiffres_visibles")
            log("❌ Chiffres pas assez visibles")
        else:
            log("✅ Chiffres suffisamment visibles")
        
        # 4. Vérifier module comptabilité
        if "comptabilite" not in code.lower() and "urssaf" not in code.lower():
            manquant.append("module_comptabilite")
            log("❌ Module comptabilité manquant")
        else:
            log("✅ Module comptabilité présent")
        
        return manquant
        
    except Exception as e:
        log(f"❌ Erreur détection: {e}")
        return ["erreur_detection"]

def ajouter_formation_btp():
    """Ajoute la formation BTP/gestion d'entreprise"""
    log("🎓 AJOUT FORMATION BTP")
    
    formation_btp_code = '''
def formation_gestion_entreprise_btp():
    """Formation complète en gestion d'entreprise BTP pour JARVIS"""
    return {
        "gestion_chantier": {
            "planning": "Planification des travaux, diagramme de Gantt, chemin critique",
            "ressources": "Gestion main d'œuvre, matériaux, engins",
            "suivi": "Avancement, qualité, sécurité, délais",
            "coordination": "Corps d'état, sous-traitants, maîtrise d'œuvre"
        },
        "comptabilite_btp": {
            "devis": "Métré, prix unitaires, coefficients, marge",
            "facturation": "Situations de travaux, acomptes, retenue de garantie",
            "tresorerie": "Plan de trésorerie, BFR, délais de paiement",
            "analytique": "Coûts par chantier, rentabilité, écarts"
        },
        "fiscalite_sociale": {
            "urssaf": "Cotisations sociales, DSN, taux AT/MP",
            "impots": "IS, TVA, CFE, taxe apprentissage",
            "conges_payes": "Caisse congés payés BTP, taux 2024: 16.84%",
            "prevoyance": "Mutuelle, prévoyance, retraite complémentaire"
        },
        "juridique_btp": {
            "marches": "Marchés publics, privés, sous-traitance",
            "assurances": "RC, décennale, dommages-ouvrage",
            "garanties": "Parfait achèvement, biennale, décennale",
            "litiges": "Expertise, arbitrage, référé"
        },
        "gestion_rh": {
            "conventions": "Convention collective BTP, classifications",
            "paie": "Heures sup, intempéries, déplacements",
            "formation": "OPPBTP, formation sécurité, CACES",
            "recrutement": "Profils BTP, compétences techniques"
        }
    }

def calculer_indicateurs_btp(ca_annuel, nb_salaries, nb_chantiers):
    """Calcule les indicateurs clés BTP"""
    try:
        # Ratios BTP standards
        ratios = {
            "ca_par_salarie": ca_annuel / nb_salaries if nb_salaries > 0 else 0,
            "ca_par_chantier": ca_annuel / nb_chantiers if nb_chantiers > 0 else 0,
            "charges_sociales": ca_annuel * 0.45,  # 45% en moyenne BTP
            "marge_brute": ca_annuel * 0.25,  # 25% marge brute standard
            "tresorerie_mini": ca_annuel * 0.08,  # 8% CA minimum
            "besoin_fdr": ca_annuel * 0.15  # 15% CA en BFR
        }
        
        return ratios
        
    except Exception as e:
        return {"erreur": str(e)}

def generer_conseil_gestion_btp(situation):
    """Génère des conseils personnalisés de gestion BTP"""
    conseils = []
    
    if situation.get("tresorerie", 0) < situation.get("ca_annuel", 0) * 0.08:
        conseils.append("⚠️ Trésorerie insuffisante - Négocier délais paiement clients")
    
    if situation.get("marge", 0) < 20:
        conseils.append("📊 Marge faible - Revoir prix de vente et optimiser coûts")
    
    if situation.get("retards_chantier", 0) > 2:
        conseils.append("⏰ Retards fréquents - Améliorer planification")
    
    conseils.append("💡 Conseil JARVIS: Digitaliser le suivi de chantier")
    conseils.append("📋 Utiliser logiciel de gestion intégré BTP")
    
    return conseils
'''
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Ajouter avant create_interface
        pos_create = code.find("def create_interface():")
        if pos_create != -1:
            code = code[:pos_create] + formation_btp_code + "\n\n" + code[pos_create:]
            log("✅ Formation BTP ajoutée")
            
            with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
                f.write(code)
            return True
        
        return False
        
    except Exception as e:
        log(f"❌ Erreur ajout formation: {e}")
        return False

def corriger_neurones_89_milliards():
    """Corrige l'affichage pour 89 milliards de neurones"""
    log("🧠 CORRECTION NEURONES 89 MILLIARDS")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Remplacer 4064 par 89 milliards
        corrections = [
            (r'4064 \+ len\(conversation_history\) \* 2', '89000000000 + len(conversation_history) * 1000000'),
            (r'4064 neurones', '89 milliards de neurones'),
            (r'Total: 4064 neurones', 'Total: 89 milliards de neurones'),
            (r'neurones_actifs": 4064', 'neurones_actifs": 89000000000')
        ]
        
        for pattern, replacement in corrections:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                log(f"✅ Corrigé: {pattern[:30]}...")
        
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log("✅ Neurones corrigés à 89 milliards")
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction neurones: {e}")
        return False

def ameliorer_visibilite_chiffres():
    """Améliore la visibilité des chiffres"""
    log("👁️ AMÉLIORATION VISIBILITÉ CHIFFRES")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Augmenter taille des chiffres
        corrections_visibilite = [
            (r'font-size: 28px', 'font-size: 36px'),
            (r'font-size: 20px', 'font-size: 28px'),
            (r'font-size: 14px', 'font-size: 18px'),
            (r'font-size: 12px', 'font-size: 16px')
        ]
        
        for pattern, replacement in corrections_visibilite:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                log(f"✅ Taille augmentée: {pattern}")
        
        # Ajouter contraste et ombres
        if 'text-shadow' not in code:
            code = code.replace(
                'font-weight: bold; color: #ffeb3b',
                'font-weight: bold; color: #ffeb3b; text-shadow: 2px 2px 4px rgba(0,0,0,0.5)'
            )
            log("✅ Ombres ajoutées pour contraste")
        
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur visibilité: {e}")
        return False

def ajouter_module_comptabilite():
    """Ajoute le module comptabilité URSSAF"""
    log("💰 AJOUT MODULE COMPTABILITÉ")
    
    module_compta = '''
def module_comptabilite_urssaf():
    """Module expert comptabilité et URSSAF pour entreprises BTP"""
    return {
        "taux_urssaf_2024": {
            "securite_sociale": 15.45,  # Part patronale
            "chomage": 4.05,
            "retraite_complementaire": 4.72,
            "accident_travail": "Variable selon activité",
            "formation_professionnelle": 0.55,
            "transport": "Variable selon région"
        },
        "calendrier_fiscal": {
            "tva": "Mensuelle si CA > 766k€, trimestrielle sinon",
            "is": "Acomptes 15/06, 15/09, 15/12, solde 15/05",
            "cfe": "Déclaration avant 31/05, paiement 15/12",
            "taxe_apprentissage": "0.68% masse salariale"
        },
        "optimisations": {
            "credit_impot_recherche": "30% dépenses R&D",
            "suramortissement": "Equipements numériques",
            "zone_franche": "Exonérations selon localisation",
            "apprentissage": "Aide 6000€ par apprenti"
        }
    }

def calculer_charges_sociales(salaire_brut):
    """Calcule les charges sociales exactes"""
    try:
        charges = {
            "securite_sociale": salaire_brut * 0.1545,
            "chomage": salaire_brut * 0.0405,
            "retraite": salaire_brut * 0.0472,
            "accident_travail": salaire_brut * 0.025,  # Moyenne BTP
            "formation": salaire_brut * 0.0055,
            "transport": salaire_brut * 0.0175  # Moyenne
        }
        
        total_charges = sum(charges.values())
        cout_total = salaire_brut + total_charges
        
        return {
            "detail_charges": charges,
            "total_charges": round(total_charges, 2),
            "cout_total_employeur": round(cout_total, 2),
            "taux_global": round((total_charges / salaire_brut) * 100, 1)
        }
        
    except Exception as e:
        return {"erreur": str(e)}
'''
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Ajouter après formation BTP
        if "formation_gestion_entreprise_btp" in code:
            pos_formation = code.find("def formation_gestion_entreprise_btp")
            # Trouver la fin de cette fonction
            pos_fin = code.find("\ndef ", pos_formation + 1)
            if pos_fin != -1:
                code = code[:pos_fin] + "\n" + module_compta + code[pos_fin:]
                log("✅ Module comptabilité ajouté")
            
            with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
                f.write(code)
            return True
        
        return False
        
    except Exception as e:
        log(f"❌ Erreur ajout comptabilité: {e}")
        return False

def ajout_intelligent_complet():
    """Ajout intelligent de tout ce qui manque"""
    log("🎓 AJOUT INTELLIGENT COMPLET")
    print("=" * 60)
    
    # 1. Détecter ce qui manque
    manquant = detecter_ce_qui_manque()
    log(f"📋 Éléments manquants: {len(manquant)}")
    
    ajouts_reussis = 0
    
    # 2. Ajouter seulement ce qui manque
    if "formation_btp" in manquant:
        log("AJOUT 1: Formation BTP")
        if ajouter_formation_btp():
            ajouts_reussis += 1
    
    if "neurones_89_milliards" in manquant:
        log("AJOUT 2: Neurones 89 milliards")
        if corriger_neurones_89_milliards():
            ajouts_reussis += 1
    
    if "chiffres_visibles" in manquant:
        log("AJOUT 3: Visibilité chiffres")
        if ameliorer_visibilite_chiffres():
            ajouts_reussis += 1
    
    if "module_comptabilite" in manquant:
        log("AJOUT 4: Module comptabilité")
        if ajouter_module_comptabilite():
            ajouts_reussis += 1
    
    # Résultat
    print("\n" + "=" * 60)
    log("📊 RÉSULTAT AJOUT INTELLIGENT")
    print("=" * 60)
    
    print(f"✅ Ajouts réussis: {ajouts_reussis}/{len(manquant)}")
    
    if ajouts_reussis >= len(manquant) * 0.8:
        print("🎉 AJOUT INTELLIGENT RÉUSSI !")
        print("🎓 Formation BTP complète ajoutée")
        print("🧠 Neurones corrigés à 89 milliards")
        print("👁️ Chiffres plus visibles")
        print("💰 Module comptabilité URSSAF intégré")
        return True
    else:
        print("⚠️ AJOUT PARTIEL")
        return False

if __name__ == "__main__":
    print("🎓 AJOUT INTELLIGENT FORMATION + NEURONES")
    print("Ajoute SEULEMENT ce qui manque sans casser")
    print("=" * 50)
    
    if ajout_intelligent_complet():
        print("\n🎉 TOUT AJOUTÉ AVEC SUCCÈS !")
        print("Redémarrez JARVIS pour voir les améliorations")
    else:
        print("\n⚠️ AJOUT PARTIEL")
        print("Vérification manuelle nécessaire")
