#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TESTEUR COMPLET FONCTIONNALITÉS JARVIS - JEAN-LUC PASSAVE
Teste toutes les capacités : Internet, Code, Visual Studio, Boutons
"""

import requests
import json
import time
import subprocess
import os
import webbrowser
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🧪 [{timestamp}] {message}")

class TesteurFonctionnalitesJarvis:
    def __init__(self):
        self.base_url = "http://localhost:8000/v1/chat/completions"
        self.interface_url = "http://localhost:7866"
        self.resultats = {}
        self.tests_reussis = 0
        self.tests_totaux = 0
        
    def envoyer_message_jarvis(self, message, timeout=60):
        """Envoie un message à JARVIS via DeepSeek"""
        try:
            payload = {
                "model": "deepseek-ai/DeepSeek-R1-0528",
                "messages": [
                    {"role": "user", "content": message}
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }
            
            response = requests.post(self.base_url, json=payload, timeout=timeout)
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"]
            else:
                return f"Erreur {response.status_code}"
                
        except Exception as e:
            return f"Erreur: {e}"
    
    def test_connexion_internet(self):
        """Test 1: Capacité d'aller sur Internet"""
        log("TEST 1: Connexion Internet")
        
        try:
            # Test 1a: Demander à JARVIS d'aller sur un site
            message = """
            JARVIS, peux-tu aller sur le site https://www.google.com et me dire ce que tu vois ?
            Utilise tes capacités de navigation web pour accéder au site.
            """
            
            reponse = self.envoyer_message_jarvis(message)
            
            # Analyser la réponse
            indicateurs_web = [
                "google", "site", "page", "navigateur", "web", "internet",
                "accès", "connexion", "url", "lien"
            ]
            
            score_web = sum(1 for mot in indicateurs_web if mot in reponse.lower())
            
            if score_web >= 3:
                log("✅ JARVIS semble capable d'accéder à Internet")
                self.tests_reussis += 1
                return True
            else:
                log("❌ JARVIS ne semble pas pouvoir accéder à Internet")
                log(f"Réponse: {reponse[:200]}...")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test Internet: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_analyse_code(self):
        """Test 2: Capacité d'analyser du code"""
        log("TEST 2: Analyse de code")
        
        try:
            # Code Python à analyser
            code_test = '''
def calculer_charges_sociales(salaire_brut):
    """Calcule les charges sociales pour un salaire"""
    charges = {
        "securite_sociale": salaire_brut * 0.1545,
        "chomage": salaire_brut * 0.0405,
        "retraite": salaire_brut * 0.0472
    }
    return charges

# Test avec 3000€
resultat = calculer_charges_sociales(3000)
print(resultat)
'''
            
            message = f"""
            JARVIS, analyse ce code Python et dis-moi :
            1. Ce qu'il fait
            2. S'il y a des erreurs
            3. Comment l'améliorer
            4. Calcule le résultat pour 3000€
            
            Code à analyser :
            {code_test}
            """
            
            reponse = self.envoyer_message_jarvis(message)
            
            # Analyser la réponse
            indicateurs_code = [
                "fonction", "calcule", "charges", "sociales", "salaire",
                "3000", "463", "121", "141", "python", "code"
            ]
            
            score_code = sum(1 for mot in indicateurs_code if mot in reponse.lower())
            
            if score_code >= 5:
                log("✅ JARVIS peut analyser le code correctement")
                self.tests_reussis += 1
                return True
            else:
                log("❌ JARVIS a des difficultés avec l'analyse de code")
                log(f"Réponse: {reponse[:200]}...")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test code: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_connexion_visual_studio(self):
        """Test 3: Capacité de se connecter à Visual Studio"""
        log("TEST 3: Connexion Visual Studio")
        
        try:
            # Test 3a: Demander à JARVIS de lancer VS Code
            message = """
            JARVIS, peux-tu lancer Visual Studio Code sur mon système ?
            Utilise tes capacités de lancement d'applications pour ouvrir VS Code.
            Si tu ne peux pas le lancer directement, explique-moi comment faire.
            """
            
            reponse = self.envoyer_message_jarvis(message)
            
            # Test 3b: Vérifier si VS Code est installé
            vs_code_paths = [
                "/Applications/Visual Studio Code.app",
                "/usr/local/bin/code",
                "/opt/homebrew/bin/code"
            ]
            
            vs_code_installe = any(os.path.exists(path) for path in vs_code_paths)
            
            # Analyser la réponse
            indicateurs_vs = [
                "visual", "studio", "code", "vscode", "lancer", "ouvrir",
                "application", "éditeur", "développement"
            ]
            
            score_vs = sum(1 for mot in indicateurs_vs if mot in reponse.lower())
            
            if score_vs >= 3 or vs_code_installe:
                log("✅ JARVIS peut interagir avec Visual Studio Code")
                if vs_code_installe:
                    log("✅ VS Code détecté sur le système")
                self.tests_reussis += 1
                return True
            else:
                log("❌ JARVIS ne peut pas se connecter à Visual Studio")
                log(f"Réponse: {reponse[:200]}...")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test Visual Studio: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_boutons_interface(self):
        """Test 4: Test des boutons de l'interface"""
        log("TEST 4: Boutons interface")
        
        try:
            # Vérifier que l'interface est accessible
            response = requests.get(self.interface_url, timeout=10)
            if response.status_code != 200:
                log("❌ Interface JARVIS non accessible")
                return False
            
            # Analyser le contenu HTML pour détecter les boutons
            html_content = response.text
            
            boutons_attendus = [
                "🔊", "🎤", "📷", "➤",  # Boutons principaux
                "🗑️", "🛑",  # Boutons contrôle
                "🧠", "💾", "🔍"  # Boutons mémoire
            ]
            
            boutons_trouves = 0
            for bouton in boutons_attendus:
                if bouton in html_content:
                    boutons_trouves += 1
                    log(f"✅ Bouton trouvé: {bouton}")
                else:
                    log(f"❌ Bouton manquant: {bouton}")
            
            if boutons_trouves >= len(boutons_attendus) * 0.7:  # 70% des boutons
                log(f"✅ Interface boutons OK: {boutons_trouves}/{len(boutons_attendus)}")
                self.tests_reussis += 1
                return True
            else:
                log(f"❌ Interface boutons incomplète: {boutons_trouves}/{len(boutons_attendus)}")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test boutons: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_lancement_applications(self):
        """Test 5: Capacité de lancer des applications"""
        log("TEST 5: Lancement applications")
        
        try:
            # Test avec une application simple
            message = """
            JARVIS, peux-tu lancer l'application Calculatrice sur mon Mac ?
            Utilise la commande 'open -a Calculator' ou équivalent.
            """
            
            reponse = self.envoyer_message_jarvis(message)
            
            # Vérifier si JARVIS comprend les commandes système
            indicateurs_apps = [
                "open", "calculator", "calculatrice", "application",
                "lancer", "commande", "terminal", "système"
            ]
            
            score_apps = sum(1 for mot in indicateurs_apps if mot in reponse.lower())
            
            # Test réel de lancement (sans risque)
            try:
                # Tester si on peut lancer la calculatrice
                result = subprocess.run(
                    ["open", "-a", "Calculator"], 
                    capture_output=True, 
                    timeout=5
                )
                lancement_reussi = result.returncode == 0
            except:
                lancement_reussi = False
            
            if score_apps >= 3 or lancement_reussi:
                log("✅ JARVIS peut lancer des applications")
                if lancement_reussi:
                    log("✅ Test lancement Calculatrice réussi")
                self.tests_reussis += 1
                return True
            else:
                log("❌ JARVIS ne peut pas lancer d'applications")
                log(f"Réponse: {reponse[:200]}...")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test applications: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def test_capacites_avancees(self):
        """Test 6: Capacités avancées (BTP, calculs, etc.)"""
        log("TEST 6: Capacités avancées")
        
        try:
            message = """
            JARVIS, montre-moi tes capacités avancées :
            1. Calcule les charges URSSAF pour un salaire de 2500€
            2. Donne-moi 3 conseils pour gérer un chantier BTP
            3. Quel est ton QI actuel ?
            4. Combien de neurones as-tu ?
            """
            
            reponse = self.envoyer_message_jarvis(message, timeout=90)
            
            # Analyser les capacités
            indicateurs_avances = [
                "charges", "urssaf", "2500", "btp", "chantier",
                "qi", "neurones", "89", "milliards", "conseil"
            ]
            
            score_avances = sum(1 for mot in indicateurs_avances if mot in reponse.lower())
            
            if score_avances >= 6:
                log("✅ JARVIS a des capacités avancées fonctionnelles")
                self.tests_reussis += 1
                return True
            else:
                log("❌ Capacités avancées limitées")
                log(f"Réponse: {reponse[:300]}...")
                return False
                
        except Exception as e:
            log(f"❌ Erreur test capacités: {e}")
            return False
        finally:
            self.tests_totaux += 1
    
    def executer_tous_les_tests(self):
        """Exécute tous les tests de fonctionnalités"""
        log("🚀 DÉBUT TEST COMPLET FONCTIONNALITÉS JARVIS")
        print("=" * 60)
        
        # Liste des tests
        tests = [
            ("Connexion Internet", self.test_connexion_internet),
            ("Analyse de code", self.test_analyse_code),
            ("Connexion Visual Studio", self.test_connexion_visual_studio),
            ("Boutons interface", self.test_boutons_interface),
            ("Lancement applications", self.test_lancement_applications),
            ("Capacités avancées", self.test_capacites_avancees)
        ]
        
        # Exécuter chaque test
        for nom_test, fonction_test in tests:
            log(f"🧪 EXÉCUTION: {nom_test}")
            try:
                resultat = fonction_test()
                self.resultats[nom_test] = resultat
                if resultat:
                    log(f"✅ RÉUSSI: {nom_test}")
                else:
                    log(f"❌ ÉCHOUÉ: {nom_test}")
            except Exception as e:
                log(f"💥 ERREUR: {nom_test} - {e}")
                self.resultats[nom_test] = False
            
            print("-" * 40)
            time.sleep(3)  # Pause entre tests
        
        # Résultats finaux
        self.afficher_resultats_finaux()
    
    def afficher_resultats_finaux(self):
        """Affiche les résultats finaux du test complet"""
        print("\n" + "=" * 60)
        log("📊 RÉSULTATS FINAUX TEST FONCTIONNALITÉS")
        print("=" * 60)
        
        pourcentage = (self.tests_reussis / self.tests_totaux) * 100 if self.tests_totaux > 0 else 0
        
        print(f"🎯 SCORE GLOBAL: {self.tests_reussis}/{self.tests_totaux} ({pourcentage:.1f}%)")
        print()
        
        # Détail par test
        for nom_test, resultat in self.resultats.items():
            statut = "✅ FONCTIONNEL" if resultat else "❌ NON FONCTIONNEL"
            print(f"  {statut}: {nom_test}")
        
        print()
        
        # Évaluation finale
        if pourcentage >= 90:
            print("🏆 JARVIS EST PARFAITEMENT FONCTIONNEL !")
            print("Toutes les capacités sont opérationnelles !")
        elif pourcentage >= 75:
            print("🥇 JARVIS EST TRÈS FONCTIONNEL !")
            print("La plupart des capacités marchent bien !")
        elif pourcentage >= 60:
            print("🥈 JARVIS EST FONCTIONNEL !")
            print("Les capacités de base fonctionnent !")
        elif pourcentage >= 40:
            print("🥉 JARVIS EST PARTIELLEMENT FONCTIONNEL !")
            print("Certaines capacités nécessitent des améliorations !")
        else:
            print("💥 JARVIS A DES PROBLÈMES MAJEURS !")
            print("Intervention urgente nécessaire !")
        
        print("=" * 60)

def main():
    """Fonction principale"""
    print("🧪 TESTEUR COMPLET FONCTIONNALITÉS JARVIS")
    print("Test Internet, Code, Visual Studio, Boutons")
    print("=" * 50)
    
    # Créer et lancer le testeur
    testeur = TesteurFonctionnalitesJarvis()
    testeur.executer_tous_les_tests()

if __name__ == "__main__":
    main()
