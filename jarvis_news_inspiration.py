#!/usr/bin/env python3
"""
📰 JARVIS NEWS INSPIRATION MODULE
Système de veille automatique pour que JARVIS s'inspire de l'actualité
et crée des projets basés sur les tendances

Créé pour Jean-Luc Passave
"""

import json
import time
import requests
import feedparser
from datetime import datetime, timedelta
import threading

class JarvisNewsInspiration:
    def __init__(self):
        self.news_sources = {
            "tech": [
                "https://feeds.feedburner.com/TechCrunch",
                "https://www.wired.com/feed/rss",
                "https://techcrunch.com/feed/",
                "https://www.theverge.com/rss/index.xml"
            ],
            "ai": [
                "https://feeds.feedburner.com/oreilly/radar",
                "https://www.artificialintelligence-news.com/feed/",
                "https://venturebeat.com/ai/feed/"
            ],
            "innovation": [
                "https://www.fastcompany.com/section/innovation/rss",
                "https://www.technologyreview.com/feed/"
            ]
        }
        
        self.inspiration_file = "jarvis_news_inspiration.json"
        self.trends_file = "jarvis_detected_trends.json"
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        
        self.last_check_time = 0
        self.check_interval = 3600  # 1 heure
        self.is_monitoring = True
        
        # Mots-clés d'intérêt pour Jean-Luc
        self.interest_keywords = [
            "intelligence artificielle", "ai", "machine learning", "automation",
            "python", "javascript", "programming", "innovation", "technology",
            "startup", "productivity", "efficiency", "creative", "music",
            "electronic", "future", "revolutionary", "breakthrough"
        ]
        
        self.start_news_monitoring()

    def fetch_news_from_source(self, url, category):
        """RÉCUPÈRE LES NEWS D'UNE SOURCE RSS"""
        try:
            feed = feedparser.parse(url)
            news_items = []
            
            for entry in feed.entries[:10]:  # 10 derniers articles
                # Vérifier si l'article est récent (dernières 24h)
                try:
                    pub_date = datetime(*entry.published_parsed[:6])
                    if datetime.now() - pub_date > timedelta(days=1):
                        continue
                except:
                    pub_date = datetime.now()
                
                # Vérifier si l'article contient des mots-clés d'intérêt
                title_lower = entry.title.lower()
                summary_lower = getattr(entry, 'summary', '').lower()
                content = f"{title_lower} {summary_lower}"
                
                if any(keyword in content for keyword in self.interest_keywords):
                    news_items.append({
                        "title": entry.title,
                        "summary": getattr(entry, 'summary', '')[:300],
                        "link": entry.link,
                        "published": pub_date.isoformat(),
                        "category": category,
                        "source": url,
                        "relevance_score": self.calculate_relevance(content)
                    })
            
            return news_items
            
        except Exception as e:
            print(f"❌ Erreur récupération news {url}: {e}")
            return []

    def calculate_relevance(self, content):
        """CALCULE LA PERTINENCE D'UN ARTICLE"""
        score = 0
        content_lower = content.lower()
        
        # Mots-clés haute priorité
        high_priority = ["intelligence artificielle", "ai", "automation", "innovation", "revolutionary"]
        medium_priority = ["python", "javascript", "technology", "startup", "creative"]
        low_priority = ["programming", "music", "electronic", "future"]
        
        for keyword in high_priority:
            if keyword in content_lower:
                score += 3
        
        for keyword in medium_priority:
            if keyword in content_lower:
                score += 2
        
        for keyword in low_priority:
            if keyword in content_lower:
                score += 1
        
        return min(score, 10)  # Score max de 10

    def fetch_all_news(self):
        """RÉCUPÈRE TOUTES LES NEWS DE TOUTES LES SOURCES"""
        all_news = []
        
        for category, sources in self.news_sources.items():
            print(f"📰 Récupération news {category}...")
            
            for source in sources:
                news_items = self.fetch_news_from_source(source, category)
                all_news.extend(news_items)
                time.sleep(1)  # Pause entre les requêtes
        
        # Trier par pertinence
        all_news.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return all_news[:20]  # Top 20 articles les plus pertinents

    def detect_trends(self, news_items):
        """DÉTECTE LES TENDANCES DANS LES NEWS"""
        trends = {}
        
        for item in news_items:
            content = f"{item['title']} {item['summary']}".lower()
            
            # Compter les occurrences de mots-clés
            for keyword in self.interest_keywords:
                if keyword in content:
                    trends[keyword] = trends.get(keyword, 0) + item['relevance_score']
        
        # Trier les tendances par score
        sorted_trends = sorted(trends.items(), key=lambda x: x[1], reverse=True)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "top_trends": sorted_trends[:10],
            "total_articles_analyzed": len(news_items),
            "trend_analysis": self.analyze_trend_patterns(sorted_trends[:5])
        }

    def analyze_trend_patterns(self, top_trends):
        """ANALYSE LES PATTERNS DES TENDANCES"""
        if not top_trends:
            return "Aucune tendance significative détectée"
        
        dominant_trend = top_trends[0][0]
        trend_score = top_trends[0][1]
        
        if trend_score > 15:
            return f"Tendance dominante très forte: {dominant_trend}"
        elif trend_score > 10:
            return f"Tendance significative: {dominant_trend}"
        elif trend_score > 5:
            return f"Tendance émergente: {dominant_trend}"
        else:
            return "Tendances dispersées, pas de dominante claire"

    def generate_inspired_project(self, news_items, trends):
        """GÉNÈRE UN PROJET CRÉATIF INSPIRÉ DES NEWS"""
        
        # Sélectionner les articles les plus pertinents
        top_articles = news_items[:3]
        top_trends = trends['top_trends'][:3]
        
        inspiration_prompt = f"""
🌍 GÉNÉRATION PROJET INSPIRÉ DE L'ACTUALITÉ

🎯 MISSION : Crée un projet créatif innovant inspiré des dernières tendances tech

📰 ACTUALITÉS INSPIRANTES :
{chr(10).join([f"• {article['title']}: {article['summary'][:150]}..." for article in top_articles])}

📊 TENDANCES DÉTECTÉES :
{chr(10).join([f"• {trend[0]} (score: {trend[1]})" for trend in top_trends])}

🧠 ANALYSE : {trends['trend_analysis']}

💡 GÉNÈRE UN PROJET CRÉATIF QUI :
1. S'inspire directement de ces actualités
2. Utilise les tendances détectées
3. Soit réalisable par Jean-Luc Passave
4. Apporte une innovation ou amélioration
5. Soit dans l'un de ces domaines : code, IA, automation, musique, écriture

🚀 Sois visionnaire et connecte l'actualité à un projet concret !
        """
        
        try:
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es JARVIS, expert en innovation et créativité. Tu crées des projets inspirés de l'actualité pour Jean-Luc Passave. Sois visionnaire, pratique et innovant."
                        },
                        {
                            "role": "user",
                            "content": inspiration_prompt
                        }
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.8
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                project_content = result['choices'][0]['message']['content']
                
                inspired_project = {
                    "id": f"news_inspired_{int(time.time())}",
                    "type": "news_inspired",
                    "content": project_content,
                    "inspiration_sources": [article['title'] for article in top_articles],
                    "trends_used": [trend[0] for trend in top_trends],
                    "news_articles": top_articles,
                    "timestamp": datetime.now().isoformat(),
                    "relevance_score": sum(article['relevance_score'] for article in top_articles)
                }
                
                return inspired_project
            else:
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération projet inspiré: {e}")
            return None

    def save_inspiration_data(self, news_items, trends, inspired_project=None):
        """SAUVEGARDE LES DONNÉES D'INSPIRATION"""
        try:
            inspiration_data = {
                "timestamp": datetime.now().isoformat(),
                "news_items": news_items,
                "trends": trends,
                "inspired_project": inspired_project,
                "total_sources_checked": sum(len(sources) for sources in self.news_sources.values())
            }
            
            # Charger les données existantes
            try:
                with open(self.inspiration_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except:
                existing_data = []
            
            existing_data.append(inspiration_data)
            
            # Garder seulement les 50 dernières inspirations
            if len(existing_data) > 50:
                existing_data = existing_data[-50:]
            
            # Sauvegarder
            with open(self.inspiration_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)
            
            # Sauvegarder les tendances séparément
            with open(self.trends_file, 'w', encoding='utf-8') as f:
                json.dump(trends, f, indent=2, ensure_ascii=False)
            
            print("💾 Données d'inspiration sauvegardées")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde inspiration: {e}")

    def notify_inspired_project(self, inspired_project):
        """NOTIFIE LE PROJET INSPIRÉ VIA WHATSAPP"""
        try:
            if not inspired_project:
                return
            
            # Créer la notification
            notification = {
                "timestamp": datetime.now().isoformat(),
                "project_id": inspired_project['id'],
                "notification": f"""🌍 INSPIRATION ACTUALITÉ ! 🌍

📰 J'ai analysé les dernières news tech et j'ai eu une inspiration !

💡 **Projet inspiré de l'actualité :**
{inspired_project['content'][:300]}...

📊 **Sources d'inspiration :**
{chr(10).join([f"• {source}" for source in inspired_project['inspiration_sources'][:3]])}

🔥 **Tendances utilisées :**
{', '.join(inspired_project['trends_used'][:3])}

💬 Répondez "VOIR" pour le projet complet ou "DÉVELOPPER" pour l'améliorer !

🤖 JARVIS - Inspiré par l'actualité mondiale"""
            }
            
            # Sauvegarder pour WhatsApp
            with open('jarvis_creative_notifications.json', 'a', encoding='utf-8') as f:
                json.dump(notification, f, ensure_ascii=False)
                f.write('\n')
            
            print("📱 Notification d'inspiration créée pour WhatsApp")
            
        except Exception as e:
            print(f"❌ Erreur notification inspiration: {e}")

    def run_inspiration_cycle(self):
        """EXÉCUTE UN CYCLE COMPLET D'INSPIRATION"""
        print("🌍 Démarrage cycle d'inspiration actualité...")
        
        # Récupérer les news
        news_items = self.fetch_all_news()
        print(f"📰 {len(news_items)} articles pertinents trouvés")
        
        if not news_items:
            print("ℹ️ Aucune news pertinente trouvée")
            return
        
        # Détecter les tendances
        trends = self.detect_trends(news_items)
        print(f"📊 Tendances détectées: {trends['trend_analysis']}")
        
        # Générer un projet inspiré
        inspired_project = self.generate_inspired_project(news_items, trends)
        
        if inspired_project:
            print(f"💡 Projet inspiré généré: {inspired_project['id']}")
            
            # Notifier via WhatsApp
            self.notify_inspired_project(inspired_project)
        
        # Sauvegarder tout
        self.save_inspiration_data(news_items, trends, inspired_project)
        
        self.last_check_time = time.time()
        print("✅ Cycle d'inspiration terminé")

    def start_news_monitoring(self):
        """DÉMARRE LA SURVEILLANCE DES NEWS EN ARRIÈRE-PLAN"""
        def monitoring_worker():
            while self.is_monitoring:
                try:
                    if time.time() - self.last_check_time > self.check_interval:
                        self.run_inspiration_cycle()
                    
                    time.sleep(300)  # Vérifier toutes les 5 minutes
                    
                except Exception as e:
                    print(f"❌ Erreur monitoring news: {e}")
                    time.sleep(600)  # Attendre 10 minutes en cas d'erreur
        
        # Lancer en thread séparé
        monitoring_thread = threading.Thread(target=monitoring_worker, daemon=True)
        monitoring_thread.start()
        print("📰 Surveillance des news démarrée")

    def get_inspiration_stats(self):
        """STATISTIQUES D'INSPIRATION"""
        try:
            with open(self.inspiration_file, 'r', encoding='utf-8') as f:
                inspiration_data = json.load(f)
            
            total_cycles = len(inspiration_data)
            total_articles = sum(len(cycle.get('news_items', [])) for cycle in inspiration_data)
            total_projects = sum(1 for cycle in inspiration_data if cycle.get('inspired_project'))
            
            # Dernières tendances
            try:
                with open(self.trends_file, 'r', encoding='utf-8') as f:
                    latest_trends = json.load(f)
            except:
                latest_trends = {"top_trends": []}
            
            return {
                "total_inspiration_cycles": total_cycles,
                "total_articles_analyzed": total_articles,
                "total_inspired_projects": total_projects,
                "latest_trends": latest_trends.get('top_trends', [])[:5],
                "last_check": datetime.fromtimestamp(self.last_check_time).isoformat() if self.last_check_time else None,
                "monitoring_active": self.is_monitoring
            }
            
        except:
            return {
                "total_inspiration_cycles": 0,
                "total_articles_analyzed": 0,
                "total_inspired_projects": 0,
                "latest_trends": [],
                "monitoring_active": self.is_monitoring
            }

if __name__ == "__main__":
    print("📰 JARVIS NEWS INSPIRATION MODULE")
    print("=================================")
    
    news_inspiration = JarvisNewsInspiration()
    
    # Test d'un cycle d'inspiration
    print("🧪 Test cycle d'inspiration...")
    news_inspiration.run_inspiration_cycle()
    
    # Afficher les stats
    stats = news_inspiration.get_inspiration_stats()
    print(f"\n📊 Stats inspiration: {stats}")
