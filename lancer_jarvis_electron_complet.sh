#!/bin/bash

# 🚀 LANCEMENT COMPLET JARVIS ELECTRON
# Interface JARVIS + Application Electron avec curseurs et notifications
# Créé pour Jean-Luc Passave

echo "🚀 ================================"
echo "🤖 LANCEMENT JARVIS ELECTRON COMPLET"
echo "🚀 ================================"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction d'affichage coloré
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier si on est dans le bon répertoire
if [ ! -f "jarvis_interface_propre.py" ]; then
    print_error "Fichier jarvis_interface_propre.py non trouvé"
    print_status "Changement vers le répertoire JARVIS..."
    cd /Volumes/seagate/Louna_Electron_Latest
    
    if [ ! -f "jarvis_interface_propre.py" ]; then
        print_error "Impossible de trouver JARVIS"
        exit 1
    fi
fi

print_success "Répertoire JARVIS trouvé"

# Vérifier l'environnement virtuel
if [ ! -d "venv_deepseek" ]; then
    print_error "Environnement virtuel venv_deepseek non trouvé"
    exit 1
fi

print_success "Environnement virtuel trouvé"

# Fonction pour tuer les processus existants
cleanup_processes() {
    print_status "Nettoyage des processus existants..."
    
    # Tuer JARVIS existant
    pkill -f "jarvis_interface_propre.py" 2>/dev/null
    
    # Tuer Electron existant
    pkill -f "jarvis_electron_force.js" 2>/dev/null
    pkill -f "Electron" 2>/dev/null
    
    # Attendre un peu
    sleep 2
    
    print_success "Processus nettoyés"
}

# Fonction pour démarrer JARVIS
start_jarvis() {
    print_status "Démarrage de l'interface JARVIS..."
    
    # Activer l'environnement virtuel et démarrer JARVIS
    source venv_deepseek/bin/activate
    
    # Démarrer JARVIS en arrière-plan
    python jarvis_interface_propre.py > jarvis.log 2>&1 &
    JARVIS_PID=$!
    
    print_status "JARVIS démarré (PID: $JARVIS_PID)"
    
    # Attendre que JARVIS soit prêt
    print_status "Attente du démarrage de JARVIS..."
    
    for i in {1..30}; do
        if curl -s http://localhost:7867 > /dev/null 2>&1; then
            print_success "JARVIS accessible sur http://localhost:7867"
            return 0
        fi
        
        echo -ne "\r${CYAN}⏳ Attente JARVIS... ${i}/30s${NC}"
        sleep 1
    done
    
    echo ""
    print_error "JARVIS n'a pas démarré dans les temps"
    return 1
}

# Fonction pour démarrer Electron
start_electron() {
    print_status "Démarrage de l'application Electron..."
    
    # Vérifier que Node.js et npm sont disponibles
    if ! command -v npm &> /dev/null; then
        print_error "npm non trouvé - Veuillez installer Node.js"
        return 1
    fi
    
    # Démarrer Electron
    npm start > electron.log 2>&1 &
    ELECTRON_PID=$!
    
    print_success "Application Electron démarrée (PID: $ELECTRON_PID)"
    
    return 0
}

# Fonction pour afficher le statut
show_status() {
    echo ""
    print_success "🎉 JARVIS ELECTRON COMPLET DÉMARRÉ !"
    echo ""
    echo -e "${PURPLE}📊 INFORMATIONS :${NC}"
    echo -e "  🌐 Interface Web : ${CYAN}http://localhost:7867${NC}"
    echo -e "  🖥️  Application Electron : ${CYAN}Fenêtre ouverte${NC}"
    echo -e "  🔔 Notifications : ${GREEN}Cliquables pour copier${NC}"
    echo -e "  📊 Curseurs d'avancement : ${GREEN}Temps réel${NC}"
    echo -e "  🚀 Détection M4 : ${GREEN}Optimisations actives${NC}"
    echo ""
    echo -e "${YELLOW}🎯 NOUVELLES FONCTIONNALITÉS :${NC}"
    echo -e "  • ${GREEN}🔔 Notifications cliquables${NC} - Cliquez pour copier le code"
    echo -e "  • ${GREEN}📊 Curseurs d'avancement${NC} - Voir l'état des projets en temps réel"
    echo -e "  • ${GREEN}🖥️ Statut M4${NC} - Informations détaillées sur votre processeur"
    echo -e "  • ${GREEN}🚀 Démo Projet${NC} - Tester le système de progression"
    echo ""
    echo -e "${BLUE}💡 UTILISATION :${NC}"
    echo -e "  1. Utilisez les boutons ${CYAN}🔔 Notifications${NC} et ${CYAN}📊 Projets Actifs${NC}"
    echo -e "  2. Cliquez ${CYAN}🚀 Démo Projet${NC} pour voir les curseurs en action"
    echo -e "  3. Cliquez sur les notifications pour copier le contenu"
    echo -e "  4. L'application Electron s'adapte automatiquement aux mises à jour"
    echo ""
}

# Fonction pour surveiller les processus
monitor_processes() {
    print_status "Surveillance des processus (Ctrl+C pour arrêter)..."
    
    while true; do
        # Vérifier JARVIS
        if ! curl -s http://localhost:7867 > /dev/null 2>&1; then
            print_warning "JARVIS non accessible - Redémarrage..."
            start_jarvis
        fi
        
        sleep 30
    done
}

# Fonction de nettoyage à la sortie
cleanup_on_exit() {
    echo ""
    print_status "Arrêt de JARVIS Electron..."
    
    # Tuer les processus
    if [ ! -z "$JARVIS_PID" ]; then
        kill $JARVIS_PID 2>/dev/null
    fi
    
    if [ ! -z "$ELECTRON_PID" ]; then
        kill $ELECTRON_PID 2>/dev/null
    fi
    
    cleanup_processes
    
    print_success "JARVIS Electron arrêté proprement"
    exit 0
}

# Capturer Ctrl+C
trap cleanup_on_exit SIGINT SIGTERM

# DÉMARRAGE PRINCIPAL
print_status "Début du processus de démarrage..."

# 1. Nettoyer les processus existants
cleanup_processes

# 2. Démarrer JARVIS
if ! start_jarvis; then
    print_error "Échec du démarrage de JARVIS"
    exit 1
fi

# 3. Démarrer Electron
if ! start_electron; then
    print_error "Échec du démarrage d'Electron"
    exit 1
fi

# 4. Afficher le statut
show_status

# 5. Surveiller les processus
monitor_processes
