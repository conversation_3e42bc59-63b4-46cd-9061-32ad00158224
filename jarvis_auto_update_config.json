{"auto_update_enabled": true, "check_interval_hours": 6, "backup_before_update": true, "max_backups": 10, "last_check": "2025-06-19T23:35:26.123483", "system_fingerprint": "561b743913dba8c0b9d5f48827685ae1", "compatibility_rules": {"python_min_version": "3.8", "gradio_compatibility": "auto", "macos_compatibility": "auto"}, "update_sources": ["system_updates", "python_packages", "gradio_updates", "dependencies"]}