#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DÉTECTEUR ERREUR 404 AVANCÉ - JEAN-LUC PASSAVE
Trouve la source exacte de l'erreur 404 persistante
"""

import requests
import subprocess
import time
import os
import json
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔍 [{timestamp}] {message}")

def test_url_complete(url):
    """Test complet d'une URL"""
    log(f"🌐 TEST COMPLET: {url}")
    
    try:
        # Test avec différentes méthodes
        methods = ['GET', 'HEAD', 'OPTIONS']
        
        for method in methods:
            try:
                if method == 'GET':
                    response = requests.get(url, timeout=5)
                elif method == 'HEAD':
                    response = requests.head(url, timeout=5)
                elif method == 'OPTIONS':
                    response = requests.options(url, timeout=5)
                
                print(f"   {method}: {response.status_code}")
                
                if response.status_code == 404:
                    print(f"   🚨 ERREUR 404 avec méthode {method}")
                    return False, method
                    
            except Exception as e:
                print(f"   {method}: ERREUR {e}")
        
        # Test avec headers spéciaux
        headers_tests = [
            {'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'},
            {'Accept': 'text/html,application/xhtml+xml'},
            {'Cache-Control': 'no-cache'},
            {'Pragma': 'no-cache'}
        ]
        
        for i, headers in enumerate(headers_tests):
            try:
                response = requests.get(url, headers=headers, timeout=5)
                print(f"   Headers {i+1}: {response.status_code}")
                
                if response.status_code == 404:
                    print(f"   🚨 ERREUR 404 avec headers {headers}")
                    return False, f"headers_{i+1}"
                    
            except Exception as e:
                print(f"   Headers {i+1}: ERREUR {e}")
        
        return True, "OK"
        
    except Exception as e:
        print(f"   ❌ ERREUR GLOBALE: {e}")
        return False, str(e)

def analyser_logs_gradio():
    """Analyse les logs Gradio pour erreurs 404"""
    log("📋 ANALYSE LOGS GRADIO")
    
    try:
        # Chercher processus JARVIS actuel
        result = subprocess.run([
            "ps", "aux"
        ], capture_output=True, text=True)
        
        jarvis_pids = []
        for ligne in result.stdout.split('\n'):
            if 'jarvis_interface_propre.py' in ligne.lower() and 'python' in ligne:
                parts = ligne.split()
                if len(parts) > 1:
                    jarvis_pids.append(parts[1])
        
        print(f"   PIDs JARVIS trouvés: {jarvis_pids}")
        
        # Vérifier logs système pour ces PIDs
        for pid in jarvis_pids[:3]:  # Limiter à 3 PIDs
            try:
                result = subprocess.run([
                    "tail", "-50", f"/var/log/system.log"
                ], capture_output=True, text=True)
                
                if "404" in result.stdout:
                    print(f"   🚨 Erreur 404 trouvée dans logs système pour PID {pid}")
                    
            except:
                pass
        
        return jarvis_pids
        
    except Exception as e:
        print(f"   ❌ Erreur analyse logs: {e}")
        return []

def tester_routes_gradio():
    """Teste les routes Gradio spécifiques"""
    log("🛣️  TEST ROUTES GRADIO")
    
    base_url = "http://localhost:7860"
    routes_a_tester = [
        "/",
        "/api/",
        "/api/predict",
        "/queue/status",
        "/config",
        "/startup-events",
        "/theme.css",
        "/assets/",
        "/file/",
        "/proxy/"
    ]
    
    erreurs_404 = []
    
    for route in routes_a_tester:
        url = base_url + route
        try:
            response = requests.get(url, timeout=3)
            status = response.status_code
            
            if status == 404:
                erreurs_404.append(route)
                print(f"   🚨 404: {route}")
            elif status == 200:
                print(f"   ✅ 200: {route}")
            else:
                print(f"   ⚠️  {status}: {route}")
                
        except Exception as e:
            print(f"   ❌ ERR: {route} - {e}")
    
    if erreurs_404:
        print(f"\n🚨 ROUTES 404 DÉTECTÉES: {erreurs_404}")
        return erreurs_404
    else:
        print("\n✅ Aucune route 404 détectée")
        return []

def capturer_trafic_reseau():
    """Capture le trafic réseau vers port 7860"""
    log("📡 CAPTURE TRAFIC RÉSEAU")
    
    try:
        # Utiliser netstat pour voir les connexions
        result = subprocess.run([
            "netstat", "-an"
        ], capture_output=True, text=True)
        
        connexions_7860 = []
        for ligne in result.stdout.split('\n'):
            if ':7860' in ligne:
                connexions_7860.append(ligne.strip())
        
        if connexions_7860:
            print("   Connexions port 7860:")
            for conn in connexions_7860:
                print(f"     {conn}")
        else:
            print("   ❌ Aucune connexion port 7860 détectée")
        
        return connexions_7860
        
    except Exception as e:
        print(f"   ❌ Erreur capture réseau: {e}")
        return []

def verifier_configuration_gradio():
    """Vérifie la configuration Gradio dans le code"""
    log("⚙️  VÉRIFICATION CONFIG GRADIO")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Chercher configuration launch
        if 'demo.launch(' in contenu:
            # Extraire la configuration launch
            start = contenu.find('demo.launch(')
            end = contenu.find(')', start) + 1
            config_launch = contenu[start:end]
            
            print(f"   Configuration launch trouvée:")
            print(f"     {config_launch}")
            
            # Vérifier paramètres problématiques
            if 'server_name=' in config_launch:
                if '0.0.0.0' in config_launch:
                    print("   ✅ server_name=0.0.0.0 (correct)")
                else:
                    print("   ⚠️  server_name différent de 0.0.0.0")
            
            if 'server_port=' in config_launch:
                print("   ✅ server_port configuré")
            else:
                print("   ⚠️  server_port non configuré")
            
            if 'share=' in config_launch:
                if 'share=True' in config_launch:
                    print("   ⚠️  share=True (peut causer problèmes)")
                else:
                    print("   ✅ share=False")
            
            return config_launch
        else:
            print("   ❌ Configuration demo.launch() non trouvée")
            return None
            
    except Exception as e:
        print(f"   ❌ Erreur vérification config: {e}")
        return None

def diagnostic_complet_404():
    """Diagnostic complet pour trouver la source de l'erreur 404"""
    log("🔍 DIAGNOSTIC COMPLET ERREUR 404")
    print("=" * 60)
    
    rapport = {
        'timestamp': datetime.now().isoformat(),
        'tests': {}
    }
    
    # 1. Test URL principal
    log("ÉTAPE 1: Test URL principal")
    url_ok, url_details = test_url_complete("http://localhost:7860")
    rapport['tests']['url_principal'] = {'ok': url_ok, 'details': url_details}
    
    print("\n" + "=" * 40)
    
    # 2. Analyse logs
    log("ÉTAPE 2: Analyse logs")
    pids = analyser_logs_gradio()
    rapport['tests']['logs'] = {'pids': pids}
    
    print("\n" + "=" * 40)
    
    # 3. Test routes Gradio
    log("ÉTAPE 3: Test routes Gradio")
    routes_404 = tester_routes_gradio()
    rapport['tests']['routes'] = {'erreurs_404': routes_404}
    
    print("\n" + "=" * 40)
    
    # 4. Capture réseau
    log("ÉTAPE 4: Capture réseau")
    connexions = capturer_trafic_reseau()
    rapport['tests']['reseau'] = {'connexions': len(connexions)}
    
    print("\n" + "=" * 40)
    
    # 5. Config Gradio
    log("ÉTAPE 5: Configuration Gradio")
    config = verifier_configuration_gradio()
    rapport['tests']['config'] = {'config_trouvee': config is not None}
    
    # 6. Résumé et diagnostic
    print("\n" + "=" * 60)
    log("📊 RÉSUMÉ DIAGNOSTIC")
    print("=" * 60)
    
    if not url_ok:
        print(f"🚨 PROBLÈME PRINCIPAL: URL échoue avec {url_details}")
    else:
        print("✅ URL principale fonctionne")
    
    if routes_404:
        print(f"🚨 ROUTES PROBLÉMATIQUES: {routes_404}")
    else:
        print("✅ Toutes les routes testées fonctionnent")
    
    if not connexions:
        print("🚨 AUCUNE CONNEXION RÉSEAU DÉTECTÉE")
    else:
        print(f"✅ {len(connexions)} connexions réseau actives")
    
    # Sauvegarder rapport
    with open("rapport_diagnostic_404.json", 'w') as f:
        json.dump(rapport, f, indent=2)
    
    log("📄 Rapport sauvé: rapport_diagnostic_404.json")
    
    return rapport

def solution_selon_diagnostic():
    """Propose des solutions selon le diagnostic"""
    log("💡 SOLUTIONS PROPOSÉES")
    
    print("\n🔧 SOLUTIONS À ESSAYER:")
    print("1. Vider complètement le cache navigateur")
    print("2. Essayer navigation privée/incognito")
    print("3. Essayer un autre navigateur")
    print("4. Vérifier proxy/VPN")
    print("5. Redémarrer JARVIS avec port différent")
    print("6. Vérifier pare-feu/antivirus")
    
    print("\n🌐 URLS À TESTER:")
    print("- http://localhost:7860")
    print("- http://127.0.0.1:7860")
    print("- http://0.0.0.0:7860")
    
    print("\n📱 TEST MOBILE:")
    print("- Essayer depuis téléphone sur même réseau")
    print("- IP locale: http://[IP_MAC]:7860")

if __name__ == "__main__":
    print("🔍 DÉTECTEUR ERREUR 404 AVANCÉ")
    print("Trouve la source exacte de l'erreur 404")
    print("=" * 50)
    
    rapport = diagnostic_complet_404()
    solution_selon_diagnostic()
    
    print(f"\n📄 Rapport détaillé: rapport_diagnostic_404.json")
    print("🔍 Diagnostic terminé")
