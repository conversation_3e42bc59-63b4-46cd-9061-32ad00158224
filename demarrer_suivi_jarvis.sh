#!/bin/bash

# 📊 DÉMARRAGE SYSTÈME DE SUIVI JARVIS 24H/24
# Surveillance complète heure par heure, minute par minute
# Mode sommeil intelligent inclus
# Créé pour Jean-Luc Passave

echo "📊 ================================"
echo "🤖 DÉMARRAGE SUIVI JARVIS 24H/24"
echo "📊 ================================"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction d'affichage coloré
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier si on est dans le bon répertoire
if [ ! -f "jarvis_feuille_suivi_complet.py" ]; then
    print_error "Fichier jarvis_feuille_suivi_complet.py non trouvé"
    print_status "Changement vers le répertoire JARVIS..."
    cd /Volumes/seagate/Louna_Electron_Latest
    
    if [ ! -f "jarvis_feuille_suivi_complet.py" ]; then
        print_error "Impossible de trouver le système de suivi"
        exit 1
    fi
fi

print_success "Système de suivi trouvé"

# Vérifier l'environnement virtuel
if [ ! -d "venv_deepseek" ]; then
    print_error "Environnement virtuel venv_deepseek non trouvé"
    exit 1
fi

print_success "Environnement virtuel trouvé"

# Fonction pour tuer les processus de suivi existants
cleanup_suivi() {
    print_status "Nettoyage des processus de suivi existants..."
    
    # Tuer processus de suivi existant
    pkill -f "jarvis_feuille_suivi_complet.py" 2>/dev/null
    
    # Attendre un peu
    sleep 2
    
    print_success "Processus de suivi nettoyés"
}

# Fonction pour démarrer le suivi
start_suivi() {
    print_status "Démarrage du système de suivi 24h/24..."
    
    # Activer l'environnement virtuel et démarrer le suivi
    source venv_deepseek/bin/activate
    
    # Démarrer le suivi en arrière-plan
    python jarvis_feuille_suivi_complet.py > suivi_jarvis.log 2>&1 &
    SUIVI_PID=$!
    
    print_status "Système de suivi démarré (PID: $SUIVI_PID)"
    
    # Attendre que le suivi soit prêt
    print_status "Vérification du démarrage du suivi..."
    
    for i in {1..10}; do
        if [ -f "jarvis_feuille_suivi_24h.json" ]; then
            print_success "Feuille de suivi créée et accessible"
            return 0
        fi
        
        echo -ne "\r${CYAN}⏳ Attente initialisation suivi... ${i}/10s${NC}"
        sleep 1
    done
    
    echo ""
    print_warning "Le suivi a démarré mais la feuille n'est pas encore visible"
    return 0
}

# Fonction pour afficher le statut
show_status() {
    echo ""
    print_success "🎉 SYSTÈME DE SUIVI JARVIS 24H/24 DÉMARRÉ !"
    echo ""
    echo -e "${PURPLE}📊 INFORMATIONS :${NC}"
    echo -e "  📋 Feuille de suivi : ${CYAN}jarvis_feuille_suivi_24h.json${NC}"
    echo -e "  📊 Visualiseur : ${CYAN}python jarvis_visualiseur_suivi.py${NC}"
    echo -e "  📝 Log de suivi : ${CYAN}suivi_jarvis.log${NC}"
    echo ""
    echo -e "${YELLOW}🎯 FONCTIONNALITÉS :${NC}"
    echo -e "  • ${GREEN}📊 Surveillance continue${NC} - Heure par heure, minute par minute"
    echo -e "  • ${GREEN}😴 Mode sommeil intelligent${NC} - Économie d'énergie automatique"
    echo -e "  • ${GREEN}🔄 Détection d'activité${NC} - Réveil automatique"
    echo -e "  • ${GREEN}📈 Statistiques complètes${NC} - Temps actif/sommeil, réveils"
    echo -e "  • ${GREEN}⏰ Historique détaillé${NC} - Toutes les activités enregistrées"
    echo ""
    echo -e "${BLUE}💡 UTILISATION :${NC}"
    echo -e "  1. ${CYAN}Résumé global${NC} : python jarvis_visualiseur_suivi.py --resume"
    echo -e "  2. ${CYAN}Activité aujourd'hui${NC} : python jarvis_visualiseur_suivi.py --jour $(date +%Y-%m-%d)"
    echo -e "  3. ${CYAN}Activités récentes${NC} : python jarvis_visualiseur_suivi.py --recent 20"
    echo -e "  4. ${CYAN}Mode interactif${NC} : python jarvis_visualiseur_suivi.py --interactif"
    echo -e "  5. ${CYAN}Dans l'interface JARVIS${NC} : Boutons 📊 Résumé Suivi, 📅 Suivi Aujourd'hui, etc."
    echo ""
    echo -e "${GREEN}😴 MODE SOMMEIL :${NC}"
    echo -e "  • ${CYAN}Activation automatique${NC} après 5 minutes d'inactivité"
    echo -e "  • ${CYAN}Surveillance réduite${NC} toutes les 5 minutes en sommeil"
    echo -e "  • ${CYAN}Réveil automatique${NC} dès détection d'activité"
    echo -e "  • ${CYAN}Contrôle manuel${NC} via bouton 😴 Mode Sommeil dans l'interface"
    echo ""
}

# Fonction pour surveiller le suivi
monitor_suivi() {
    print_status "Surveillance du système de suivi (Ctrl+C pour arrêter)..."
    
    while true; do
        # Vérifier si le processus de suivi est toujours actif
        if ! pgrep -f "jarvis_feuille_suivi_complet.py" > /dev/null; then
            print_warning "Processus de suivi arrêté - Redémarrage..."
            start_suivi
        fi
        
        # Afficher un statut périodique
        if [ -f "jarvis_feuille_suivi_24h.json" ]; then
            # Compter les activités du jour
            activites_aujourd_hui=$(python -c "
import json
from datetime import datetime
try:
    with open('jarvis_feuille_suivi_24h.json', 'r') as f:
        data = json.load(f)
    date_aujourd_hui = datetime.now().strftime('%Y-%m-%d')
    suivi_jour = data.get('suivi_par_jour', {}).get(date_aujourd_hui, {})
    nb_activites = suivi_jour.get('resume_journee', {}).get('nombre_activites', 0)
    print(nb_activites)
except:
    print(0)
" 2>/dev/null)
            
            print_status "Suivi actif - ${activites_aujourd_hui} activités aujourd'hui"
        fi
        
        sleep 300  # Vérifier toutes les 5 minutes
    done
}

# Fonction de nettoyage à la sortie
cleanup_on_exit() {
    echo ""
    print_status "Arrêt du système de suivi..."
    
    # Tuer le processus de suivi
    if [ ! -z "$SUIVI_PID" ]; then
        kill $SUIVI_PID 2>/dev/null
    fi
    
    cleanup_suivi
    
    print_success "Système de suivi arrêté proprement"
    exit 0
}

# Capturer Ctrl+C
trap cleanup_on_exit SIGINT SIGTERM

# DÉMARRAGE PRINCIPAL
print_status "Début du processus de démarrage du suivi..."

# 1. Nettoyer les processus existants
cleanup_suivi

# 2. Démarrer le système de suivi
if ! start_suivi; then
    print_error "Échec du démarrage du système de suivi"
    exit 1
fi

# 3. Afficher le statut
show_status

# 4. Surveiller le système de suivi
monitor_suivi
