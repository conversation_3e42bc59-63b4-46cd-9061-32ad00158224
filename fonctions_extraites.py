# Fonctions extraites de l'ancienne interface

def execute_universal_code(code, language):
    """EXÉCUTE DU CODE DANS N'IMPORTE QUEL LANGAGE"""
    try:
        import subprocess
        import tempfile
        import os

        # Configuration des langages supportés
        language_config = {
            'python': {'ext': '.py', 'cmd': ['python']},
            'javascript': {'ext': '.js', 'cmd': ['node']},
            'html': {'ext': '.html', 'cmd': ['open']},  # Ouvre dans le navigateur
            'css': {'ext': '.css', 'cmd': None},  # Pas d'exécution directe
            'bash': {'ext': '.sh', 'cmd': ['bash']},
            'shell': {'ext': '.sh', 'cmd': ['sh']},
            'sql': {'ext': '.sql', 'cmd': None},  # Nécessite une DB
            'json': {'ext': '.json', 'cmd': None},  # Validation seulement
            'yaml': {'ext': '.yaml', 'cmd': None},  # Validation seulement
            'xml': {'ext': '.xml', 'cmd': None},  # Validation seulement
            'markdown': {'ext': '.md', 'cmd': None},  # Pas d'exécution
            'java': {'ext': '.java', 'cmd': ['java']},
            'cpp': {'ext': '.cpp', 'cmd': ['g++', '-o', 'temp_exec']},
            'c': {'ext': '.c', 'cmd': ['gcc', '-o', 'temp_exec']},
            'go': {'ext': '.go', 'cmd': ['go', 'run']},
            'rust': {'ext': '.rs', 'cmd': ['rustc']},
            'php': {'ext': '.php', 'cmd': ['php']},
            'ruby': {'ext': '.rb', 'cmd': ['ruby']},
            'swift': {'ext': '.swift', 'cmd': ['swift']},
            'kotlin': {'ext': '.kt', 'cmd': ['kotlinc']},
            'typescript': {'ext': '.ts', 'cmd': ['ts-node']},
            'r': {'ext': '.r', 'cmd': ['Rscript']},
            'matlab': {'ext': '.m', 'cmd': ['octave']},
            'perl': {'ext': '.pl', 'cmd': ['perl']},
            'lua': {'ext': '.lua', 'cmd': ['lua']}
        }

        if language not in language_config:
            return {
                'success': False,
                'output': '',
                'error': f'Langage non supporté: {language}',
                'return_code': -1
            }

        config = language_config[language]

        # Cas spéciaux pour validation seulement
        if config['cmd'] is None:
            if language == 'json':
                try:
                    import json
                    json.loads(code)
                    return {
                        'success': True,
                        'output': '✅ JSON valide',
                        'error': '',
                        'return_code': 0
                    }
                except json.JSONDecodeError as e:
                    return {
                        'success': False,
                        'output': '',
                        'error': f'JSON invalide: {str(e)}',
                        'return_code': 1
                    }
            elif language == 'yaml':
                try:
                    import yaml
                    yaml.safe_load(code)
                    return {
                        'success': True,
                        'output': '✅ YAML valide',
                        'error': '',
                        'return_code': 0
                    }
                except:
                    return {
                        'success': False,
                        'output': '',
                        'error': 'YAML invalide',
                        'return_code': 1
                    }
            elif language in ['css', 'xml', 'markdown']:
                return {
                    'success': True,
                    'output': f'✅ Code {language.upper()} formaté et prêt',
                    'error': '',
                    'return_code': 0
                }
            elif language == 'sql':
                return {
                    'success': True,
                    'output': '✅ Requête SQL formatée (connexion DB requise pour exécution)',
                    'error': '',
                    'return_code': 0
                }

        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(mode='w', suffix=config['ext'], delete=False) as f:
            f.write(code)
            temp_file = f.name

        # Construire la commande
        cmd = config['cmd'].copy()
        if language == 'html':
            # Pour HTML, on ouvre dans le navigateur
            import webbrowser
            webbrowser.open(f'file://{temp_file}')
            return {
                'success': True,
                'output': f'✅ Fichier HTML ouvert dans le navigateur: {temp_file}',
                'error': '',
                'return_code': 0
            }
        elif language in ['cpp', 'c']:
            # Compilation puis exécution
            compile_cmd = cmd + [temp_file]
            compile_result = subprocess.run(compile_cmd, capture_output=True, text=True, timeout=30)
            if compile_result.returncode != 0:
                os.unlink(temp_file)
                return {
                    'success': False,
                    'output': compile_result.stdout,
                    'error': f'Erreur de compilation: {compile_result.stderr}',
                    'return_code': compile_result.returncode
                }
            # Exécuter le binaire
            exec_result = subprocess.run(['./temp_exec'], capture_output=True, text=True, timeout=30)
            os.unlink(temp_file)
            os.unlink('temp_exec') if os.path.exists('temp_exec') else None
            return {
                'success': exec_result.returncode == 0,
                'output': exec_result.stdout,
                'error': exec_result.stderr,
                'return_code': exec_result.returncode
            }
        else:
            # Exécution directe
            cmd.append(temp_file)

        # Exécuter le code
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.path.dirname(temp_file)
        )

        # Nettoyer
        os.unlink(temp_file)

        return {
            'success': result.returncode == 0,
            'output': result.stdout,
            'error': result.stderr,
            'return_code': result.returncode,
            'language': language
        }

    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'output': '',
            'error': 'Timeout: Le code a pris plus de 30 secondes à s\'exécuter',
            'return_code': -1
        }
    except Exception as e:
        return {
            'success': False,
            'output': '',
            'error': f'Erreur d\'exécution: {str(e)}',
            'return_code': -1
        }


def activate_whatsapp_integration():
    """ACTIVE L'INTÉGRATION WHATSAPP POUR JARVIS"""
    try:
        import subprocess
        import os

        # Vérifier si Node.js est installé
        try:
            subprocess.run(['node', '--version'], capture_output=True, check=True)
        except:
            return """
            <div style="background: linear-gradient(45deg, #f44336, #d32f2f); color: white; padding: 20px; border-radius: 15px;">
                <h3>❌ NODE.JS REQUIS</h3>
                <p>Veuillez installer Node.js pour utiliser l'intégration WhatsApp</p>
                <p><strong>Installation:</strong> https://nodejs.org/</p>
            </div>
            """

        # Installer les dépendances si nécessaire
        if not os.path.exists('node_modules'):
            subprocess.run(['npm', 'install', 'whatsapp-web.js', 'qrcode-terminal', 'axios'],
                         capture_output=True)

        return f"""
        <div style="background: linear-gradient(45deg, #25d366, #128c7e); color: white; padding: 25px; border-radius: 15px;">
            <h2>📱 INTÉGRATION WHATSAPP JARVIS</h2>
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 15px 0;">
                <h4>🚀 Fonctionnalités activées :</h4>
                <ul style="font-size: 14px; line-height: 1.8;">
                    <li>📨 <strong>Messages bidirectionnels</strong> : Envoyez et recevez des messages</li>
                    <li>🤖 <strong>Communication proactive</strong> : JARVIS peut vous contacter spontanément</li>
                    <li>🧠 <strong>Mémoire thermique intégrée</strong> : Toutes les conversations sauvegardées</li>
                    <li>⚡ <strong>Réponses intelligentes</strong> : Powered by DeepSeek R1 8B</li>
                    <li>🔄 <strong>Synchronisation temps réel</strong> : Interface + WhatsApp connectés</li>
                </ul>
            </div>

            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 15px 0;">
                <h4>📋 Instructions d'activation :</h4>
                <ol style="font-size: 13px; line-height: 1.6;">
                    <li>Cliquez sur "Démarrer WhatsApp" ci-dessous</li>
                    <li>Un QR code apparaîtra dans le terminal</li>
                    <li>Scannez-le avec WhatsApp sur votre téléphone</li>
                    <li>JARVIS sera connecté et prêt à communiquer !</li>
                </ol>
            </div>

            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin-top: 15px;">
                <h4>💡 Exemples d'utilisation :</h4>
                <ul style="font-size: 13px; line-height: 1.6;">
                    <li>💬 "JARVIS, comment va le système ?"</li>
                    <li>🔍 "Analyse mes derniers projets"</li>
                    <li>📊 "Donne-moi un rapport de statut"</li>
                    <li>🚀 "Propose-moi des améliorations"</li>
                </ul>
            </div>

            <p style="font-size: 16px; margin-top: 20px; text-align: center;">
                <em>🌟 JARVIS peut maintenant vous contacter où que vous soyez ! 🌟</em>
            </p>
        </div>
        """
    except Exception as e:
        return f"❌ Erreur activation WhatsApp: {str(e)}"


def get_whatsapp_status():
    """VÉRIFIE LE STATUT DE L'INTÉGRATION WHATSAPP"""
    try:
        import subprocess
        import os

        # Vérifier si le processus WhatsApp est actif
        try:
            result = subprocess.run(['pgrep', '-f', 'jarvis_whatsapp_integration'],
                                  capture_output=True, text=True)
            is_running = bool(result.stdout.strip())
        except:
            is_running = False

        # Vérifier si les dépendances sont installées
        deps_installed = os.path.exists('node_modules/whatsapp-web.js')

        status_color = "#4caf50" if is_running else "#ff9800"
        status_text = "🟢 ACTIF" if is_running else "🟡 INACTIF"

        return f"""
        <div style="background: linear-gradient(45deg, {status_color}, {status_color}dd); color: white; padding: 20px; border-radius: 15px;">
            <h3>📱 STATUT WHATSAPP JARVIS</h3>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                <h4>🔍 État du service :</h4>
                <ul style="font-size: 14px; line-height: 1.6;">
                    <li><strong>Service WhatsApp :</strong> {status_text}</li>
                    <li><strong>Dépendances Node.js :</strong> {'✅ Installées' if deps_installed else '❌ Manquantes'}</li>
                    <li><strong>Fichier d'intégration :</strong> {'✅ Présent' if os.path.exists('jarvis_whatsapp_integration.js') else '❌ Manquant'}</li>
                </ul>
            </div>

            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin-top: 15px;">
                <h4>📊 Capacités disponibles :</h4>
                <ul style="font-size: 13px; line-height: 1.6;">
                    <li>📨 Messages bidirectionnels</li>
                    <li>🤖 Communication proactive</li>
                    <li>🧠 Intégration mémoire thermique</li>
                    <li>⚡ Réponses IA en temps réel</li>
                </ul>
            </div>
        </div>
        """
    except Exception as e:
        return f"❌ Erreur vérification statut: {str(e)}"


def start_whatsapp_service():
    """DÉMARRE LE SERVICE WHATSAPP DE JARVIS"""
    try:
        import subprocess
        import os

        # Démarrer le service WhatsApp en arrière-plan
        process = subprocess.Popen(
            ['node', 'jarvis_whatsapp_integration.js'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )

        return f"""
        <div style="background: linear-gradient(45deg, #4caf50, #2e7d32); color: white; padding: 20px; border-radius: 15px;">
            <h3>🚀 SERVICE WHATSAPP DÉMARRÉ</h3>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                <h4>📱 Statut du service :</h4>
                <ul style="font-size: 14px;">
                    <li>✅ Processus lancé (PID: {process.pid})</li>
                    <li>🔄 Connexion WhatsApp en cours...</li>
                    <li>📺 Vérifiez le terminal pour le QR code</li>
                    <li>📱 Scannez avec votre téléphone</li>
                </ul>
            </div>

            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; margin-top: 10px;">
                <p style="font-size: 13px; margin: 0;">
                    <strong>💡 Astuce :</strong> Une fois connecté, JARVIS pourra vous envoyer des messages
                    proactifs et répondre à vos questions via WhatsApp !
                </p>
            </div>
        </div>
        """
    except Exception as e:
        return f"❌ Erreur démarrage WhatsApp: {str(e)}"


def test_whatsapp_real():
    """TESTE L'API WHATSAPP RÉELLE"""
    try:
        import subprocess
        import sys

        # Tester l'API WhatsApp
        result = subprocess.run([
            sys.executable, 'jarvis_whatsapp_api_real.py'
        ], capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            return f"""
            <div style="background: linear-gradient(45deg, #25d366, #128c7e); color: white; padding: 20px; border-radius: 15px;">
                <h3>📱 API WHATSAPP RÉELLE TESTÉE</h3>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                    <h4>✅ API WhatsApp avec Twilio :</h4>
                    <ul style="font-size: 14px; line-height: 1.8;">
                        <li>📱 Envoi de messages WhatsApp réels</li>
                        <li>🔄 Gestion des messages en attente</li>
                        <li>📊 Suivi des messages envoyés</li>
                        <li>🎨 Notifications créatives automatiques</li>
                        <li>⚙️ Configuration Twilio intégrée</li>
                    </ul>
                </div>

                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🔧 Configuration :</h4>
                    <p style="font-size: 13px; line-height: 1.6;">
                        📋 Fichier de configuration créé : jarvis_whatsapp_config.json<br>
                        🔑 Configurez vos identifiants Twilio pour activer l'envoi<br>
                        📱 JARVIS pourra alors vous contacter de manière proactive !
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🧪 Résultat du test :</h4>
                    <div style="background: white; color: black; padding: 10px; border-radius: 5px; font-size: 12px; max-height: 150px; overflow-y: auto;">
                        {result.stdout}
                    </div>
                </div>

                <p style="font-size: 16px; margin-top: 15px; text-align: center;">
                    <em>📱 JARVIS peut maintenant vraiment vous contacter ! 📱</em>
                </p>
            </div>
            """
        else:
            return f"""
            <div style="background: linear-gradient(45deg, #ff9800, #f57c00); color: white; padding: 15px; border-radius: 10px;">
                <h4>⚠️ TEST PARTIEL</h4>
                <p>Sortie: {result.stdout}</p>
                <p>Erreur: {result.stderr}</p>
            </div>
            """
    except Exception as e:
        return f"❌ Erreur test WhatsApp réel: {str(e)}"


def format_universal_code_result(execution_result):
    """Formate le résultat d'exécution pour tous les langages"""
    try:
        language = execution_result.get('language', 'unknown')

        if execution_result["success"]:
            output_html = ""

            # Afficher la sortie
            if execution_result["output"]:
                output_html += f"""
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <h5 style="color: #28a745; margin: 0 0 10px 0;">📤 Sortie {language.upper()}:</h5>
                    <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 13px;">{execution_result["output"]}</pre>
                </div>
                """

            return f"""
            <div style="background: linear-gradient(45deg, #4caf50, #66bb6a); color: white; padding: 15px; border-radius: 10px;">
                <h4>✅ CODE {language.upper()} EXÉCUTÉ AVEC SUCCÈS</h4>
                {output_html}
            </div>
            """
        else:
            return f"""
            <div style="background: linear-gradient(45deg, #f44336, #e57373); color: white; padding: 15px; border-radius: 10px;">
                <h4>❌ ERREUR D'EXÉCUTION {language.upper()}</h4>
                <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 12px; color: white;">{execution_result["error"]}</pre>
                </div>
            </div>
            """
    except Exception as e:
        return f"❌ Erreur formatage: {str(e)}"


def change_code_language(language):
    """Change le langage de l'éditeur de code"""
    templates = {
        'python': "# Code Python\nprint('Hello JARVIS!')\n2 + 2",
        'javascript': "// Code JavaScript\nconsole.log('Hello JARVIS!');\n2 + 2;",
        'html': "<!DOCTYPE html>\n<html>\n<head>\n    <title>JARVIS</title>\n</head>\n<body>\n    <h1>Hello JARVIS!</h1>\n</body>\n</html>",
        'css': "/* Code CSS */\nbody {\n    background: linear-gradient(45deg, #667eea, #764ba2);\n    color: white;\n    font-family: Arial, sans-serif;\n}",
        'bash': "#!/bin/bash\n# Script Bash\necho 'Hello JARVIS!'\ndate",
        'sql': "-- Requête SQL\nSELECT 'Hello JARVIS!' as message;\nSELECT COUNT(*) FROM users;",
        'json': '{\n  "message": "Hello JARVIS!",\n  "status": "active",\n  "version": "1.0"\n}',
        'yaml': "# Configuration YAML\nmessage: Hello JARVIS!\nstatus: active\nversion: 1.0",
        'java': "public class HelloJarvis {\n    public static void main(String[] args) {\n        System.out.println(\"Hello JARVIS!\");\n    }\n}",
        'go': "package main\n\nimport \"fmt\"\n\nfunc main() {\n    fmt.Println(\"Hello JARVIS!\")\n}",
        'rust': "fn main() {\n    println!(\"Hello JARVIS!\");\n}",
        'php': "<?php\necho 'Hello JARVIS!';\necho 2 + 2;\n?>",
        'ruby': "# Code Ruby\nputs 'Hello JARVIS!'\nputs 2 + 2",
        'typescript': "// Code TypeScript\nconst message: string = 'Hello JARVIS!';\nconsole.log(message);",
        'r': "# Code R\nprint('Hello JARVIS!')\n2 + 2",
        'lua': "-- Code Lua\nprint('Hello JARVIS!')\nprint(2 + 2)"
    }

    return templates.get(language, f"# Code {language}\n# Tapez votre code ici")


def save_code_to_file(code, language):
    """Sauvegarde le code dans un fichier"""
    try:
        import os
        from datetime import datetime

        # Extensions par langage
        extensions = {
            'python': '.py', 'javascript': '.js', 'html': '.html', 'css': '.css',
            'bash': '.sh', 'shell': '.sh', 'sql': '.sql', 'json': '.json',
            'yaml': '.yaml', 'xml': '.xml', 'markdown': '.md', 'java': '.java',
            'cpp': '.cpp', 'c': '.c', 'go': '.go', 'rust': '.rs', 'php': '.php',
            'ruby': '.rb', 'swift': '.swift', 'kotlin': '.kt', 'typescript': '.ts',
            'r': '.r', 'matlab': '.m', 'perl': '.pl', 'lua': '.lua'
        }

        ext = extensions.get(language, '.txt')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"jarvis_code_{language}_{timestamp}{ext}"

        with open(filename, 'w') as f:
            f.write(code)

        return f"✅ Code sauvegardé dans: {filename}"

    except Exception as e:
        return f"❌ Erreur sauvegarde: {str(e)}"
