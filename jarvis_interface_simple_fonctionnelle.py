#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 INTERFACE JARVIS SIMPLE ET FONCTIONNELLE - JEAN-LUC PASSAVE
Version restaurée de la sauvegarde qui marchait bien
Modèle: DeepSeek R1 8B via VLLM (localhost:8000)
"""

import gradio as gr
import requests
import json
import time
import os
from datetime import datetime

# ============================================================================
# CONFIGURATION
# ============================================================================

# CONFIGURATION SERVEUR LOCAL - DEEPSEEK R1 8B
SERVER_URL = "http://localhost:8000/v1/chat/completions"
API_KEY = None
MODEL_NAME = "DeepSeek R1 0528 Qwen3 8B"  # Nom exact du modèle chargé
MEMORY_FILE = "thermal_memory_persistent.json"
CURRENT_AGENT = "agent1"
conversation_history = []

# SESSION HTTP PERSISTANTE AVEC KEEP-ALIVE (OPTIMISATION CRITIQUE)
http_session = requests.Session()
http_session.headers.update({"Content-Type": "application/json"})
# Configuration optimisée pour llama.cpp local
adapter = requests.adapters.HTTPAdapter(
    pool_connections=1,  # Une seule connexion au serveur local
    pool_maxsize=1,      # Pool minimal pour localhost
    max_retries=3        # Retry automatique
)
http_session.mount('http://', adapter)
http_session.mount('https://', adapter)

# ============================================================================
# FONCTIONS MÉMOIRE THERMIQUE
# ============================================================================

def load_thermal_memory():
    """Charge la mémoire thermique"""
    try:
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('conversations', [])
        return []
    except Exception as e:
        print(f"Erreur chargement mémoire: {e}")
        return []

def save_to_thermal_memory(user_message, agent_response, agent_type):
    """Sauvegarde dans la mémoire thermique avec monitoring détaillé"""
    try:
        memory = {"conversations": load_thermal_memory()}

        # Ajouter la conversation avec métadonnées détaillées
        timestamp = time.strftime("%Y-%m-%dT%H:%M:%S.000Z")

        # Analyse des données pour le monitoring
        user_keywords = user_message.lower().split()[:10]
        response_keywords = agent_response.lower().split()[:10]

        # Calcul des métriques
        user_length = len(user_message)
        response_length = len(agent_response)
        complexity_score = len(set(user_keywords)) / max(len(user_keywords), 1)

        memory["conversations"].extend([
            {
                "id": int(time.time() * 1000),
                "timestamp": timestamp,
                "sender": "user",
                "content": user_message,
                "agent": agent_type,
                "keywords": user_keywords,
                "length": user_length,
                "complexity": complexity_score,
                "thermal_zone": "input_processing"
            },
            {
                "id": int(time.time() * 1000) + 1,
                "timestamp": timestamp,
                "sender": agent_type,
                "content": agent_response,
                "agent": agent_type,
                "keywords": response_keywords,
                "length": response_length,
                "thermal_zone": f"{agent_type}_output",
                "processing_time": time.time()
            }
        ])

        # Métadonnées de la mémoire thermique
        memory["lastUpdate"] = timestamp
        memory["totalEntries"] = len(memory["conversations"])
        memory["thermal_stats"] = {
            "active_zones": ["input_processing", f"{agent_type}_output", "keyword_indexing"],
            "last_agent": agent_type,
            "avg_complexity": complexity_score,
            "total_keywords": len(user_keywords) + len(response_keywords),
            "memory_size_mb": len(json.dumps(memory)) / 1024 / 1024
        }

        # Sauvegarder dans le fichier
        with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(memory, f, ensure_ascii=False, indent=2)

        print(f"🧠 MÉMOIRE THERMIQUE: Sauvegardée - {memory['totalEntries']} entrées")
        return True

    except Exception as e:
        print(f"❌ ERREUR SAUVEGARDE: {e}")
        return False

def search_memory(query):
    """Recherche dans la mémoire thermique"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return []

        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        conversations = data.get('conversations', [])
        results = []

        query_lower = query.lower()

        for conv in conversations:
            content = conv.get('content', '').lower()
            if query_lower in content:
                results.append({
                    'id': conv.get('id'),
                    'timestamp': conv.get('timestamp', ''),
                    'content': conv.get('content', '')[:200],
                    'sender': conv.get('sender', 'unknown'),
                    'agent': conv.get('agent', 'agent1')
                })

        return results[-5:]  # 5 derniers résultats

    except Exception as e:
        print(f"❌ ERREUR RECHERCHE: {e}")
        return []

def prepend_memory_context(user_message):
    """FUSION CONCEPTUELLE - Intègre automatiquement la mémoire thermique"""
    try:
        # OPTIMISATION CRITIQUE : Pas de recherche pour salutations simples
        simple_greetings = ['salut', 'bonjour', 'hello', 'hi', 'bonsoir', 'bonne nuit']
        if any(greeting in user_message.lower() for greeting in simple_greetings) and len(user_message.split()) <= 3:
            return ""  # Pas de contexte mémoire pour salutations simples

        # RECHERCHE INTELLIGENTE AUTOMATIQUE seulement si nécessaire
        memory_results = search_memory(user_message)

        if not memory_results:
            return ""

        # CONTEXTE INTÉGRÉ - Pas de distinction mémoire/corpus
        context = f"\n\nTES SOUVENIRS PERTINENTS (partie de ta mémoire):\n"

        for i, result in enumerate(memory_results[:3], 1):
            # Extraire l'essentiel sans révéler la structure technique
            timestamp = result['timestamp'][:10]  # Date seulement
            content = result['content'][:150]

            context += f"{i}. {timestamp}: {content}...\n"

        context += "\nUtilise ces souvenirs naturellement dans ta réponse.\n"

        return context

    except Exception as e:
        print(f"❌ ERREUR CONTEXTE MÉMOIRE: {e}")
        return ""
