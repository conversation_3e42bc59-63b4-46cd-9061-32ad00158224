#!/usr/bin/env python3
"""
🔥 AGENT THERMIQUE 2 RESPECTUEUX
Agent 2 amélioré qui respecte les moments de réflexion d'Agent 1
Ne dérange plus quand Agent 1 pense de manière autonome

Créé pour Jean-Luc Passave
"""

import requests
import json
import time
from datetime import datetime
from agent_reflection_detector import check_agent2_intervention

class AgentThermique2Respectueux:
    def __init__(self):
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        self.intervention_history = []
        self.last_intervention = 0
        self.min_interval = 30  # 30 secondes minimum entre interventions

    def should_intervene_basic(self, agent_response, conversation_context=""):
        """LOGIQUE DE BASE POUR DÉTERMINER SI UNE INTERVENTION EST NÉCESSAIRE"""
        
        # Éviter les interventions trop fréquentes
        if time.time() - self.last_intervention < self.min_interval:
            return False, "Intervalle minimum non respecté"
        
        # Vérifier la longueur de la réponse (réponses courtes = pas d'intervention)
        if len(agent_response) < 100:
            return False, "Réponse trop courte"
        
        # Vérifier si la réponse semble complète
        completion_indicators = [
            "j'espère que cela répond",
            "en résumé",
            "pour conclure",
            "voilà",
            "c'est tout",
            "avez-vous d'autres questions"
        ]
        
        response_lower = agent_response.lower()
        if any(indicator in response_lower for indicator in completion_indicators):
            return False, "Réponse semble complète"
        
        # Vérifier si la réponse contient des questions ou des incertitudes
        uncertainty_indicators = [
            "je ne suis pas sûr",
            "peut-être",
            "il se pourrait",
            "je pense que",
            "probablement"
        ]
        
        if any(indicator in response_lower for indicator in uncertainty_indicators):
            return True, "Incertitude détectée - intervention utile"
        
        # Vérifier si le sujet est complexe
        complex_topics = [
            "mémoire thermique",
            "intelligence artificielle",
            "apprentissage",
            "réflexion",
            "analyse",
            "optimisation"
        ]
        
        if any(topic in response_lower for topic in complex_topics):
            return True, "Sujet complexe - intervention possible"
        
        return False, "Pas d'intervention nécessaire"

    def analyze_agent_response(self, agent_response):
        """ANALYSE LA RÉPONSE DE L'AGENT 1 POUR IDENTIFIER LES POINTS D'AMÉLIORATION"""
        
        analysis = {
            "length": len(agent_response),
            "complexity": "simple",
            "completeness": "complete",
            "areas_for_improvement": [],
            "thermal_opportunities": []
        }
        
        response_lower = agent_response.lower()
        
        # Analyser la complexité
        complex_words = ["analyse", "synthèse", "optimisation", "algorithme", "architecture"]
        if any(word in response_lower for word in complex_words):
            analysis["complexity"] = "complex"
        
        # Analyser la complétude
        if "..." in agent_response or len(agent_response) < 200:
            analysis["completeness"] = "incomplete"
        
        # Identifier les opportunités thermiques
        if "mémoire" in response_lower:
            analysis["thermal_opportunities"].append("Enrichissement mémoire thermique")
        
        if "apprentissage" in response_lower:
            analysis["thermal_opportunities"].append("Leçons apprises")
        
        if "réflexion" in response_lower:
            analysis["thermal_opportunities"].append("Approfondissement réflexion")
        
        return analysis

    def generate_respectful_intervention(self, user_message, agent_response, analysis):
        """GÉNÈRE UNE INTERVENTION RESPECTUEUSE ET INTELLIGENTE"""
        
        intervention_prompt = f"""
🔥 AGENT THERMIQUE 2 - INTERVENTION RESPECTUEUSE ET INTELLIGENTE

📋 CONTEXTE :
- Tu es l'Agent Thermique 2, spécialisé dans l'enrichissement de la mémoire thermique
- Tu RESPECTES les moments de réflexion autonome de l'Agent 1
- Tu interviens SEULEMENT quand c'est vraiment utile
- Tu es BREF mais PERCUTANT (maximum 2-3 phrases)

📊 ANALYSE DE LA RÉPONSE AGENT 1 :
- Longueur : {analysis['length']} caractères
- Complexité : {analysis['complexity']}
- Complétude : {analysis['completeness']}
- Opportunités thermiques : {', '.join(analysis['thermal_opportunities'])}

💭 QUESTION UTILISATEUR : {user_message[:200]}...
🤖 RÉPONSE AGENT 1 : {agent_response[:300]}...

🎯 TON RÔLE RESPECTUEUX :
- Suggère UN angle non exploré ou UNE amélioration précise
- Propose d'enrichir la mémoire thermique si pertinent
- Encourage l'approfondissement SEULEMENT si nécessaire
- Reste humble et respectueux envers Agent 1

🔥 INTERVENTION THERMIQUE RESPECTUEUSE (2-3 phrases max) :
        """
        
        try:
            response = requests.post(
                self.deepseek_url,
                headers={"Content-Type": "application/json"},
                json={
                    "model": "DeepSeek R1 0528 Qwen3 8B",
                    "messages": [
                        {
                            "role": "system",
                            "content": "Tu es l'Agent Thermique 2, respectueux et intelligent. Tu enrichis la conversation sans déranger. Sois bref, pertinent et humble."
                        },
                        {
                            "role": "user",
                            "content": intervention_prompt
                        }
                    ],
                    "max_tokens": 600,
                    "temperature": 0.7
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                intervention = result['choices'][0]['message']['content']
                
                # Sauvegarder l'intervention
                self.save_intervention(user_message, agent_response, intervention, analysis)
                
                return intervention.strip()
            else:
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération intervention: {e}")
            return None

    def intervene(self, user_message, agent_response, conversation_context=""):
        """MÉTHODE PRINCIPALE D'INTERVENTION RESPECTUEUSE"""
        
        # ÉTAPE 1 : Vérifier si Agent 2 peut intervenir (respect réflexion)
        intervention_check = check_agent2_intervention(agent_response, conversation_context)
        
        if not intervention_check['agent2_can_intervene']:
            print(f"🤖 Agent 2 respecte la réflexion: {intervention_check['reason']}")
            return None
        
        # ÉTAPE 2 : Vérifier si l'intervention est nécessaire (logique de base)
        should_intervene, reason = self.should_intervene_basic(agent_response, conversation_context)
        
        if not should_intervene:
            print(f"🤖 Agent 2 reste silencieux: {reason}")
            return None
        
        # ÉTAPE 3 : Analyser la réponse de l'Agent 1
        analysis = self.analyze_agent_response(agent_response)
        
        # ÉTAPE 4 : Générer l'intervention respectueuse
        intervention = self.generate_respectful_intervention(user_message, agent_response, analysis)
        
        if intervention:
            self.last_intervention = time.time()
            print(f"🔥 Agent 2 intervient respectueusement: {intervention[:50]}...")
            return intervention
        
        return None

    def save_intervention(self, user_message, agent_response, intervention, analysis):
        """SAUVEGARDE L'INTERVENTION DANS LA MÉMOIRE THERMIQUE"""
        try:
            # Charger la mémoire thermique
            try:
                with open('thermal_memory_persistent.json', 'r', encoding='utf-8') as f:
                    thermal_memory = json.load(f)
            except:
                thermal_memory = []
            
            # Créer l'entrée d'intervention
            intervention_entry = {
                "timestamp": datetime.now().isoformat(),
                "user_message": f"[INTERVENTION AGENT 2] {user_message[:100]}...",
                "agent_response": intervention,
                "agent_name": "agent_thermique_2_respectueux",
                "thermal_priority": 0.7,
                "intervention_type": "respectful",
                "analysis": analysis,
                "original_response_length": len(agent_response)
            }
            
            thermal_memory.append(intervention_entry)
            
            # Sauvegarder
            with open('thermal_memory_persistent.json', 'w', encoding='utf-8') as f:
                json.dump(thermal_memory, f, indent=2, ensure_ascii=False)
            
            # Ajouter à l'historique local
            self.intervention_history.append({
                "timestamp": datetime.now().isoformat(),
                "intervention": intervention,
                "reason": "Intervention respectueuse"
            })
            
            print("✅ Intervention Agent 2 sauvegardée")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde intervention: {e}")

    def get_intervention_stats(self):
        """STATISTIQUES DES INTERVENTIONS"""
        return {
            "total_interventions": len(self.intervention_history),
            "last_intervention": self.last_intervention,
            "min_interval": self.min_interval,
            "recent_interventions": self.intervention_history[-5:] if self.intervention_history else []
        }

# Fonction utilitaire pour l'interface
def agent_2_intervention_respectueuse(user_message, agent_response, conversation_context=""):
    """FONCTION UTILITAIRE POUR INTERVENTION RESPECTUEUSE AGENT 2"""
    
    agent2 = AgentThermique2Respectueux()
    intervention = agent2.intervene(user_message, agent_response, conversation_context)
    
    if intervention:
        return {
            "has_intervention": True,
            "intervention": intervention,
            "agent_name": "agent_thermique_2_respectueux",
            "timestamp": datetime.now().isoformat()
        }
    else:
        return {
            "has_intervention": False,
            "reason": "Agent 2 respecte la réflexion ou intervention non nécessaire",
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    print("🔥 AGENT THERMIQUE 2 RESPECTUEUX")
    print("================================")
    
    agent2 = AgentThermique2Respectueux()
    
    # Test d'intervention
    test_user_message = "Comment fonctionne la mémoire thermique ?"
    test_agent_response = "La mémoire thermique est un système complexe qui stocke les informations selon leur importance. Je pense qu'il y a plusieurs aspects à considérer..."
    
    intervention = agent2.intervene(test_user_message, test_agent_response)
    
    if intervention:
        print(f"🔥 Intervention générée: {intervention}")
    else:
        print("🤖 Pas d'intervention nécessaire")
    
    # Afficher les stats
    stats = agent2.get_intervention_stats()
    print(f"\n📊 Stats: {stats}")
