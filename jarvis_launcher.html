<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 JARVIS DeepSeek R1 8B TURBO - Launcher</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #2d2d5f 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 3em;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #0099cc, #0066ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2em;
            color: #cccccc;
            margin-bottom: 5px;
        }

        .version {
            font-size: 0.9em;
            color: #888888;
        }

        .main-content {
            display: flex;
            flex: 1;
            gap: 30px;
        }

        .control-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #00d4ff;
            border-bottom: 2px solid #00d4ff;
            padding-bottom: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px 0;
            width: 100%;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }

        .btn:disabled {
            background: #666666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-stop {
            background: linear-gradient(45deg, #ff4444, #cc0000);
        }

        .btn-stop:hover {
            box-shadow: 0 5px 15px rgba(255, 68, 68, 0.4);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        .status-running {
            background: #00ff00;
        }

        .status-stopped {
            background: #ff4444;
        }

        .status-starting {
            background: #ffaa00;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin-top: 20px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #00d4ff;
            padding-left: 10px;
        }

        .log-error {
            border-left-color: #ff4444;
            color: #ffaaaa;
        }

        .features {
            margin-top: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .feature-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }

        .quick-links {
            margin-top: 20px;
        }

        .link-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #00d4ff;
            padding: 10px 20px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
            text-align: left;
            transition: all 0.3s ease;
        }

        .link-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤖 JARVIS</div>
            <div class="subtitle">DeepSeek R1 8B avec Mémoire Thermique</div>
            <div class="version">Version 2.0.0 - Créé avec amour par Claude pour Jean-Luc Passave</div>
        </div>

        <div class="main-content">
            <div class="control-panel">
                <h2 class="panel-title">🎛️ Contrôle JARVIS</h2>
                
                <button id="startBtn" class="btn">🚀 Démarrer JARVIS</button>
                <button id="stopBtn" class="btn btn-stop" disabled>🛑 Arrêter JARVIS</button>
                <button id="openBtn" class="btn" disabled>🌐 Ouvrir Interface JARVIS</button>

                <div class="features">
                    <h3 style="color: #00d4ff; margin-bottom: 15px;">✨ Fonctionnalités</h3>
                    <div class="feature-item">
                        <span class="feature-icon">🧠</span>
                        <span>Mémoire Thermique Continue</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🤖</span>
                        <span>Agent DeepSeek R1 8B</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">💾</span>
                        <span>Sauvegarde Automatique</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🎛️</span>
                        <span>Contrôle Multi-Agents</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🌐</span>
                        <span>Interface Gradio Complète</span>
                    </div>
                </div>

                <div class="quick-links">
                    <h3 style="color: #00d4ff; margin-bottom: 15px;">🔗 Liens Rapides</h3>
                    <button class="link-btn" onclick="openLogs()">📋 Voir les Logs</button>
                    <button class="link-btn" onclick="openMemory()">🧠 Mémoire Thermique</button>
                    <button class="link-btn" onclick="openFolder()">📁 Dossier JARVIS</button>
                </div>
            </div>

            <div class="status-panel">
                <h2 class="panel-title">📊 État du Système</h2>
                
                <div class="status-indicator">
                    <div id="jarvisStatus" class="status-dot status-stopped"></div>
                    <span id="jarvisStatusText">JARVIS: Arrêté</span>
                </div>

                <div class="status-indicator">
                    <div id="deepseekStatus" class="status-dot status-stopped"></div>
                    <span id="deepseekStatusText">DeepSeek R1 8B: Arrêté</span>
                </div>

                <div class="status-indicator">
                    <div id="memoryStatus" class="status-dot status-stopped"></div>
                    <span id="memoryStatusText">Mémoire Thermique: Inactive</span>
                </div>

                <div class="log-container" id="logContainer">
                    <div class="log-entry">🚀 JARVIS Launcher prêt</div>
                    <div class="log-entry">💡 Cliquez sur "Démarrer JARVIS" pour commencer</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        // Éléments DOM
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const openBtn = document.getElementById('openBtn');
        const jarvisStatus = document.getElementById('jarvisStatus');
        const jarvisStatusText = document.getElementById('jarvisStatusText');
        const logContainer = document.getElementById('logContainer');

        // État initial
        let isRunning = false;

        // Gestionnaires d'événements
        startBtn.addEventListener('click', async () => {
            addLog('🚀 Démarrage de JARVIS...');
            try {
                await ipcRenderer.invoke('start-jarvis');
            } catch (error) {
                addLog(`❌ Erreur démarrage: ${error.message}`, true);
            }
        });

        stopBtn.addEventListener('click', async () => {
            addLog('🛑 Arrêt de JARVIS...');
            try {
                await ipcRenderer.invoke('stop-jarvis');
            } catch (error) {
                addLog(`❌ Erreur arrêt: ${error.message}`, true);
            }
        });

        openBtn.addEventListener('click', async () => {
            try {
                await ipcRenderer.invoke('open-jarvis');
            } catch (error) {
                addLog(`❌ Erreur ouverture: ${error.message}`, true);
            }
        });

        // Écouter les événements IPC
        ipcRenderer.on('jarvis-starting', () => {
            updateStatus('starting');
            addLog('⏳ JARVIS en cours de démarrage...');
        });

        ipcRenderer.on('jarvis-running', (event, url) => {
            updateStatus('running');
            addLog(`✅ JARVIS opérationnel sur ${url}`);
        });

        ipcRenderer.on('jarvis-stopped', () => {
            updateStatus('stopped');
            addLog('🛑 JARVIS arrêté');
        });

        ipcRenderer.on('jarvis-log', (event, data) => {
            addLog(data, false);
        });

        ipcRenderer.on('jarvis-error', (event, data) => {
            addLog(data, true);
        });

        // Fonctions utilitaires
        function updateStatus(status) {
            const statusClasses = {
                'running': 'status-running',
                'stopped': 'status-stopped',
                'starting': 'status-starting'
            };

            const statusTexts = {
                'running': 'JARVIS: Opérationnel ✅',
                'stopped': 'JARVIS: Arrêté ❌',
                'starting': 'JARVIS: Démarrage... ⏳'
            };

            jarvisStatus.className = `status-dot ${statusClasses[status]}`;
            jarvisStatusText.textContent = statusTexts[status];

            isRunning = status === 'running';
            startBtn.disabled = isRunning || status === 'starting';
            stopBtn.disabled = !isRunning && status !== 'starting';
            openBtn.disabled = !isRunning;
        }

        function addLog(message, isError = false) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${isError ? 'log-error' : ''}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // Limiter le nombre de logs
            if (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // Fonctions pour les liens rapides
        async function openLogs() {
            try {
                await ipcRenderer.invoke('open-logs');
                addLog('📋 Ouverture des logs...');
            } catch (error) {
                addLog(`❌ Erreur ouverture logs: ${error.message}`, true);
            }
        }

        async function openMemory() {
            try {
                await ipcRenderer.invoke('open-memory');
                addLog('🧠 Ouverture mémoire thermique...');
            } catch (error) {
                addLog(`❌ Erreur ouverture mémoire: ${error.message}`, true);
            }
        }

        async function openFolder() {
            try {
                await ipcRenderer.invoke('open-folder');
                addLog('📁 Ouverture dossier JARVIS...');
            } catch (error) {
                addLog(`❌ Erreur ouverture dossier: ${error.message}`, true);
            }
        }

        // Vérifier le statut au chargement
        window.addEventListener('load', async () => {
            try {
                await ipcRenderer.invoke('check-status');
                addLog('🎯 Interface de contrôle JARVIS chargée');
            } catch (error) {
                addLog(`⚠️ Erreur vérification statut: ${error.message}`, true);
            }
        });
    </script>
</body>
</html>
