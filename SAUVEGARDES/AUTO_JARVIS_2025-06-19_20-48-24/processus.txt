jean-luc         49912   0.3  0.5 413495920  78832 s196  S+    4:44PM   1:38.19 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python jarvis_interface_propre.py
jean-luc         68475   0.3  0.3 413587568  43856 s019  S     6:55AM   2:50.15 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python jarvis_interface_propre.py
jean-luc         10774   0.0  0.2 413255424  36816 s019  S+   10:22AM   0:09.22 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012import sys\012sys.path.append('.')\012from jarvis_interface_propre import update_interface_stats, scan_all_buttons_and_connect, analyze_document_background, check_analysis_status\012\012print('🔍 TEST 1: Mise à jour stats')\012try:\012    result = update_interface_stats()\012    print('-'^E update_interface_stats() fonctionne')\012    print(f'📊 Longueur résultat: {len(result)} caractères')\012except Exception as e:\012    print(f'-'-L Erreur update_interface_stats: {e}')\012\012print('\n🔍 TEST 2: Scanner boutons')\012try:\012    result = scan_all_buttons_and_connect()\012    print('-'^E scan_all_buttons_and_connect() fonctionne')\012    print(f'📊 Longueur résultat: {len(result)} caractères')\012except Exception as e:\012    print(f'-'-L Erreur scan_all_buttons_and_connect: {e}')\012\012print('\n🔍 TEST 3: Analyse document')\012try:\012    result = analyze_document_background('print("test")', 'test.py')\012    print('-'^E analyze_document_background() fonctionne')\012    print(f'📊 Résultat: {result}')\012except Exception as e:\012    print(f'-'-L Erreur analyze_document_background: {e}')\012\012print('\n🔍 TEST 4: Vérifier statut analyse')\012try:\012    result = check_analysis_status()\012    print('-'^E check_analysis_status() fonctionne')\012    print(f'📊 Longueur résultat: {len(result)} caractères')\012except Exception as e:\012    print(f'-'-L Erreur check_analysis_status: {e}')\012\012print('\n🎉 TOUS LES TESTS TERMINÉS')\012
jean-luc         82447   0.0  0.2 413403200  32640 s153  S+    8:03AM   0:12.29 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python jarvis_interface_propre.py
jean-luc         68471   0.0  0.0 410213632    384 s019  S     6:55AM   0:00.00 bash -c cd /Volumes/seagate/Louna_Electron_Latest && source venv_deepseek/bin/activate && python jarvis_interface_propre.py
jean-luc         60519   0.0 29.0 416963392 4862480 s019  S     6:18AM  52:56.23 llama-server --model /Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf --host 0.0.0.0 --port 8000 --ctx-size 4096 --threads 8 --n-gpu-layers 32 --batch-size 1024 --ubatch-size 256 --n-predict 2048 --timeout 120 --flash-attn --mlock --verbose
jean-luc         96730   0.0  0.0 410059184    272   ??  R     8:48PM   0:00.00 grep -E (llama-server|python.*jarvis)
