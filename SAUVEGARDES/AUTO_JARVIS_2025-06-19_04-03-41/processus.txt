jean-luc         31752   6.2  4.4 424650816 742032 s091  U+    3:44AM   2:19.38 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python jarvis_interface_propre.py
jean-luc         31992   5.7  4.0 424524688 677024 s093  U+    3:45AM   2:30.63 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python test_jarvis_status.py
jean-luc         27546   0.0  0.0 411179760   2288 s079  S+    3:21AM   0:00.12 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012from jarvis_verification_neurones import verifier_neurones_complets_jarvis\012\012print('🔍 TEST VÉRIFICATION NEURONES COMPLETS')\012print('=' * 50)\012\012result = verifier_neurones_complets_jarvis()\012print('-'^E Vérification neurones complète testée')\012\012print('\n🎯 VÉRIFICATION NEURONES OPÉRATIONNELLE !')\012
jean-luc         26957   0.0  0.0 411179760   2288 s077  S+    3:17AM   0:00.14 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012from jarvis_systeme_neuronal_etages import activer_systeme_neuronal_jarvis\012\012print('^AM-yM-` TEST SYSTÈME NEURONAL PAR ÉTAGES')\012print('=' * 50)\012\012result = activer_systeme_neuronal_jarvis()\012print('-'^E Système neuronal par étages testé')\012\012print('\n🎯 SYSTÈME NEURONAL OPÉRATIONNEL !')\012
jean-luc         25669   0.0  0.0 411064208   2288 s019  S+    3:10AM   0:00.11 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012from jarvis_accelerateur_global_unifie import activer_acceleration_globale_jarvis\012\012print('🚀 TEST ACCÉLÉRATEUR GLOBAL UNIFIÉ')\012print('=' * 50)\012\012result = activer_acceleration_globale_jarvis()\012print('-'^E Accélérateur global unifié testé')\012\012print('\n🎯 ACCÉLÉRATEUR GLOBAL OPÉRATIONNEL !')\012
jean-luc         23924   0.0 26.5 416496768 4443152 s073  S+    3:00AM   0:07.98 llama-server --model /Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf --host 0.0.0.0 --port 8000 --ctx-size 1024 --threads 4 --n-gpu-layers 28 --batch-size 512 --ubatch-size 128 --n-predict 256 --timeout 30 --temp 0.3 --top-p 0.8 --repeat-penalty 1.2 --mlock --cont-batching --parallel 2 --verbose
jean-luc         35045   0.0  0.0 410225920   1376   ??  U     4:03AM   0:00.00 grep -E (llama-server|python.*jarvis)
