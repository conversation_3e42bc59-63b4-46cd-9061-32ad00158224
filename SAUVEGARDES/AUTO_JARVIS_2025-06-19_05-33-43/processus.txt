jean-luc         50792   0.0  0.5 413310240  79808 s119  S+    5:28AM   0:03.11 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c import jarvis_interface_propre
jean-luc         47193   0.0  0.0 411025360   3520 s111  S+    5:11AM   0:00.20 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python test_complet_jarvis.py
jean-luc         46888   0.0 26.5 416563328 4450112 s109  S+    5:09AM   2:38.23 llama-server --model /Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf --host 0.0.0.0 --port 8000 --ctx-size 1024 --threads 4 --n-gpu-layers 28 --batch-size 512 --ubatch-size 128 --n-predict 256 --timeout 30 --temp 0.3 --top-p 0.8 --repeat-penalty 1.2 --mlock --cont-batching --parallel 2 --verbose
jean-luc         25669   0.0  0.0 411064208   1744 s019  S+    3:10AM   0:00.19 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012from jarvis_accelerateur_global_unifie import activer_acceleration_globale_jarvis\012\012print('🚀 TEST ACCÉLÉRATEUR GLOBAL UNIFIÉ')\012print('=' * 50)\012\012result = activer_acceleration_globale_jarvis()\012print('-'^E Accélérateur global unifié testé')\012\012print('\n🎯 ACCÉLÉRATEUR GLOBAL OPÉRATIONNEL !')\012
jean-luc         51870   0.0  0.0 410219248   1408   ??  S     5:33AM   0:00.00 grep -E (llama-server|python.*jarvis)
