jean-<PERSON><PERSON>         68475   0.4  0.1 413323744  10672 s019  S     6:55AM   3:50.79 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python jarvis_interface_propre.py
jean-luc         10774   0.0  0.0 412731152   4768 s019  S+   10:22AM   0:12.86 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012import sys\012sys.path.append('.')\012from jarvis_interface_propre import update_interface_stats, scan_all_buttons_and_connect, analyze_document_background, check_analysis_status\012\012print('🔍 TEST 1: Mise à jour stats')\012try:\012    result = update_interface_stats()\012    print('-'^E update_interface_stats() fonctionne')\012    print(f'📊 Longueur résultat: {len(result)} caractères')\012except Exception as e:\012    print(f'-'-L Erreur update_interface_stats: {e}')\012\012print('\n🔍 TEST 2: Scanner boutons')\012try:\012    result = scan_all_buttons_and_connect()\012    print('-'^E scan_all_buttons_and_connect() fonctionne')\012    print(f'📊 Longueur résultat: {len(result)} caractères')\012except Exception as e:\012    print(f'-'-L Erreur scan_all_buttons_and_connect: {e}')\012\012print('\n🔍 TEST 3: Analyse document')\012try:\012    result = analyze_document_background('print("test")', 'test.py')\012    print('-'^E analyze_document_background() fonctionne')\012    print(f'📊 Résultat: {result}')\012except Exception as e:\012    print(f'-'-L Erreur analyze_document_background: {e}')\012\012print('\n🔍 TEST 4: Vérifier statut analyse')\012try:\012    result = check_analysis_status()\012    print('-'^E check_analysis_status() fonctionne')\012    print(f'📊 Longueur résultat: {len(result)} caractères')\012except Exception as e:\012    print(f'-'-L Erreur check_analysis_status: {e}')\012\012print('\n🎉 TOUS LES TESTS TERMINÉS')\012
jean-luc         82447   0.0  0.0 413010000   4800 s153  S+    8:03AM   0:16.19 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python jarvis_interface_propre.py
jean-luc         68471   0.0  0.0 410213632    368 s019  S     6:55AM   0:00.00 bash -c cd /Volumes/seagate/Louna_Electron_Latest && source venv_deepseek/bin/activate && python jarvis_interface_propre.py
jean-luc         60519   0.0 29.0 416962832 4860032 s019  S     6:18AM  64:20.70 llama-server --model /Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf --host 0.0.0.0 --port 8000 --ctx-size 4096 --threads 8 --n-gpu-layers 32 --batch-size 1024 --ubatch-size 256 --n-predict 2048 --timeout 120 --flash-attn --mlock --verbose
jean-luc         56964   0.0  0.0 410054784    400   ??  R     1:48AM   0:00.00 grep -E (llama-server|python.*jarvis)
jean-luc         56514   0.0  0.5 413483616  88112 s220  S+    1:46AM   0:03.73 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012import sys\012sys.path.append('.')\012from jarvis_interface_propre import execute_universal_code\012print('🔍 TEST EXÉCUTEUR CODE UNIVERSEL:')\012\012# Test Python\012result = execute_universal_code('print("Hello JARVIS!")', 'python')\012print('-'^E Python:', result['success'], '- Sortie:', result.get('output', '').strip())\012\012# Test JavaScript\012result = execute_universal_code('console.log("Hello JARVIS!");', 'javascript')\012print('-'^E JavaScript:', result['success'], '- Sortie:', result.get('output', '').strip())\012\012# Test JSON\012result = execute_universal_code('{"message": "Hello JARVIS!"}', 'json')\012print('-'^E JSON:', result['success'], '- Sortie:', result.get('output', '').strip())\012\012print('💻 Exécuteur de code fonctionne!')\012
jean-luc         56327   0.0  0.2 413315040  36112 s218  S+    1:45AM   0:03.99 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012import sys\012sys.path.append('.')\012from jarvis_interface_propre import activate_whatsapp_integration\012print('�� TEST BOUTON WHATSAPP:')\012result = activate_whatsapp_integration()\012print('-'^E Résultat:', len(result), 'caractères')\012print('📱 WhatsApp fonctionne!')\012
jean-luc         56029   0.0  0.2 413304432  34624 s217  S+    1:44AM   0:04.68 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python correcteur_final_complet_jarvis.py
