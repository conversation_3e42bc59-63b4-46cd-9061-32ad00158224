#!/usr/bin/env python3
"""
📊 VISUALISEUR FEUILLE DE SUIVI JARVIS
Interface pour consulter l'activité heure par heure
Créé pour Jean-Luc Passave
"""

import json
import os
from datetime import datetime, timedelta
import argparse

class VisualiseurSuiviJarvis:
    def __init__(self):
        self.suivi_file = "jarvis_feuille_suivi_24h.json"
    
    def charger_donnees(self):
        """Charger les données de suivi"""
        if not os.path.exists(self.suivi_file):
            print("❌ Aucune feuille de suivi trouvée")
            return None
        
        try:
            with open(self.suivi_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Erreur lecture feuille de suivi: {e}")
            return None
    
    def afficher_resume_global(self):
        """Afficher le résumé global"""
        data = self.charger_donnees()
        if not data:
            return
        
        print("📊 ================================")
        print("🤖 RÉSUMÉ GLOBAL JARVIS")
        print("📊 ================================")
        
        # Informations générales
        creation = data.get("date_creation", "")
        derniere_maj = data.get("derniere_mise_a_jour", "")
        mode_sommeil = data.get("mode_sommeil_actif", False)
        
        print(f"📅 Création: {creation.split('T')[0] if 'T' in creation else creation}")
        print(f"🔄 Dernière MAJ: {derniere_maj.split('T')[1][:8] if 'T' in derniere_maj else derniere_maj}")
        print(f"😴 Mode sommeil: {'🟢 ACTIF' if mode_sommeil else '🔴 INACTIF'}")
        
        # Statistiques
        stats = data.get("statistiques_globales", {})
        print(f"\n📈 STATISTIQUES:")
        print(f"  🔄 Réveils: {stats.get('nombre_reveils', 0)}")
        print(f"  😴 Endormissements: {stats.get('nombre_endormissements', 0)}")
        print(f"  ⏱️ Temps actif: {stats.get('temps_total_actif', 0)} min")
        print(f"  💤 Temps sommeil: {stats.get('temps_total_sommeil', 0)} min")
        
        # Nombre de jours suivis
        jours_suivis = len(data.get("suivi_par_jour", {}))
        print(f"  📅 Jours suivis: {jours_suivis}")
        
        print("\n" + "="*50)
    
    def afficher_activite_jour(self, date=None):
        """Afficher l'activité d'un jour spécifique"""
        data = self.charger_donnees()
        if not data:
            return
        
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")
        
        suivi_jour = data.get("suivi_par_jour", {}).get(date)
        if not suivi_jour:
            print(f"❌ Aucune donnée pour le {date}")
            return
        
        print(f"📅 ================================")
        print(f"🤖 ACTIVITÉ JARVIS - {date}")
        print(f"📅 ================================")
        
        # Résumé de la journée
        resume = suivi_jour.get("resume_journee", {})
        debut = resume.get("debut_activite", "")
        fin = resume.get("fin_activite", "")
        nb_activites = resume.get("nombre_activites", 0)
        
        print(f"🌅 Début activité: {debut.split('T')[1][:8] if 'T' in debut else debut}")
        print(f"🌙 Fin activité: {fin.split('T')[1][:8] if 'T' in fin else fin}")
        print(f"📊 Nombre d'activités: {nb_activites}")
        
        # Activités par heure
        print(f"\n⏰ ACTIVITÉS HEURE PAR HEURE:")
        activites_par_heure = suivi_jour.get("activites_par_heure", {})
        
        for heure in sorted(activites_par_heure.keys()):
            activites = activites_par_heure[heure]
            print(f"\n🕐 {heure}:00 ({len(activites)} activités)")
            
            for activite in activites:
                timestamp = activite["timestamp"].split("T")[1][:8]
                type_act = activite["type"]
                description = activite["description"]
                mode = "😴" if activite.get("mode_sommeil") else "🔍"
                
                # Raccourcir la description si trop longue
                if len(description) > 60:
                    description = description[:57] + "..."
                
                print(f"    {timestamp} {mode} {type_act}: {description}")
        
        print("\n" + "="*50)
    
    def afficher_activites_recentes(self, nombre=20):
        """Afficher les activités récentes"""
        data = self.charger_donnees()
        if not data:
            return
        
        print(f"⏰ ================================")
        print(f"🤖 {nombre} ACTIVITÉS RÉCENTES")
        print(f"⏰ ================================")
        
        activites_recentes = data.get("activites_temps_reel", [])[-nombre:]
        
        if not activites_recentes:
            print("❌ Aucune activité récente")
            return
        
        for i, activite in enumerate(reversed(activites_recentes), 1):
            timestamp = activite["timestamp"]
            date_part = timestamp.split("T")[0]
            time_part = timestamp.split("T")[1][:8]
            
            type_act = activite["type"]
            description = activite["description"]
            mode = "😴" if activite.get("mode_sommeil") else "🔍"
            
            # Raccourcir la description
            if len(description) > 50:
                description = description[:47] + "..."
            
            print(f"{i:2d}. {date_part} {time_part} {mode} {type_act}")
            print(f"    {description}")
        
        print("\n" + "="*50)
    
    def afficher_alertes_systeme(self):
        """Afficher les alertes système"""
        data = self.charger_donnees()
        if not data:
            return
        
        alertes = data.get("alertes_systeme", [])
        
        print("🚨 ================================")
        print("🤖 ALERTES SYSTÈME")
        print("🚨 ================================")
        
        if not alertes:
            print("✅ Aucune alerte système")
        else:
            for i, alerte in enumerate(alertes, 1):
                timestamp = alerte.get("timestamp", "")
                type_alerte = alerte.get("type", "")
                message = alerte.get("message", "")
                
                print(f"{i}. {timestamp} - {type_alerte}")
                print(f"   {message}")
        
        print("\n" + "="*50)
    
    def generer_rapport_complet(self, date=None):
        """Générer un rapport complet"""
        print("📋 RAPPORT COMPLET JARVIS")
        print("=" * 50)
        
        self.afficher_resume_global()
        print()
        self.afficher_activite_jour(date)
        print()
        self.afficher_activites_recentes(10)
        print()
        self.afficher_alertes_systeme()
    
    def mode_interactif(self):
        """Mode interactif pour explorer les données"""
        while True:
            print("\n🤖 VISUALISEUR SUIVI JARVIS")
            print("=" * 30)
            print("1. 📊 Résumé global")
            print("2. 📅 Activité aujourd'hui")
            print("3. 📅 Activité date spécifique")
            print("4. ⏰ Activités récentes")
            print("5. 🚨 Alertes système")
            print("6. 📋 Rapport complet")
            print("7. 🚪 Quitter")
            
            choix = input("\n🔍 Votre choix (1-7): ").strip()
            
            if choix == "1":
                self.afficher_resume_global()
            elif choix == "2":
                self.afficher_activite_jour()
            elif choix == "3":
                date = input("📅 Date (YYYY-MM-DD): ").strip()
                self.afficher_activite_jour(date)
            elif choix == "4":
                try:
                    nb = int(input("📊 Nombre d'activités (défaut 20): ") or "20")
                    self.afficher_activites_recentes(nb)
                except ValueError:
                    self.afficher_activites_recentes(20)
            elif choix == "5":
                self.afficher_alertes_systeme()
            elif choix == "6":
                date = input("📅 Date pour rapport (YYYY-MM-DD, vide=aujourd'hui): ").strip()
                self.generer_rapport_complet(date if date else None)
            elif choix == "7":
                print("👋 Au revoir !")
                break
            else:
                print("❌ Choix invalide")

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Visualiseur feuille de suivi JARVIS")
    parser.add_argument("--resume", action="store_true", help="Afficher résumé global")
    parser.add_argument("--jour", type=str, help="Afficher activité d'un jour (YYYY-MM-DD)")
    parser.add_argument("--recent", type=int, default=20, help="Nombre d'activités récentes")
    parser.add_argument("--rapport", action="store_true", help="Rapport complet")
    parser.add_argument("--interactif", action="store_true", help="Mode interactif")
    
    args = parser.parse_args()
    
    visualiseur = VisualiseurSuiviJarvis()
    
    if args.resume:
        visualiseur.afficher_resume_global()
    elif args.jour:
        visualiseur.afficher_activite_jour(args.jour)
    elif args.rapport:
        visualiseur.generer_rapport_complet()
    elif args.interactif:
        visualiseur.mode_interactif()
    else:
        # Par défaut : activités récentes
        visualiseur.afficher_activites_recentes(args.recent)

if __name__ == "__main__":
    main()
