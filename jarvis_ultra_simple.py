#!/usr/bin/env python3
"""
🚀 JARVIS ULTRA SIMPLE - GARANTIE DE FONCTIONNEMENT
Interface minimaliste qui DOIT marcher
"""

import gradio as gr
import requests
import json

def chat_simple(message):
    """Chat ultra simple avec DeepSeek"""
    if not message:
        return "Tapez un message"
    
    try:
        payload = {
            "model": "deepseek-ai/DeepSeek-R1-0528",
            "messages": [
                {"role": "user", "content": message}
            ],
            "max_tokens": 300,
            "temperature": 0.7
        }
        
        response = requests.post(
            "http://localhost:8000/v1/chat/completions", 
            json=payload, 
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            return data['choices'][0]['message']['content']
        else:
            return f"Erreur {response.status_code}"
            
    except Exception as e:
        return f"Erreur: {str(e)}"

# Interface ultra simple
with gr.Blocks(title="JARVIS SIMPLE") as demo:
    gr.HTML("<h1>🤖 JARVIS ULTRA SIMPLE</h1>")
    
    with gr.Row():
        with gr.Column():
            message = gr.Textbox(label="Votre message", placeholder="Tapez ici...")
            send = gr.Button("Envoyer", variant="primary")
            
        with gr.Column():
            response = gr.Textbox(label="Réponse JARVIS", lines=10)
    
    send.click(chat_simple, inputs=message, outputs=response)
    message.submit(chat_simple, inputs=message, outputs=response)

if __name__ == "__main__":
    print("🚀 JARVIS ULTRA SIMPLE")
    print("URL: http://localhost:7864")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7864,
        share=False,
        quiet=False
    )
