#!/bin/bash

echo "🚀 LANCEMENT JARVIS CORRIGÉ - JEAN-LUC PASSAVE"
echo "=============================================="

cd "/Volumes/seagate/Louna_Electron_Latest"

echo "📍 Répertoire: $(pwd)"

# Activer l'environnement virtuel et lancer JARVIS
echo "🔧 Activation environnement virtuel..."
source venv_deepseek/bin/activate

echo "🧠 Lancement JARVIS avec corrections appliquées..."
echo "📱 Interface: http://localhost:7863"
echo "🔧 DeepSeek: http://localhost:8000"
echo ""
echo "💡 CORRECTIONS APPLIQUÉES:"
echo "   ✅ Affichage horizontal corrigé"
echo "   ✅ CSS anti-horizontal"
echo "   ✅ Imports nettoyés"
echo "   ✅ Configuration optimisée"
echo ""
echo "🌟 JARVIS EST PRÊT !"
echo "=============================================="

python jarvis_interface_propre.py
