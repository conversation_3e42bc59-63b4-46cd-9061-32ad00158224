#!/usr/bin/env python3
"""
🚀 JARVIS TURBO OPTIMIZER
Optimise automatiquement les performances de JARVIS
Accélérateurs qui s'installent et restent actifs
"""

import os
import time
import requests
import json
import subprocess
import psutil
from datetime import datetime

class JarvisTurboOptimizer:
    def __init__(self):
        self.api_url = "http://localhost:8000/v1/chat/completions"
        self.optimizations_applied = []
        self.performance_baseline = None
        
    def apply_turbo_optimizations(self):
        """Applique tous les accélérateurs turbo automatiquement"""
        optimizations = []
        
        # 1. Variables d'environnement turbo
        turbo_env = {
            'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512,expandable_segments:True',
            'VLLM_ATTENTION_BACKEND': 'FLASHINFER',
            'VLLM_USE_TRITON_FLASH_ATTN': '1',
            'VLLM_FLASH_ATTN_CHUNK_SIZE': '1',
            'VLLM_ENGINE_ITERATION_TIMEOUT_S': '15',
            'OMP_NUM_THREADS': '8',
            'MKL_NUM_THREADS': '8'
        }
        
        for key, value in turbo_env.items():
            os.environ[key] = value
            optimizations.append(f"✅ {key} = {value}")
        
        # 2. Optimisations système
        try:
            # Priorité processus haute
            current_process = psutil.Process()
            current_process.nice(-10)  # Priorité élevée
            optimizations.append("✅ Priorité processus élevée")
        except:
            optimizations.append("⚠️ Priorité processus (permissions limitées)")
        
        # 3. Cache optimisé
        cache_dir = os.path.expanduser("~/.cache/jarvis_turbo")
        os.makedirs(cache_dir, exist_ok=True)
        os.environ['JARVIS_CACHE_DIR'] = cache_dir
        optimizations.append(f"✅ Cache turbo: {cache_dir}")
        
        self.optimizations_applied = optimizations
        return optimizations
    
    def measure_response_time(self, test_message="Bonjour JARVIS"):
        """Mesure le temps de réponse de JARVIS"""
        try:
            start_time = time.time()
            
            response = requests.post(self.api_url, 
                json={
                    'model': 'deepseek-ai/DeepSeek-R1-0528',
                    'messages': [{'role': 'user', 'content': test_message}],
                    'max_tokens': 50,
                    'temperature': 0.1
                }, 
                timeout=30
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'response_time': response_time,
                    'tokens_per_second': 50 / response_time if response_time > 0 else 0,
                    'status': 'Opérationnel'
                }
            else:
                return {
                    'success': False,
                    'response_time': response_time,
                    'error': f"HTTP {response.status_code}",
                    'status': 'Erreur'
                }
                
        except Exception as e:
            return {
                'success': False,
                'response_time': 999,
                'error': str(e),
                'status': 'Inaccessible'
            }
    
    def get_system_performance(self):
        """Analyse les performances système"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Mémoire
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = memory.available / (1024**3)
            
            # GPU (si disponible)
            gpu_info = "Non détecté"
            try:
                result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu,memory.used,memory.total', '--format=csv,noheader,nounits'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    gpu_data = result.stdout.strip().split(',')
                    gpu_info = f"GPU: {gpu_data[0]}% utilisé, {gpu_data[1]}/{gpu_data[2]} MB"
            except:
                pass
            
            return {
                'cpu_percent': cpu_percent,
                'cpu_count': cpu_count,
                'memory_percent': memory_percent,
                'memory_available_gb': round(memory_available_gb, 1),
                'gpu_info': gpu_info,
                'status': 'Optimal' if cpu_percent < 80 and memory_percent < 80 else 'Surchargé'
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'status': 'Erreur analyse'
            }
    
    def restart_deepseek_turbo(self):
        """Redémarre DeepSeek avec optimisations turbo"""
        try:
            # Arrêter les processus existants
            subprocess.run(['pkill', '-f', 'vllm.*8000'], capture_output=True)
            subprocess.run(['pkill', '-f', 'llama.*8000'], capture_output=True)
            time.sleep(3)
            
            # Démarrer en mode turbo
            script_path = "./demarrer_deepseek_turbo.sh"
            if os.path.exists(script_path):
                subprocess.Popen(['bash', script_path], 
                               stdout=subprocess.DEVNULL, 
                               stderr=subprocess.DEVNULL)
                return "✅ DeepSeek redémarré en mode TURBO"
            else:
                return "❌ Script turbo non trouvé"
                
        except Exception as e:
            return f"❌ Erreur redémarrage: {str(e)}"
    
    def generate_performance_report(self):
        """Génère un rapport complet des performances"""
        # Appliquer optimisations
        optimizations = self.apply_turbo_optimizations()
        
        # Mesurer performances
        response_test = self.measure_response_time()
        system_perf = self.get_system_performance()
        
        # Générer rapport
        report = f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 20px;">
            <h2>🚀 RAPPORT PERFORMANCE JARVIS TURBO</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <h3>⚡ ACCÉLÉRATEURS TURBO</h3>
                    <p><strong>Status:</strong> {len(optimizations)} optimisations actives</p>
                    <div style="font-size: 12px; max-height: 150px; overflow-y: auto;">
                        {"<br>".join(optimizations[:8])}
                        {f"<br><em>... et {len(optimizations) - 8} autres</em>" if len(optimizations) > 8 else ""}
                    </div>
                </div>
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <h3>🧠 PERFORMANCE IA</h3>
                    <p><strong>Status:</strong> {response_test.get('status', 'Inconnu')}</p>
                    <p><strong>Temps réponse:</strong> {response_test.get('response_time', 0):.2f}s</p>
                    <p><strong>Vitesse:</strong> {response_test.get('tokens_per_second', 0):.1f} tokens/s</p>
                    {"<p style='color: #ff6b6b;'><strong>Erreur:</strong> " + response_test.get('error', '') + "</p>" if not response_test.get('success') else ""}
                </div>
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <h3>💻 SYSTÈME</h3>
                    <p><strong>CPU:</strong> {system_perf.get('cpu_percent', 0):.1f}% ({system_perf.get('cpu_count', 0)} cœurs)</p>
                    <p><strong>RAM:</strong> {system_perf.get('memory_percent', 0):.1f}% utilisée</p>
                    <p><strong>Disponible:</strong> {system_perf.get('memory_available_gb', 0)} GB</p>
                    <p><strong>GPU:</strong> {system_perf.get('gpu_info', 'Non détecté')}</p>
                </div>
                
            </div>
            
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px; margin-top: 20px;">
                <h3>📊 RECOMMANDATIONS</h3>
                {self._generate_recommendations(response_test, system_perf)}
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <p><strong>📅 Rapport généré:</strong> {datetime.now().strftime('%H:%M:%S')}</p>
                <p style="font-size: 12px; opacity: 0.8;">Les accélérateurs turbo restent actifs jusqu'au redémarrage système</p>
            </div>
        </div>
        """
        
        return report
    
    def _generate_recommendations(self, response_test, system_perf):
        """Génère des recommandations d'optimisation"""
        recommendations = []
        
        # Analyse temps de réponse
        response_time = response_test.get('response_time', 999)
        if response_time > 10:
            recommendations.append("🔴 Temps de réponse lent > 10s - Redémarrer en mode TURBO")
        elif response_time > 5:
            recommendations.append("🟡 Temps de réponse moyen > 5s - Optimisations possibles")
        else:
            recommendations.append("🟢 Temps de réponse excellent < 5s")
        
        # Analyse système
        cpu_percent = system_perf.get('cpu_percent', 0)
        memory_percent = system_perf.get('memory_percent', 0)
        
        if cpu_percent > 90:
            recommendations.append("🔴 CPU surchargé > 90% - Fermer applications inutiles")
        elif cpu_percent > 70:
            recommendations.append("🟡 CPU élevé > 70% - Surveiller utilisation")
        
        if memory_percent > 90:
            recommendations.append("🔴 RAM saturée > 90% - Libérer mémoire")
        elif memory_percent > 80:
            recommendations.append("🟡 RAM élevée > 80% - Surveiller utilisation")
        
        # GPU
        if "Non détecté" in system_perf.get('gpu_info', ''):
            recommendations.append("🟡 GPU non détecté - Vérifier drivers NVIDIA")
        
        if not recommendations:
            recommendations.append("🟢 Système optimal - Aucune action requise")
        
        return "<br>".join([f"• {rec}" for rec in recommendations])

# Instance globale
turbo_optimizer = JarvisTurboOptimizer()

def optimize_jarvis_turbo():
    """Fonction principale d'optimisation pour l'interface"""
    return turbo_optimizer.generate_performance_report()

def restart_jarvis_turbo():
    """Redémarre JARVIS en mode turbo"""
    result = turbo_optimizer.restart_deepseek_turbo()
    return f"""
    <div style="background: linear-gradient(45deg, #4caf50, #45a049); color: white; padding: 15px; border-radius: 10px;">
        <h4>🚀 REDÉMARRAGE TURBO</h4>
        <p>{result}</p>
        <p><em>Attendre 60 secondes pour le chargement complet...</em></p>
    </div>
    """
