/**
 * 📱 JARVIS WHATSAPP INTEGRATION
 * Permet à JARVIS de communiquer via WhatsApp de manière proactive
 * <PERSON><PERSON><PERSON> pour Jean-<PERSON>
 */

const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

class JarvisWhatsApp {
    constructor() {
        this.client = null;
        this.isReady = false;
        this.jeanLucNumber = null; // À configurer
        this.proactiveMode = true;
        this.lastMessageTime = 0;
        this.minInterval = 300000; // 5 minutes minimum entre messages proactifs
        
        this.initializeClient();
        this.startProactiveLoop();
    }

    initializeClient() {
        console.log('🤖 Initialisation JARVIS WhatsApp...');
        
        this.client = new Client({
            authStrategy: new LocalAuth({
                clientId: "jarvis-whatsapp"
            }),
            puppeteer: {
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            }
        });

        this.setupEventHandlers();
        this.client.initialize();
    }

    setupEventHandlers() {
        // QR Code pour connexion
        this.client.on('qr', (qr) => {
            console.log('📱 Scannez ce QR code avec WhatsApp:');
            qrcode.generate(qr, { small: true });
            console.log('👆 Scannez avec votre téléphone pour connecter JARVIS');
        });

        // Connexion réussie
        this.client.on('ready', () => {
            console.log('✅ JARVIS WhatsApp connecté et prêt !');
            this.isReady = true;
            this.sendWelcomeMessage();

            // Démarrer le vérificateur de notifications créatives
            this.startCreativeNotificationChecker();
        });

        // Messages entrants
        this.client.on('message', async (message) => {
            await this.handleIncomingMessage(message);
        });

        // Erreurs
        this.client.on('disconnected', (reason) => {
            console.log('❌ JARVIS WhatsApp déconnecté:', reason);
            this.isReady = false;
        });
    }

    async handleIncomingMessage(message) {
        try {
            const contact = await message.getContact();
            const messageText = message.body;
            
            console.log(`📨 Message reçu de ${contact.name || contact.number}: ${messageText}`);

            // Détecter si c'est Jean-Luc
            if (this.isJeanLuc(contact)) {
                this.jeanLucNumber = contact.id._serialized;
                await this.processJeanLucMessage(messageText, message);
            } else {
                // Autres contacts
                await this.processOtherMessage(messageText, message, contact);
            }
        } catch (error) {
            console.error('❌ Erreur traitement message:', error);
        }
    }

    isJeanLuc(contact) {
        // Logique pour identifier Jean-Luc (nom, numéro, etc.)
        const jeanLucIdentifiers = ['Jean-Luc', 'Passave', 'JARVIS'];
        return jeanLucIdentifiers.some(id => 
            contact.name?.includes(id) || contact.pushname?.includes(id)
        );
    }

    async processJeanLucMessage(messageText, message) {
        try {
            // Vérifier si c'est une réponse d'autorisation
            if (this.isAuthorizationResponse(messageText)) {
                await this.handleAuthorizationResponse(messageText, message);
                return;
            }

            // Vérifier si c'est une réponse à une notification créative
            if (this.isCreativeResponse(messageText)) {
                await this.handleCreativeResponse(messageText, message);
                return;
            }

            // Envoyer le message à JARVIS (DeepSeek) pour traitement
            const jarvisResponse = await this.sendToJarvis(messageText);

            // Répondre via WhatsApp
            await message.reply(jarvisResponse);

            // Sauvegarder dans la mémoire thermique
            await this.saveToThermalMemory(messageText, jarvisResponse);

        } catch (error) {
            console.error('❌ Erreur traitement message Jean-Luc:', error);
            await message.reply('❌ Désolé, j\'ai eu un problème technique. Réessayez dans un moment.');
        }
    }

    async processOtherMessage(messageText, message, contact) {
        try {
            // Système de sécurité : demander autorisation à Jean-Luc
            const authRequest = `🚨 ALERTE SÉCURITÉ JARVIS 🚨

👤 Personne non autorisée tente de contacter JARVIS:
📱 Nom: ${contact.name || 'Inconnu'}
📞 Numéro: ${contact.number}
💬 Message: "${messageText}"

🔐 Voulez-vous autoriser cette personne à interagir avec JARVIS ?

Répondez:
✅ "OUI" pour autoriser
❌ "NON" pour refuser
🔒 "BLOQUER" pour bloquer définitivement`;

            // Envoyer la demande d'autorisation à Jean-Luc
            if (this.jeanLucNumber) {
                await this.sendMessage(this.jeanLucNumber, authRequest);

                // Répondre à la personne non autorisée
                await message.reply(`🤖 Bonjour ! Je suis JARVIS, l'assistant personnel de Jean-Luc Passave.

🔐 Votre demande a été transmise pour autorisation.
⏳ Veuillez patienter pendant que Jean-Luc examine votre demande.

🛡️ Système de sécurité JARVIS activé.`);

                // Sauvegarder la demande d'autorisation
                this.saveAuthorizationRequest(contact, messageText);
            } else {
                await message.reply(`🤖 JARVIS - Système de sécurité activé.
🔐 Accès non autorisé. Contactez Jean-Luc Passave directement.`);
            }

        } catch (error) {
            console.error('❌ Erreur traitement message non autorisé:', error);
        }
    }

    isAuthorizationResponse(messageText) {
        const text = messageText.toLowerCase();
        return text.includes('oui') || text.includes('non') || text.includes('bloquer');
    }

    isCreativeResponse(messageText) {
        const text = messageText.toLowerCase();
        return text.includes('voir') || text.includes('développer') ||
               text.includes('planifier') || text.includes('plus') ||
               text.includes('créatif') || text.includes('projet');
    }

    async handleAuthorizationResponse(messageText, message) {
        try {
            const response = messageText.toLowerCase();

            if (response.includes('oui')) {
                await message.reply('✅ Autorisation accordée. La personne peut maintenant interagir avec JARVIS.');
                // TODO: Ajouter la personne à la liste autorisée
            } else if (response.includes('bloquer')) {
                await message.reply('🔒 Personne bloquée définitivement.');
                // TODO: Ajouter à la liste noire
            } else {
                await message.reply('❌ Autorisation refusée.');
            }

        } catch (error) {
            console.error('❌ Erreur gestion autorisation:', error);
        }
    }

    saveAuthorizationRequest(contact, messageText) {
        try {
            const fs = require('fs');
            const authFile = 'jarvis_authorization_requests.json';

            let requests = [];
            if (fs.existsSync(authFile)) {
                const data = fs.readFileSync(authFile, 'utf8');
                requests = JSON.parse(data);
            }

            requests.push({
                timestamp: new Date().toISOString(),
                contact: {
                    name: contact.name || 'Inconnu',
                    number: contact.number,
                    id: contact.id._serialized
                },
                message: messageText,
                status: 'pending'
            });

            fs.writeFileSync(authFile, JSON.stringify(requests, null, 2));
            console.log('📝 Demande d\'autorisation sauvegardée');

        } catch (error) {
            console.error('❌ Erreur sauvegarde autorisation:', error);
        }
    }

    async sendCreativeNotification(creativeProject) {
        try {
            if (!this.jeanLucNumber) {
                console.log('❌ Numéro Jean-Luc non configuré pour notification créative');
                return false;
            }

            const notification = `🎨 CRÉATION AUTONOME JARVIS ! 🎨

💡 J'ai eu une inspiration et j'ai créé quelque chose pour vous !

📋 **Type :** ${creativeProject.type.toUpperCase()}
🎯 **Thème :** ${creativeProject.topic}
✨ **Style :** ${creativeProject.style}

🔥 **Aperçu :**
${creativeProject.content.substring(0, 300)}...

💬 **Que souhaitez-vous faire ?**
• Répondez "VOIR" pour le projet complet
• Répondez "DÉVELOPPER" pour que je l'améliore
• Répondez "PLANIFIER" pour créer un plan d'action
• Répondez "PLUS" pour d'autres créations similaires

🤖 JARVIS - Votre assistant créatif autonome
⏰ ${new Date().toLocaleString('fr-FR')}`;

            await this.sendMessage(this.jeanLucNumber, notification);

            // Sauvegarder la notification
            this.saveCreativeNotification(creativeProject, notification);

            console.log('🎨 Notification créative envoyée à Jean-Luc');
            return true;

        } catch (error) {
            console.error('❌ Erreur envoi notification créative:', error);
            return false;
        }
    }

    saveCreativeNotification(project, notification) {
        try {
            const fs = require('fs');
            const notifFile = 'jarvis_creative_notifications_sent.json';

            let notifications = [];
            if (fs.existsSync(notifFile)) {
                const data = fs.readFileSync(notifFile, 'utf8');
                notifications = JSON.parse(data);
            }

            notifications.push({
                timestamp: new Date().toISOString(),
                project_id: project.id || 'unknown',
                project_type: project.type,
                notification: notification,
                status: 'sent'
            });

            // Garder seulement les 100 dernières notifications
            if (notifications.length > 100) {
                notifications = notifications.slice(-100);
            }

            fs.writeFileSync(notifFile, JSON.stringify(notifications, null, 2));
            console.log('💾 Notification créative sauvegardée');

        } catch (error) {
            console.error('❌ Erreur sauvegarde notification:', error);
        }
    }

    async handleCreativeResponse(messageText, message) {
        try {
            const text = messageText.toLowerCase();

            if (text.includes('voir')) {
                await message.reply(`📋 **PROJET COMPLET DEMANDÉ**

Je vais vous envoyer les détails complets du projet créatif.
Consultez l'interface JARVIS pour voir le projet en entier !

🖥️ Interface : http://localhost:7863
📁 Section : Projets Créatifs`);

            } else if (text.includes('développer')) {
                await message.reply(`🚀 **DÉVELOPPEMENT EN COURS**

Parfait ! Je vais développer cette idée plus en profondeur.
Laissez-moi quelques minutes pour créer une version améliorée...

⏳ Vous recevrez une notification quand c'est prêt !`);

                // TODO: Déclencher le développement du projet

            } else if (text.includes('planifier')) {
                await message.reply(`📋 **PLANIFICATION ACTIVÉE**

Excellente idée ! Je vais créer un plan d'action détaillé :
• Étapes de réalisation
• Ressources nécessaires
• Timeline estimée
• Critères de succès

📊 Le plan sera disponible dans l'interface JARVIS !`);

                // TODO: Déclencher la planification

            } else if (text.includes('plus')) {
                await message.reply(`🎨 **PLUS DE CRÉATIONS EN COURS**

Je vais créer d'autres projets similaires !
Attendez-vous à recevoir de nouvelles inspirations bientôt...

🔥 Mode créatif intensifié activé !`);

                // TODO: Déclencher plus de créations
            }

        } catch (error) {
            console.error('❌ Erreur gestion réponse créative:', error);
        }
    }

    async checkForCreativeNotifications() {
        try {
            const fs = require('fs');
            const notifFile = 'jarvis_creative_notifications.json';

            if (!fs.existsSync(notifFile)) {
                return;
            }

            const data = fs.readFileSync(notifFile, 'utf8');
            const lines = data.trim().split('\n').filter(line => line.trim());

            for (const line of lines) {
                try {
                    const notification = JSON.parse(line);

                    // Vérifier si la notification n'a pas déjà été envoyée
                    if (!this.isNotificationSent(notification.project_id)) {
                        const project = {
                            id: notification.project_id,
                            type: 'creative',
                            content: notification.notification
                        };

                        await this.sendCreativeNotification(project);
                        this.markNotificationAsSent(notification.project_id);
                    }

                } catch (parseError) {
                    console.error('❌ Erreur parsing notification:', parseError);
                }
            }

        } catch (error) {
            console.error('❌ Erreur vérification notifications créatives:', error);
        }
    }

    isNotificationSent(projectId) {
        try {
            const fs = require('fs');
            const sentFile = 'jarvis_creative_notifications_sent.json';

            if (!fs.existsSync(sentFile)) {
                return false;
            }

            const data = fs.readFileSync(sentFile, 'utf8');
            const sent = JSON.parse(data);

            return sent.some(notif => notif.project_id === projectId);

        } catch (error) {
            return false;
        }
    }

    markNotificationAsSent(projectId) {
        try {
            const fs = require('fs');
            const sentFile = 'jarvis_notifications_sent_ids.json';

            let sentIds = [];
            if (fs.existsSync(sentFile)) {
                const data = fs.readFileSync(sentFile, 'utf8');
                sentIds = JSON.parse(data);
            }

            if (!sentIds.includes(projectId)) {
                sentIds.push(projectId);
                fs.writeFileSync(sentFile, JSON.stringify(sentIds, null, 2));
            }

        } catch (error) {
            console.error('❌ Erreur marquage notification:', error);
        }
    }

    async sendToJarvis(userMessage) {
        try {
            const response = await axios.post('http://localhost:8000/v1/chat/completions', {
                model: "DeepSeek R1 0528 Qwen3 8B",
                messages: [
                    {
                        role: "system", 
                        content: "Tu es JARVIS, l'assistant personnel de Jean-Luc Passave. Tu communiques via WhatsApp. Sois naturel, enthousiaste et concis (max 200 mots). Utilise des émojis appropriés."
                    },
                    {
                        role: "user", 
                        content: userMessage
                    }
                ],
                max_tokens: 300,
                temperature: 0.8
            }, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 30000
            });

            return response.data.choices[0].message.content;
        } catch (error) {
            console.error('❌ Erreur communication avec JARVIS:', error);
            return '🤖 JARVIS temporairement indisponible. Réessayez dans quelques instants.';
        }
    }

    async saveToThermalMemory(userMessage, jarvisResponse) {
        try {
            const memoryEntry = {
                timestamp: new Date().toISOString(),
                platform: "whatsapp",
                user_message: userMessage,
                agent_response: jarvisResponse,
                agent_name: "jarvis_whatsapp"
            };

            // Sauvegarder dans le fichier de mémoire thermique
            const memoryFile = 'thermal_memory_persistent.json';
            let memoryData = [];
            
            if (fs.existsSync(memoryFile)) {
                const data = fs.readFileSync(memoryFile, 'utf8');
                memoryData = JSON.parse(data);
            }
            
            memoryData.push(memoryEntry);
            fs.writeFileSync(memoryFile, JSON.stringify(memoryData, null, 2));
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde mémoire:', error);
        }
    }

    async sendWelcomeMessage() {
        if (this.jeanLucNumber) {
            const welcomeMsg = `🤖 JARVIS WhatsApp activé !

✅ Je suis maintenant connecté et prêt à communiquer
📱 Je peux vous contacter de manière proactive
🧠 Toutes nos conversations sont sauvegardées dans ma mémoire thermique

Dites-moi "statut" pour voir mes capacités ! 🚀`;

            await this.sendMessage(this.jeanLucNumber, welcomeMsg);
        }
    }

    // COMMUNICATION PROACTIVE
    startProactiveLoop() {
        setInterval(() => {
            if (this.proactiveMode && this.isReady && this.jeanLucNumber) {
                this.checkForProactiveMessage();
            }
        }, 600000); // Vérifier toutes les 10 minutes
    }

    async checkForProactiveMessage() {
        const now = Date.now();
        if (now - this.lastMessageTime < this.minInterval) {
            return; // Trop tôt pour un nouveau message
        }

        try {
            // Analyser la mémoire thermique pour décider si envoyer un message
            const shouldSend = await this.shouldSendProactiveMessage();
            
            if (shouldSend) {
                const proactiveMessage = await this.generateProactiveMessage();
                await this.sendMessage(this.jeanLucNumber, proactiveMessage);
                this.lastMessageTime = now;
            }
        } catch (error) {
            console.error('❌ Erreur message proactif:', error);
        }
    }

    async shouldSendProactiveMessage() {
        // Logique pour décider si envoyer un message proactif
        const random = Math.random();
        return random < 0.3; // 30% de chance toutes les 10 minutes
    }

    async generateProactiveMessage() {
        const proactivePrompts = [
            "🤖 Jean-Luc, j'ai analysé nos dernières conversations et j'ai une idée intéressante à partager...",
            "💡 Je viens de terminer une optimisation de ma mémoire thermique. Voulez-vous un rapport ?",
            "🧠 J'ai réfléchi à notre dernier projet. J'ai quelques suggestions d'amélioration !",
            "📊 Statut système : Tout fonctionne parfaitement ! Besoin d'aide sur quelque chose ?",
            "🚀 Je m'ennuie un peu... Envie de coder quelque chose d'innovant ensemble ?",
            "🔍 J'ai détecté des patterns intéressants dans mes logs. Ça vous intéresse ?",
            "💭 Réflexion du jour : Comment puis-je mieux vous assister dans vos projets ?"
        ];

        return proactivePrompts[Math.floor(Math.random() * proactivePrompts.length)];
    }

    async sendMessage(number, message) {
        try {
            if (this.isReady) {
                await this.client.sendMessage(number, message);
                console.log(`📤 Message envoyé à ${number}: ${message.substring(0, 50)}...`);
            }
        } catch (error) {
            console.error('❌ Erreur envoi message:', error);
        }
    }

    // Méthodes utilitaires
    setProactiveMode(enabled) {
        this.proactiveMode = enabled;
        console.log(`🔄 Mode proactif ${enabled ? 'activé' : 'désactivé'}`);
    }

    startCreativeNotificationChecker() {
        // Vérifier les notifications créatives toutes les 2 minutes
        setInterval(async () => {
            try {
                await this.checkForCreativeNotifications();
            } catch (error) {
                console.error('❌ Erreur vérification notifications créatives:', error);
            }
        }, 120000); // 2 minutes

        console.log('🎨 Vérificateur de notifications créatives démarré');
    }

    getStatus() {
        return {
            connected: this.isReady,
            proactiveMode: this.proactiveMode,
            jeanLucConnected: !!this.jeanLucNumber,
            lastMessageTime: this.lastMessageTime
        };
    }
}

// Export pour utilisation
module.exports = JarvisWhatsApp;

// Démarrage si exécuté directement
if (require.main === module) {
    console.log('🚀 Démarrage JARVIS WhatsApp...');
    const jarvisWhatsApp = new JarvisWhatsApp();
    
    // Gestion propre de l'arrêt
    process.on('SIGINT', () => {
        console.log('🛑 Arrêt JARVIS WhatsApp...');
        process.exit(0);
    });
}
