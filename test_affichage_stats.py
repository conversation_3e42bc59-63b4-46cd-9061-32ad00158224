#!/usr/bin/env python3
"""
🔍 TEST AFFICHAGE STATISTIQUES JARVIS
Vérifie que l'affichage des statistiques de la mémoire thermique est lisible
"""

import requests
import json
import time

def test_affichage_stats():
    """Test l'affichage des statistiques via l'API"""
    try:
        print("🔍 TEST AFFICHAGE STATISTIQUES MÉMOIRE THERMIQUE")
        print("=" * 60)
        
        # Test de l'endpoint des statistiques
        base_url = "http://localhost:7867"
        
        # Vérifier que l'interface répond
        response = requests.get(base_url, timeout=10)
        if response.status_code != 200:
            print(f"❌ Interface non accessible: {response.status_code}")
            return False
        
        print("✅ Interface JARVIS accessible")
        print(f"📏 Taille réponse: {len(response.text):,} caractères")
        
        # Vérifier la présence du HTML formaté pour les statistiques
        html_content = response.text
        
        # Rechercher les éléments d'affichage améliorés
        checks = {
            "Mémoire Thermique Active": "🧠 MÉMOIRE THERMIQUE ACTIVE" in html_content,
            "Grille de statistiques": "grid-template-columns: 1fr 1fr" in html_content,
            "Padding amélioré": "padding: 25px" in html_content,
            "Bordures arrondies": "border-radius: 15px" in html_content,
            "Couleurs gradient": "linear-gradient" in html_content,
            "Taille police": "font-size: 28px" in html_content,
            "Statut opérationnel": "STATUT: OPÉRATIONNELLE" in html_content
        }
        
        print("\n📊 VÉRIFICATION ÉLÉMENTS D'AFFICHAGE:")
        print("-" * 40)
        
        passed = 0
        total = len(checks)
        
        for element, found in checks.items():
            status = "✅" if found else "❌"
            print(f"{status} {element}")
            if found:
                passed += 1
        
        print(f"\n🎯 RÉSULTAT: {passed}/{total} éléments trouvés")
        
        if passed >= total * 0.8:  # 80% des éléments
            print("🎉 AFFICHAGE STATISTIQUES: CORRIGÉ ET LISIBLE !")
            print("📱 L'affichage n'est plus étriqué")
            print("👁️ Toutes les informations sont visibles")
            return True
        else:
            print("⚠️ AFFICHAGE STATISTIQUES: PARTIELLEMENT CORRIGÉ")
            print("🔧 Quelques éléments nécessitent encore des ajustements")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test affichage: {e}")
        return False

def test_fonction_update_thermal_status():
    """Test direct de la fonction update_thermal_status"""
    try:
        print("\n🔧 TEST DIRECT FONCTION update_thermal_status()")
        print("-" * 50)
        
        # Import de la fonction
        import sys
        sys.path.append('/Volumes/seagate/Louna_Electron_Latest')
        
        from jarvis_interface_propre import update_thermal_status
        
        # Appel direct de la fonction
        result = update_thermal_status()
        
        print(f"📏 Longueur résultat: {len(result)} caractères")
        
        # Vérifier que c'est du HTML et non du texte brut
        html_indicators = [
            "<div",
            "style=",
            "background:",
            "padding:",
            "border-radius:",
            "</div>"
        ]
        
        html_found = sum(1 for indicator in html_indicators if indicator in result)
        
        print(f"🔍 Indicateurs HTML trouvés: {html_found}/{len(html_indicators)}")
        
        if html_found >= len(html_indicators) * 0.8:
            print("✅ La fonction retourne du HTML formaté (plus de texte brut)")
            print("🎨 Affichage sera lisible dans l'interface")
            
            # Afficher un extrait pour vérification
            print("\n📋 EXTRAIT DU HTML GÉNÉRÉ:")
            print("-" * 30)
            lines = result.split('\n')[:10]  # 10 premières lignes
            for line in lines:
                if line.strip():
                    print(f"   {line.strip()[:80]}...")
            
            return True
        else:
            print("❌ La fonction retourne encore du texte brut")
            print("🔧 L'affichage sera toujours étriqué")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test fonction: {e}")
        return False

def main():
    """Test complet de l'affichage des statistiques"""
    print("🚀 DÉMARRAGE TESTS AFFICHAGE STATISTIQUES")
    print("=" * 70)
    print(f"⏰ Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Interface web
    test1_passed = test_affichage_stats()
    
    # Test 2: Fonction directe
    test2_passed = test_fonction_update_thermal_status()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ FINAL DES TESTS")
    print("-" * 30)
    
    print(f"🌐 Test interface web: {'✅ PASSÉ' if test1_passed else '❌ ÉCHOUÉ'}")
    print(f"🔧 Test fonction directe: {'✅ PASSÉ' if test2_passed else '❌ ÉCHOUÉ'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 TOUS LES TESTS PASSÉS !")
        print("✅ L'affichage des statistiques est maintenant LISIBLE")
        print("📱 Plus d'affichage étriqué dans l'interface")
        print("👁️ Toutes les informations sont parfaitement visibles")
        print("\n🌐 Accédez à votre interface: http://localhost:7867")
        print("🔄 Cliquez sur 'Actualiser Stats' pour voir les améliorations")
        
    elif test1_passed or test2_passed:
        print("\n⚠️ TESTS PARTIELLEMENT RÉUSSIS")
        print("🔧 L'affichage est amélioré mais peut nécessiter des ajustements")
        
    else:
        print("\n❌ TESTS ÉCHOUÉS")
        print("🚨 L'affichage des statistiques nécessite encore des corrections")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    main()
