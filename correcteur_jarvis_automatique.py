#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR AUTOMATIQUE JARVIS - JEAN-LUC PASSAVE
Corrige automatiquement les problèmes d'affichage sans refaire le code
"""

import re
import os
import shutil
from datetime import datetime

def analyser_problemes(fichier_path):
    """Analyse les problèmes dans le fichier JARVIS"""
    print(f"🔍 ANALYSE: {fichier_path}")
    
    with open(fichier_path, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    problemes = []
    
    # 1. CSS trop complexe
    if 'custom-chatbot' in contenu and len(contenu) > 5000:
        problemes.append("CSS_COMPLEXE")
    
    # 2. Configuration chatbot problématique
    if 'bubble_full_width=False' in contenu:
        problemes.append("BUBBLE_CONFIG")
    
    # 3. Thème problématique
    if 'theme=gr.themes.Soft()' in contenu:
        problemes.append("THEME_SOFT")
    
    # 4. Imports inutiles
    imports_inutiles = ['psutil', 'urllib3', 'threading', 'uuid', 'subprocess']
    for imp in imports_inutiles:
        if f'import {imp}' in contenu:
            problemes.append(f"IMPORT_{imp.upper()}")
    
    # 5. Code trop long
    lignes = contenu.count('\n')
    if lignes > 2000:
        problemes.append("CODE_TROP_LONG")
    
    print(f"❌ PROBLÈMES DÉTECTÉS: {len(problemes)}")
    for p in problemes:
        print(f"   - {p}")
    
    return problemes, contenu

def corriger_css_simple(contenu):
    """Corrige le CSS pour éviter l'affichage horizontal"""
    print("🎨 CORRECTION CSS...")
    
    # Remplacer le CSS complexe par un CSS simple
    pattern_css = r'custom_css = """.*?"""'
    css_simple = '''custom_css = """
    /* CSS SIMPLE POUR ÉVITER AFFICHAGE HORIZONTAL */
    .chatbot { width: 100% !important; }
    .message { display: block !important; word-wrap: break-word !important; }
    """'''
    
    contenu_corrige = re.sub(pattern_css, css_simple, contenu, flags=re.DOTALL)
    print("✅ CSS simplifié")
    return contenu_corrige

def corriger_chatbot_config(contenu):
    """Corrige la configuration du chatbot"""
    print("💬 CORRECTION CHATBOT...")
    
    # Simplifier la configuration du chatbot
    pattern_chatbot = r'conversation_display = gr\.Chatbot\([^)]*\)'
    config_simple = '''conversation_display = gr.Chatbot(
                    label="💬 Conversation avec JARVIS",
                    height=500,
                    show_label=True,
                    show_copy_button=True
                )'''
    
    contenu_corrige = re.sub(pattern_chatbot, config_simple, contenu, flags=re.DOTALL)
    print("✅ Configuration chatbot simplifiée")
    return contenu_corrige

def corriger_theme(contenu):
    """Corrige le thème Gradio"""
    print("🎨 CORRECTION THÈME...")
    
    # Remplacer le thème Soft par Default
    contenu_corrige = contenu.replace('theme=gr.themes.Soft()', 'theme=gr.themes.Default()')
    print("✅ Thème corrigé")
    return contenu_corrige

def nettoyer_imports(contenu):
    """Nettoie les imports inutiles"""
    print("📦 NETTOYAGE IMPORTS...")
    
    imports_a_supprimer = [
        'import threading',
        'import uuid', 
        'import subprocess',
        'import sys',
        'import signal',
        'import atexit',
        'from requests.adapters import HTTPAdapter',
        'from urllib3.util.retry import Retry',
        'import psutil'
    ]
    
    for imp in imports_a_supprimer:
        contenu = contenu.replace(imp, f'# {imp}  # SUPPRIMÉ PAR CORRECTEUR')
    
    print("✅ Imports nettoyés")
    return contenu

def sauvegarder_backup(fichier_path):
    """Sauvegarde le fichier original"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{fichier_path}.backup_{timestamp}"
    shutil.copy2(fichier_path, backup_path)
    print(f"💾 BACKUP: {backup_path}")
    return backup_path

def corriger_jarvis_automatique():
    """FONCTION PRINCIPALE - Corrige automatiquement JARVIS"""
    fichier_jarvis = "jarvis_interface_propre.py"
    
    print("🔧 CORRECTEUR AUTOMATIQUE JARVIS")
    print("=" * 50)
    
    # Vérifier que le fichier existe
    if not os.path.exists(fichier_jarvis):
        print(f"❌ ERREUR: {fichier_jarvis} non trouvé")
        return False
    
    # Analyser les problèmes
    problemes, contenu_original = analyser_problemes(fichier_jarvis)
    
    if not problemes:
        print("✅ AUCUN PROBLÈME DÉTECTÉ")
        return True
    
    # Sauvegarder l'original
    backup_path = sauvegarder_backup(fichier_jarvis)
    
    # Appliquer les corrections
    contenu_corrige = contenu_original
    
    if "CSS_COMPLEXE" in problemes:
        contenu_corrige = corriger_css_simple(contenu_corrige)
    
    if "BUBBLE_CONFIG" in problemes:
        contenu_corrige = corriger_chatbot_config(contenu_corrige)
    
    if "THEME_SOFT" in problemes:
        contenu_corrige = corriger_theme(contenu_corrige)
    
    if any("IMPORT_" in p for p in problemes):
        contenu_corrige = nettoyer_imports(contenu_corrige)
    
    # Sauvegarder le fichier corrigé
    with open(fichier_jarvis, 'w', encoding='utf-8') as f:
        f.write(contenu_corrige)
    
    print("\n🎉 CORRECTIONS APPLIQUÉES:")
    print(f"   📁 Original sauvé: {backup_path}")
    print(f"   ✅ Fichier corrigé: {fichier_jarvis}")
    print(f"   🔧 Problèmes résolus: {len(problemes)}")
    
    print("\n🚀 VOTRE JARVIS EST MAINTENANT CORRIGÉ !")
    print("   Vous pouvez le lancer normalement")
    
    return True

def restaurer_backup():
    """Restaure la dernière sauvegarde si besoin"""
    fichier_jarvis = "jarvis_interface_propre.py"
    
    # Chercher le dernier backup
    backups = [f for f in os.listdir('.') if f.startswith(f"{fichier_jarvis}.backup_")]
    
    if not backups:
        print("❌ Aucun backup trouvé")
        return False
    
    dernier_backup = sorted(backups)[-1]
    
    print(f"🔄 RESTAURATION: {dernier_backup}")
    shutil.copy2(dernier_backup, fichier_jarvis)
    print("✅ Fichier restauré")
    
    return True

if __name__ == "__main__":
    print("🔧 CORRECTEUR AUTOMATIQUE JARVIS")
    print("1. Corriger automatiquement")
    print("2. Restaurer backup")
    
    choix = input("\nVotre choix (1 ou 2): ").strip()
    
    if choix == "1":
        corriger_jarvis_automatique()
    elif choix == "2":
        restaurer_backup()
    else:
        print("❌ Choix invalide")
