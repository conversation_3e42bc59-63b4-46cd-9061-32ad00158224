#!/usr/bin/env python3
"""
🚀 ACCÉLÉRATEURS POUR JARVIS_INTERFACE_PROPRE.PY
"""

from concurrent.futures import ThreadPoolExecutor
import gzip
import pickle

import time
import json


class InterfaceAccelerator:
    def __init__(self):
        self.response_cache = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
    
    def accelerate_response(self, function, *args, **kwargs):
        cache_key = hash(str(args) + str(kwargs))
        if cache_key in self.response_cache:
            return self.response_cache[cache_key]
        
        result = function(*args, **kwargs)
        self.response_cache[cache_key] = result
        return result
    
    def async_process(self, function, *args, **kwargs):
        return self.thread_pool.submit(function, *args, **kwargs)



def cache_intelligent(max_size=1000):
    def decorator(func):
        cache = {}
        access_count = {}
        
        def wrapper(*args, **kwargs):
            key = hash(str(args) + str(kwargs))
            
            if key in cache:
                access_count[key] = access_count.get(key, 0) + 1
                return cache[key]
            
            result = func(*args, **kwargs)
            
            if len(cache) >= max_size:
                # Supprimer l'élément le moins utilisé
                least_used = min(access_count.items(), key=lambda x: x[1])[0]
                del cache[least_used]
                del access_count[least_used]
            
            cache[key] = result
            access_count[key] = 1
            return result
        
        return wrapper
    return decorator



def compression_rapide(data, level=6):
    if isinstance(data, str):
        data = data.encode('utf-8')
    elif isinstance(data, dict):
        data = json.dumps(data, separators=(',', ':')).encode('utf-8')
    
    return gzip.compress(data, compresslevel=level)

def decompression_rapide(compressed_data):
    decompressed = gzip.decompress(compressed_data)
    try:
        return json.loads(decompressed.decode('utf-8'))
    except:
        return decompressed.decode('utf-8')



def threading_optimise(max_workers=4):
    def decorator(func):
        thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        def wrapper(*args, **kwargs):
            if kwargs.get('async_mode', False):
                return thread_pool.submit(func, *args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


