{"buttons": {"send_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "📤 Envoyer", "found": true}, "clear_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🗑️ Effacer", "found": true}, "stop_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🛑 Stop", "found": true}, "mic_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🎤", "found": true}, "speaker_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🔊", "found": true}, "camera_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "📹", "found": true}, "switch_agent_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🤖 Changer Agent", "found": true}, "agent2_toggle_btn": {"status": "❌ Manquant", "text": "Agent 2", "found": false}, "deep_reflection_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🧠 Réflexion Profonde", "found": true}, "voice_interface_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🎤 Activer Interface Vocale", "found": true}, "speech_to_text_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🗣️ Reconnaissance Vocale", "found": true}, "text_to_speech_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🔊 Synthèse Vocale", "found": true}, "read_thoughts_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🧠 Lire Pen<PERSON>ées", "found": true}, "voice_status_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "📊 État Vocal", "found": true}, "creative_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🎨 Créativité", "found": true}, "music_btn": {"status": "❌ Manquant", "text": "🎵 Musique", "found": false}, "project_btn": {"status": "❌ Manquant", "text": "📋 Projets", "found": false}, "inspiration_btn": {"status": "❌ Manquant", "text": "💡 Inspiration", "found": false}, "security_btn": {"status": "❌ Manquant", "text": "🔐 Sécurité", "found": false}, "biometric_btn": {"status": "❌ Manquant", "text": "👤 Biométrie", "found": false}, "vpn_check_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🔍 Vérifier VPN", "found": true}, "vpn_connect_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🔐 Connecter VPN", "found": true}, "vpn_report_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "📊 Rapport Sécurité", "found": true}, "t7_check_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "🔍 Vérifier T7", "found": true}, "t7_sync_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "💾 Sync Forcée", "found": true}, "t7_toggle_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "⏸️ Arrêter Auto-Sync", "found": true}, "whatsapp_btn": {"status": "❌ Manquant", "text": "📱 WhatsApp", "found": false}, "whatsapp_send_btn": {"status": "❌ Manquant", "text": "📤 Envoyer Message", "found": false}, "whatsapp_status_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "📊 Statut WhatsApp", "found": true}, "monitoring_btn": {"status": "❌ Manquant", "text": "📊 Monitoring", "found": false}, "system_info_btn": {"status": "❌ Manquant", "text": "💻 Info Système", "found": false}, "memory_stats_btn": {"status": "❌ Manquant", "text": "🧠 Stats Mémoire", "found": false}, "code_btn": {"status": "✅ D<PERSON><PERSON>i", "text": "💻 Code", "found": true}, "execute_btn": {"status": "❌ Manquant", "text": "▶️ Exécuter", "found": false}, "workspace_btn": {"status": "❌ Manquant", "text": "📁 Workspace", "found": false}}, "functions": {"quick_voice_input": {"status": "✅ Implémentée", "button": "mic_btn", "implemented": true}, "speak_last_response": {"status": "✅ Implémentée", "button": "speaker_btn", "implemented": true}, "camera_interface": {"status": "✅ Implémentée", "button": "camera_btn", "implemented": true}, "send_message_to_agent": {"status": "✅ Implémentée", "button": "send_btn", "implemented": true}, "clear_conversation": {"status": "✅ Implémentée", "button": "clear_btn", "implemented": true}, "stop_current_generation": {"status": "✅ Implémentée", "button": "stop_btn", "implemented": true}, "switch_agent": {"status": "✅ Implémentée", "button": "switch_agent_btn", "implemented": true}, "voice_interface": {"status": "✅ Implémentée", "button": "voice_interface_btn", "implemented": true}, "speech_to_text": {"status": "✅ Implémentée", "button": "speech_to_text_btn", "implemented": true}, "text_to_speech": {"status": "✅ Implémentée", "button": "text_to_speech_btn", "implemented": true}, "read_agent_thoughts": {"status": "✅ Implémentée", "button": "read_thoughts_btn", "implemented": true}, "voice_status": {"status": "✅ Implémentée", "button": "voice_status_btn", "implemented": true}, "creative_interface": {"status": "✅ Implémentée", "button": "creative_btn", "implemented": true}, "music_interface": {"status": "✅ Implémentée", "button": "music_btn", "implemented": true}, "security_interface": {"status": "✅ Implémentée", "button": "security_btn", "implemented": true}, "biometric_interface": {"status": "✅ Implémentée", "button": "biometric_btn", "implemented": true}, "check_vpn_status_interface": {"status": "✅ Implémentée", "button": "vpn_check_btn", "implemented": true}, "connect_vpn_interface": {"status": "✅ Implémentée", "button": "vpn_connect_btn", "implemented": true}, "get_security_report_interface": {"status": "✅ Implémentée", "button": "vpn_report_btn", "implemented": true}, "check_t7_status_interface": {"status": "✅ Implémentée", "button": "t7_check_btn", "implemented": true}, "force_t7_sync_interface": {"status": "✅ Implémentée", "button": "t7_sync_btn", "implemented": true}, "toggle_t7_auto_sync": {"status": "✅ Implémentée", "button": "t7_toggle_btn", "implemented": true}, "whatsapp_interface": {"status": "✅ Implémentée", "button": "whatsapp_btn", "implemented": true}, "monitoring_interface": {"status": "✅ Implémentée", "button": "monitoring_btn", "implemented": true}, "code_interface": {"status": "✅ Implémentée", "button": "code_btn", "implemented": true}}, "modules": {"gradio": {"status": "✅ Importé", "available": true}, "requests": {"status": "✅ Importé", "available": true}, "json": {"status": "✅ Importé", "available": true}, "time": {"status": "✅ Importé", "available": true}, "os": {"status": "✅ Importé", "available": true}, "threading": {"status": "✅ Importé", "available": true}, "datetime": {"status": "✅ Importé", "available": true}}, "connections": {"mic_btn": {"status": "✅ Connecté", "function": "quick_voice_input", "connected": true}, "speaker_btn": {"status": "✅ Connecté", "function": "speak_last_response", "connected": true}, "camera_btn": {"status": "✅ Connecté", "function": "camera_interface", "connected": true}, "send_btn": {"status": "❌ Non connecté", "function": "send_message_to_agent", "connected": false}, "clear_btn": {"status": "✅ Connecté", "function": "clear_conversation", "connected": true}, "stop_btn": {"status": "✅ Connecté", "function": "stop_current_generation", "connected": true}, "switch_agent_btn": {"status": "✅ Connecté", "function": "switch_agent", "connected": true}, "voice_interface_btn": {"status": "❌ Non connecté", "function": "voice_interface", "connected": false}, "speech_to_text_btn": {"status": "✅ Connecté", "function": "speech_to_text", "connected": true}, "text_to_speech_btn": {"status": "❌ Non connecté", "function": "text_to_speech", "connected": false}, "read_thoughts_btn": {"status": "✅ Connecté", "function": "read_agent_thoughts", "connected": true}, "voice_status_btn": {"status": "✅ Connecté", "function": "voice_status", "connected": true}, "creative_btn": {"status": "❌ Non connecté", "function": "creative_interface", "connected": false}, "music_btn": {"status": "❌ Non connecté", "function": "music_interface", "connected": false}, "security_btn": {"status": "❌ Non connecté", "function": "security_interface", "connected": false}, "biometric_btn": {"status": "❌ Non connecté", "function": "biometric_interface", "connected": false}, "vpn_check_btn": {"status": "✅ Connecté", "function": "check_vpn_status_interface", "connected": true}, "vpn_connect_btn": {"status": "✅ Connecté", "function": "connect_vpn_interface", "connected": true}, "vpn_report_btn": {"status": "✅ Connecté", "function": "get_security_report_interface", "connected": true}, "t7_check_btn": {"status": "✅ Connecté", "function": "check_t7_status_interface", "connected": true}, "t7_sync_btn": {"status": "✅ Connecté", "function": "force_t7_sync_interface", "connected": true}, "t7_toggle_btn": {"status": "✅ Connecté", "function": "toggle_t7_auto_sync", "connected": true}, "whatsapp_btn": {"status": "❌ Non connecté", "function": "whatsapp_interface", "connected": false}, "monitoring_btn": {"status": "❌ Non connecté", "function": "monitoring_interface", "connected": false}, "code_btn": {"status": "❌ Non connecté", "function": "code_interface", "connected": false}}, "errors": ["❌ Bouton manquant: agent2_toggle_btn", "❌ Bouton manquant: music_btn", "❌ Bouton manquant: project_btn", "❌ Bouton manquant: inspiration_btn", "❌ Bouton manquant: security_btn", "❌ Bouton manquant: biometric_btn", "❌ Bouton manquant: whatsapp_btn", "❌ Bouton manquant: whatsapp_send_btn", "❌ Bouton manquant: monitoring_btn", "❌ Bouton manquant: system_info_btn", "❌ Bouton manquant: memory_stats_btn", "❌ Bouton manquant: execute_btn", "❌ Bouton manquant: workspace_btn", "❌ Connexion manquante: send_btn -> send_message_to_agent", "❌ Connexion manquante: voice_interface_btn -> voice_interface", "❌ Connexion manquante: text_to_speech_btn -> text_to_speech", "❌ Connexion manquante: creative_btn -> creative_interface", "❌ Connexion manquante: music_btn -> music_interface", "❌ Connexion manquante: security_btn -> security_interface", "❌ Connexion manquante: biometric_btn -> biometric_interface", "❌ Connexion manquante: whatsapp_btn -> whatsapp_interface", "❌ Connexion manquante: monitoring_btn -> monitoring_interface", "❌ Connexion manquante: code_btn -> code_interface"], "warnings": [], "score": 75, "timestamp": "2025-06-19T02:05:44.567078"}