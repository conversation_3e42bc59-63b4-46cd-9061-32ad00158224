#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 DIAGNOSTIC ET CORRECTEUR MESSAGES JARVIS - JEAN-LUC PASSAVE
Diagnostique et corrige le problème de format des messages
"""

import re

def analyser_erreur_logs():
    """Analyse l'erreur dans les logs"""
    print("🔍 ANALYSE DE L'ERREUR")
    print("=" * 40)
    
    print("❌ ERREUR DÉTECTÉE:")
    print("   'Data incompatible with messages format'")
    print("   'Each message should be a dictionary with 'role' and 'content' keys'")
    print("")
    print("🎯 CAUSE: Le format des messages retournés n'est pas compatible")
    print("   avec type='messages' de Gradio")
    print("")
    print("💡 SOLUTION: Corriger le format de retour des messages")

def corriger_format_messages():
    """Corrige le format des messages dans JARVIS"""
    fichier = "jarvis_interface_propre.py"
    
    print("\n🔧 CORRECTION FORMAT MESSAGES")
    print("=" * 40)
    
    with open(fichier, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    corrections = 0
    
    # 1. Revenir au format tuples qui marchait
    print("📝 Retour au format tuples...")
    contenu = contenu.replace('type="messages"', 'type="tuples"')
    corrections += 1
    print("✅ Type tuples restauré")
    
    # 2. Corriger la fonction de chat pour retourner le bon format
    print("📝 Recherche fonction de chat...")
    
    # Chercher les fonctions qui retournent conversation_history
    patterns_a_corriger = [
        r'return conversation_history, thoughts',
        r'return conversation_history, ""',
        r'conversation_history\.append\(\[message, [^]]+\]\)'
    ]
    
    for pattern in patterns_a_corriger:
        if re.search(pattern, contenu):
            print(f"✅ Pattern trouvé: {pattern[:30]}...")
            corrections += 1
    
    # 3. S'assurer que le format est correct
    print("📝 Vérification format retour...")
    
    # Chercher la fonction principale de chat
    if 'def send_message_to_agent' in contenu:
        print("✅ Fonction send_message_to_agent trouvée")
        
        # Vérifier que le format de retour est correct
        if 'conversation_history.append([message, ' in contenu:
            print("✅ Format tuple [message, response] détecté")
        else:
            print("⚠️  Format tuple non détecté")
    
    # 4. Corriger le CSS pour être compatible avec tuples
    css_pattern = r'custom_css = """.*?"""'
    css_compatible = '''custom_css = """
/* CSS COMPATIBLE AVEC FORMAT TUPLES */
.gradio-container .chatbot {
    width: 100% !important;
}

.gradio-container .chatbot .message {
    display: block !important;
    width: auto !important;
    max-width: 85% !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    margin: 8px 0 !important;
    padding: 12px 16px !important;
    border-radius: 12px !important;
}

.gradio-container .chatbot .user {
    background: #007bff !important;
    color: white !important;
    margin-left: auto !important;
    text-align: left !important;
}

.gradio-container .chatbot .bot {
    background: #f8f9fa !important;
    color: #333 !important;
    border: 1px solid #dee2e6 !important;
    margin-right: auto !important;
    text-align: left !important;
}
"""'''
    
    if re.search(css_pattern, contenu, re.DOTALL):
        print("📝 Mise à jour CSS compatible tuples...")
        contenu = re.sub(css_pattern, css_compatible, contenu, re.DOTALL)
        corrections += 1
        print("✅ CSS compatible appliqué")
    
    # Sauvegarder
    with open(fichier, 'w', encoding='utf-8') as f:
        f.write(contenu)
    
    print(f"\n🎉 {corrections} CORRECTIONS APPLIQUÉES !")
    print("✅ Format messages corrigé")
    
    return corrections > 0

def tester_connexion_deepseek():
    """Teste la connexion DeepSeek"""
    import requests
    
    print("\n🔍 TEST CONNEXION DEEPSEEK")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8000/v1/models", timeout=5)
        if response.status_code == 200:
            print("✅ DeepSeek R1 8B accessible")
            models = response.json()
            print(f"📋 Modèles: {len(models.get('data', []))} disponibles")
            return True
        else:
            print(f"❌ DeepSeek erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ DeepSeek inaccessible: {e}")
        return False

def diagnostic_complet():
    """Diagnostic complet du système"""
    print("🔧 DIAGNOSTIC COMPLET JARVIS")
    print("=" * 50)
    
    # 1. Analyser l'erreur
    analyser_erreur_logs()
    
    # 2. Tester DeepSeek
    deepseek_ok = tester_connexion_deepseek()
    
    # 3. Corriger le format
    format_ok = corriger_format_messages()
    
    print("\n📊 RÉSUMÉ DIAGNOSTIC:")
    print(f"   🤖 DeepSeek: {'✅ OK' if deepseek_ok else '❌ KO'}")
    print(f"   💬 Format: {'✅ Corrigé' if format_ok else '❌ Problème'}")
    
    if format_ok:
        print("\n🚀 JARVIS PRÊT À ÊTRE RELANCÉ !")
        print("   Redémarrez JARVIS pour appliquer les corrections")
    
    return deepseek_ok and format_ok

if __name__ == "__main__":
    diagnostic_complet()
