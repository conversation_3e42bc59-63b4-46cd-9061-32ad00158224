#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 COMPARATEUR DE CODES JARVIS - JEAN-LUC PASSAVE
Compare le code actuel avec une version fonctionnelle pour identifier les problèmes
"""

import os
import difflib
import re
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔍 [{timestamp}] {message}")

def trouver_version_fonctionnelle():
    """Trouve la meilleure version fonctionnelle de référence"""
    log("🔍 RECHERCHE VERSION FONCTIONNELLE")
    
    # Chercher dans les sauvegardes
    sauvegardes_possibles = [
        "SAUVEGARDES/JARVIS_FONCTIONNEL_2025-06-18_19-09-54/jarvis_interface_propre.py",
        "jarvis_interface_propre_SAUVEGARDE_FONCTIONNELLE_20250619_055612.py",
        "SAUVEGARDES/jarvis_interface_propre_backup_20250619_055612.py"
    ]
    
    for sauvegarde in sauvegardes_possibles:
        if os.path.exists(sauvegarde):
            log(f"✅ Version fonctionnelle trouvée: {sauvegarde}")
            return sauvegarde
    
    log("❌ Aucune version fonctionnelle trouvée")
    return None

def analyser_differences_structurelles(code_fonctionnel, code_actuel):
    """Analyse les différences structurelles importantes"""
    log("🔍 ANALYSE DIFFÉRENCES STRUCTURELLES")
    
    differences = []
    
    # 1. Taille des fichiers
    taille_fonctionnel = len(code_fonctionnel.split('\n'))
    taille_actuel = len(code_actuel.split('\n'))
    
    if abs(taille_fonctionnel - taille_actuel) > 100:
        differences.append({
            'type': 'TAILLE',
            'probleme': f"Différence de taille importante: {taille_fonctionnel} vs {taille_actuel} lignes",
            'gravite': 'HAUTE'
        })
    
    # 2. Imports
    imports_fonctionnel = re.findall(r'^import .*|^from .* import .*', code_fonctionnel, re.MULTILINE)
    imports_actuel = re.findall(r'^import .*|^from .* import .*', code_actuel, re.MULTILINE)
    
    imports_ajoutes = set(imports_actuel) - set(imports_fonctionnel)
    imports_supprimes = set(imports_fonctionnel) - set(imports_actuel)
    
    if imports_ajoutes:
        differences.append({
            'type': 'IMPORTS_AJOUTES',
            'probleme': f"Imports ajoutés: {list(imports_ajoutes)[:3]}...",
            'gravite': 'MOYENNE'
        })
    
    if imports_supprimes:
        differences.append({
            'type': 'IMPORTS_SUPPRIMES',
            'probleme': f"Imports supprimés: {list(imports_supprimes)[:3]}...",
            'gravite': 'HAUTE'
        })
    
    # 3. Configuration Gradio
    config_fonctionnel = re.search(r'demo\.launch\([^)]*\)', code_fonctionnel, re.DOTALL)
    config_actuel = re.search(r'demo\.launch\([^)]*\)', code_actuel, re.DOTALL)
    
    if config_fonctionnel and config_actuel:
        if config_fonctionnel.group() != config_actuel.group():
            differences.append({
                'type': 'CONFIG_GRADIO',
                'probleme': "Configuration demo.launch() différente",
                'gravite': 'HAUTE',
                'details': {
                    'fonctionnel': config_fonctionnel.group(),
                    'actuel': config_actuel.group()
                }
            })
    
    # 4. CSS
    css_fonctionnel = re.search(r'custom_css = """.*?"""', code_fonctionnel, re.DOTALL)
    css_actuel = re.search(r'custom_css = """.*?"""', code_actuel, re.DOTALL)
    
    if css_fonctionnel and css_actuel:
        if len(css_actuel.group()) > len(css_fonctionnel.group()) * 2:
            differences.append({
                'type': 'CSS_COMPLEXE',
                'probleme': "CSS beaucoup plus complexe dans version actuelle",
                'gravite': 'MOYENNE'
            })
    
    # 5. Fonctions principales
    fonctions_fonctionnel = re.findall(r'^def ([a-zA-Z_][a-zA-Z0-9_]*)', code_fonctionnel, re.MULTILINE)
    fonctions_actuel = re.findall(r'^def ([a-zA-Z_][a-zA-Z0-9_]*)', code_actuel, re.MULTILINE)
    
    fonctions_ajoutees = set(fonctions_actuel) - set(fonctions_fonctionnel)
    if len(fonctions_ajoutees) > 10:
        differences.append({
            'type': 'FONCTIONS_AJOUTEES',
            'probleme': f"{len(fonctions_ajoutees)} nouvelles fonctions ajoutées",
            'gravite': 'MOYENNE'
        })
    
    return differences

def comparer_sections_critiques(code_fonctionnel, code_actuel):
    """Compare les sections critiques du code"""
    log("🔍 COMPARAISON SECTIONS CRITIQUES")
    
    sections_critiques = []
    
    # 1. Configuration chatbot
    chatbot_fonctionnel = re.search(r'gr\.Chatbot\([^)]*\)', code_fonctionnel, re.DOTALL)
    chatbot_actuel = re.search(r'gr\.Chatbot\([^)]*\)', code_actuel, re.DOTALL)
    
    if chatbot_fonctionnel and chatbot_actuel:
        if chatbot_fonctionnel.group() != chatbot_actuel.group():
            sections_critiques.append({
                'section': 'CHATBOT_CONFIG',
                'probleme': 'Configuration gr.Chatbot() différente',
                'fonctionnel': chatbot_fonctionnel.group(),
                'actuel': chatbot_actuel.group()
            })
    
    # 2. Fonction send_message
    send_msg_fonctionnel = re.search(r'def send_message.*?(?=\ndef|\nclass|\n$)', code_fonctionnel, re.DOTALL)
    send_msg_actuel = re.search(r'def send_message.*?(?=\ndef|\nclass|\n$)', code_actuel, re.DOTALL)
    
    if send_msg_fonctionnel and send_msg_actuel:
        if len(send_msg_actuel.group()) > len(send_msg_fonctionnel.group()) * 2:
            sections_critiques.append({
                'section': 'SEND_MESSAGE',
                'probleme': 'Fonction send_message beaucoup plus complexe',
                'taille_fonctionnel': len(send_msg_fonctionnel.group()),
                'taille_actuel': len(send_msg_actuel.group())
            })
    
    # 3. Interface principale
    interface_fonctionnel = re.search(r'with gr\.Blocks.*?demo\.launch', code_fonctionnel, re.DOTALL)
    interface_actuel = re.search(r'with gr\.Blocks.*?demo\.launch', code_actuel, re.DOTALL)
    
    if interface_fonctionnel and interface_actuel:
        if len(interface_actuel.group()) > len(interface_fonctionnel.group()) * 1.5:
            sections_critiques.append({
                'section': 'INTERFACE_PRINCIPALE',
                'probleme': 'Interface principale plus complexe',
                'taille_fonctionnel': len(interface_fonctionnel.group()),
                'taille_actuel': len(interface_actuel.group())
            })
    
    return sections_critiques

def generer_diff_detaille(code_fonctionnel, code_actuel):
    """Génère un diff détaillé entre les deux codes"""
    log("📄 GÉNÉRATION DIFF DÉTAILLÉ")
    
    lignes_fonctionnel = code_fonctionnel.split('\n')
    lignes_actuel = code_actuel.split('\n')
    
    # Générer diff unifié
    diff = list(difflib.unified_diff(
        lignes_fonctionnel,
        lignes_actuel,
        fromfile='Version Fonctionnelle',
        tofile='Version Actuelle',
        lineterm=''
    ))
    
    # Analyser le diff
    ajouts = len([l for l in diff if l.startswith('+')])
    suppressions = len([l for l in diff if l.startswith('-')])
    
    return {
        'diff_complet': diff,
        'ajouts': ajouts,
        'suppressions': suppressions,
        'modifications_totales': ajouts + suppressions
    }

def identifier_problemes_specifiques(differences, sections_critiques):
    """Identifie les problèmes spécifiques à corriger"""
    log("🎯 IDENTIFICATION PROBLÈMES SPÉCIFIQUES")
    
    problemes_prioritaires = []
    
    # Analyser les différences
    for diff in differences:
        if diff['gravite'] == 'HAUTE':
            if diff['type'] == 'CONFIG_GRADIO':
                problemes_prioritaires.append({
                    'probleme': 'Configuration Gradio corrompue',
                    'solution': 'Restaurer configuration demo.launch() simple',
                    'priorite': 1
                })
            elif diff['type'] == 'IMPORTS_SUPPRIMES':
                problemes_prioritaires.append({
                    'probleme': 'Imports essentiels manquants',
                    'solution': 'Restaurer imports de base',
                    'priorite': 2
                })
    
    # Analyser les sections critiques
    for section in sections_critiques:
        if section['section'] == 'CHATBOT_CONFIG':
            problemes_prioritaires.append({
                'probleme': 'Configuration chatbot modifiée',
                'solution': 'Restaurer configuration chatbot simple',
                'priorite': 1
            })
        elif section['section'] == 'SEND_MESSAGE':
            problemes_prioritaires.append({
                'probleme': 'Fonction send_message trop complexe',
                'solution': 'Simplifier fonction send_message',
                'priorite': 2
            })
    
    return sorted(problemes_prioritaires, key=lambda x: x['priorite'])

def comparaison_complete():
    """Comparaison complète entre codes"""
    log("🔍 COMPARAISON COMPLÈTE CODES JARVIS")
    print("=" * 60)
    
    # 1. Trouver version fonctionnelle
    version_fonctionnelle = trouver_version_fonctionnelle()
    if not version_fonctionnelle:
        log("❌ Impossible de trouver version fonctionnelle")
        return False
    
    # 2. Charger les codes
    try:
        with open(version_fonctionnelle, 'r', encoding='utf-8') as f:
            code_fonctionnel = f.read()
        
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code_actuel = f.read()
    except Exception as e:
        log(f"❌ Erreur lecture fichiers: {e}")
        return False
    
    log(f"✅ Code fonctionnel: {len(code_fonctionnel.split())} lignes")
    log(f"✅ Code actuel: {len(code_actuel.split())} lignes")
    
    # 3. Analyser différences
    differences = analyser_differences_structurelles(code_fonctionnel, code_actuel)
    sections_critiques = comparer_sections_critiques(code_fonctionnel, code_actuel)
    diff_detaille = generer_diff_detaille(code_fonctionnel, code_actuel)
    
    # 4. Identifier problèmes
    problemes = identifier_problemes_specifiques(differences, sections_critiques)
    
    # 5. Rapport final
    print("\n" + "=" * 60)
    log("📊 RAPPORT COMPARAISON")
    print("=" * 60)
    
    print(f"📄 Différences structurelles: {len(differences)}")
    for diff in differences:
        print(f"   🚨 {diff['type']}: {diff['probleme']}")
    
    print(f"\n🔧 Sections critiques modifiées: {len(sections_critiques)}")
    for section in sections_critiques:
        print(f"   ⚠️  {section['section']}: {section['probleme']}")
    
    print(f"\n📈 Modifications totales: {diff_detaille['modifications_totales']}")
    print(f"   + {diff_detaille['ajouts']} ajouts")
    print(f"   - {diff_detaille['suppressions']} suppressions")
    
    print(f"\n🎯 Problèmes prioritaires: {len(problemes)}")
    for i, probleme in enumerate(problemes, 1):
        print(f"   {i}. {probleme['probleme']}")
        print(f"      💡 Solution: {probleme['solution']}")
    
    # 6. Sauvegarder rapport détaillé
    rapport = {
        'timestamp': datetime.now().isoformat(),
        'version_fonctionnelle': version_fonctionnelle,
        'differences': differences,
        'sections_critiques': sections_critiques,
        'diff_stats': diff_detaille,
        'problemes_prioritaires': problemes
    }
    
    import json
    with open("rapport_comparaison_codes.json", 'w') as f:
        json.dump(rapport, f, indent=2, default=str)
    
    log("📄 Rapport détaillé: rapport_comparaison_codes.json")
    
    return True

if __name__ == "__main__":
    print("🔍 COMPARATEUR DE CODES JARVIS")
    print("Compare le code actuel avec une version fonctionnelle")
    print("=" * 60)
    
    if comparaison_complete():
        print("\n🎉 COMPARAISON TERMINÉE")
        print("Consultez le rapport pour identifier les problèmes exacts")
    else:
        print("\n❌ ÉCHEC de la comparaison")
