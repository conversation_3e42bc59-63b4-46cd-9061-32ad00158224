#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 SUPER CORRECTEUR AUTOMATIQUE JARVIS - JEAN-LUC PASSAVE
Corrige AUTOMATIQUEMENT tous les problèmes et garantit la stabilité du code
"""

import os
import re
import subprocess
import time
import requests
import shutil
from datetime import datetime

class SuperCorrecteurJarvis:
    def __init__(self):
        self.fichier_jarvis = "jarvis_interface_propre.py"
        self.corrections_appliquees = []
        self.backup_path = None
        
    def log(self, message):
        """Log avec timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def creer_backup(self):
        """Crée un backup avant corrections"""
        if os.path.exists(self.fichier_jarvis):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.backup_path = f"{self.fichier_jarvis}.backup_super_{timestamp}"
            shutil.copy2(self.fichier_jarvis, self.backup_path)
            self.log(f"💾 BACKUP: {self.backup_path}")
            return True
        return False
    
    def diagnostic_complet(self):
        """Diagnostic complet du système"""
        self.log("🔍 DIAGNOSTIC COMPLET")
        print("=" * 50)
        
        problemes = []
        
        # 1. Vérifier fichier JARVIS
        if not os.path.exists(self.fichier_jarvis):
            problemes.append("FICHIER_MANQUANT")
            self.log("❌ Fichier JARVIS manquant")
        else:
            self.log("✅ Fichier JARVIS trouvé")
        
        # 2. Vérifier DeepSeek
        try:
            response = requests.get("http://localhost:8000/v1/models", timeout=3)
            if response.status_code == 200:
                self.log("✅ DeepSeek R1 8B opérationnel")
            else:
                problemes.append("DEEPSEEK_ERREUR")
                self.log("❌ DeepSeek erreur HTTP")
        except:
            problemes.append("DEEPSEEK_ARRETE")
            self.log("❌ DeepSeek arrêté")
        
        # 3. Vérifier interface JARVIS
        try:
            response = requests.get("http://localhost:7860", timeout=2)
            if response.status_code == 200:
                self.log("✅ Interface JARVIS active")
            else:
                problemes.append("INTERFACE_ERREUR")
                self.log("❌ Interface JARVIS erreur")
        except:
            problemes.append("INTERFACE_ARRETEE")
            self.log("❌ Interface JARVIS arrêtée")
        
        # 4. Analyser le code
        if os.path.exists(self.fichier_jarvis):
            with open(self.fichier_jarvis, 'r', encoding='utf-8') as f:
                contenu = f.read()
            
            # Problèmes de format
            if 'type="messages"' in contenu:
                problemes.append("FORMAT_MESSAGES")
                self.log("❌ Format messages incompatible")
            
            # CSS problématique
            if 'custom-chatbot' in contenu and len(contenu) > 8000:
                problemes.append("CSS_COMPLEXE")
                self.log("❌ CSS trop complexe")
            
            # Imports inutiles
            imports_problematiques = ['psutil', 'threading', 'uuid', 'subprocess']
            for imp in imports_problematiques:
                if f'import {imp}' in contenu:
                    problemes.append(f"IMPORT_{imp.upper()}")
                    self.log(f"❌ Import inutile: {imp}")
        
        self.log(f"📊 TOTAL PROBLÈMES: {len(problemes)}")
        return problemes
    
    def corriger_format_messages(self):
        """Corrige le format des messages"""
        self.log("🔧 CORRECTION FORMAT MESSAGES")
        
        with open(self.fichier_jarvis, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Forcer format tuples
        contenu = contenu.replace('type="messages"', 'type="tuples"')
        
        # Configuration chatbot simple
        pattern_chatbot = r'conversation_display = gr\.Chatbot\([^)]*\)'
        config_simple = '''conversation_display = gr.Chatbot(
                    label="💬 Conversation avec JARVIS",
                    height=500,
                    show_label=True,
                    show_copy_button=True,
                    type="tuples"
                )'''
        
        contenu = re.sub(pattern_chatbot, config_simple, contenu, flags=re.DOTALL)
        
        with open(self.fichier_jarvis, 'w', encoding='utf-8') as f:
            f.write(contenu)
        
        self.corrections_appliquees.append("FORMAT_MESSAGES")
        self.log("✅ Format messages corrigé")
    
    def corriger_css_simple(self):
        """Applique un CSS simple et fonctionnel"""
        self.log("🎨 CORRECTION CSS")
        
        with open(self.fichier_jarvis, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # CSS minimal et fonctionnel
        css_simple = '''custom_css = """
/* CSS MINIMAL ANTI-PROBLÈMES */
.chatbot { width: 100% !important; }
.message { 
    display: block !important; 
    word-wrap: break-word !important; 
    white-space: pre-wrap !important;
    margin: 8px 0 !important;
    padding: 12px !important;
    border-radius: 12px !important;
}
.user { 
    background: #007bff !important; 
    color: white !important; 
    margin-left: auto !important; 
}
.bot { 
    background: #f8f9fa !important; 
    color: #333 !important; 
    margin-right: auto !important; 
}
"""'''
        
        # Remplacer tout CSS existant
        pattern_css = r'custom_css = """.*?"""'
        contenu = re.sub(pattern_css, css_simple, contenu, flags=re.DOTALL)
        
        with open(self.fichier_jarvis, 'w', encoding='utf-8') as f:
            f.write(contenu)
        
        self.corrections_appliquees.append("CSS_SIMPLE")
        self.log("✅ CSS simplifié")
    
    def nettoyer_imports(self):
        """Nettoie les imports inutiles"""
        self.log("📦 NETTOYAGE IMPORTS")
        
        with open(self.fichier_jarvis, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        imports_a_supprimer = [
            'import threading',
            'import uuid',
            'import subprocess', 
            'import sys',
            'import signal',
            'import atexit',
            'import psutil',
            'from requests.adapters import HTTPAdapter',
            'from urllib3.util.retry import Retry'
        ]
        
        for imp in imports_a_supprimer:
            if imp in contenu:
                contenu = contenu.replace(imp, f'# {imp}  # SUPPRIMÉ AUTO')
                self.corrections_appliquees.append(f"IMPORT_{imp.split()[-1].upper()}")
        
        with open(self.fichier_jarvis, 'w', encoding='utf-8') as f:
            f.write(contenu)
        
        self.log("✅ Imports nettoyés")
    
    def forcer_port_libre(self):
        """Force un port libre pour JARVIS"""
        self.log("🔌 CORRECTION PORT")
        
        # Trouver port libre
        import socket
        for port in range(7860, 7880):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    port_libre = port
                    break
            except:
                continue
        else:
            port_libre = 7860
        
        # Appliquer le port
        with open(self.fichier_jarvis, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        contenu = re.sub(r'server_port=\d+', f'server_port={port_libre}', contenu)
        
        with open(self.fichier_jarvis, 'w', encoding='utf-8') as f:
            f.write(contenu)
        
        self.corrections_appliquees.append("PORT_LIBRE")
        self.log(f"✅ Port {port_libre} configuré")
        return port_libre
    
    def demarrer_deepseek(self):
        """Démarre DeepSeek si nécessaire"""
        self.log("🚀 VÉRIFICATION DEEPSEEK")
        
        try:
            response = requests.get("http://localhost:8000/v1/models", timeout=3)
            if response.status_code == 200:
                self.log("✅ DeepSeek déjà actif")
                return True
        except:
            pass
        
        # Chercher script de démarrage
        scripts = ["demarrer_deepseek_optimise.sh", "demarrer_deepseek.sh"]
        for script in scripts:
            if os.path.exists(script):
                self.log(f"🚀 Démarrage DeepSeek: {script}")
                subprocess.Popen(["bash", script], cwd=".")
                
                # Attendre démarrage
                for i in range(30):
                    time.sleep(1)
                    try:
                        response = requests.get("http://localhost:8000/v1/models", timeout=2)
                        if response.status_code == 200:
                            self.log("✅ DeepSeek démarré")
                            return True
                    except:
                        pass
                break
        
        self.log("⚠️  DeepSeek non démarré - mode dégradé")
        return False
    
    def corriger_tout_automatiquement(self):
        """CORRECTION AUTOMATIQUE COMPLÈTE"""
        self.log("🔧 SUPER CORRECTEUR AUTOMATIQUE JARVIS")
        print("=" * 60)
        
        # 1. Backup
        if not self.creer_backup():
            self.log("❌ Impossible de créer backup")
            return False
        
        # 2. Diagnostic
        problemes = self.diagnostic_complet()
        
        if not problemes:
            self.log("✅ Aucun problème détecté")
            return True
        
        # 3. Corrections automatiques
        self.log("🔧 APPLICATION CORRECTIONS...")
        
        if any("FORMAT" in p for p in problemes):
            self.corriger_format_messages()
        
        if any("CSS" in p for p in problemes):
            self.corriger_css_simple()
        
        if any("IMPORT" in p for p in problemes):
            self.nettoyer_imports()
        
        port = self.forcer_port_libre()
        
        # 4. Démarrer DeepSeek
        deepseek_ok = self.demarrer_deepseek()
        
        # 5. Résumé
        self.log("🎉 CORRECTIONS TERMINÉES")
        print("=" * 40)
        self.log(f"📁 Backup: {self.backup_path}")
        self.log(f"🔧 Corrections: {len(self.corrections_appliquees)}")
        for correction in self.corrections_appliquees:
            self.log(f"   ✅ {correction}")
        
        self.log(f"🌐 Interface: http://localhost:{port}")
        self.log(f"🤖 DeepSeek: {'✅ OK' if deepseek_ok else '❌ KO'}")
        
        return True

    def lancer_jarvis_corrige(self):
        """Lance JARVIS après corrections"""
        self.log("🚀 LANCEMENT JARVIS CORRIGÉ")

        try:
            # Commande de lancement
            if os.path.exists("venv_deepseek/bin/activate"):
                cmd = "source venv_deepseek/bin/activate && python jarvis_interface_propre.py"
            else:
                cmd = "python3 jarvis_interface_propre.py"

            self.log(f"💻 Commande: {cmd}")

            # Lancer en arrière-plan
            process = subprocess.Popen(
                cmd,
                shell=True,
                cwd=".",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # Attendre que l'interface soit prête
            for i in range(20):
                time.sleep(1)
                try:
                    response = requests.get("http://localhost:7860", timeout=2)
                    if response.status_code == 200:
                        self.log("✅ Interface JARVIS active")
                        return True
                except:
                    pass

            self.log("⚠️  Interface prend du temps à démarrer")
            return True

        except Exception as e:
            self.log(f"❌ Erreur lancement: {e}")
            return False

    def correction_et_lancement_complet(self):
        """Correction complète + lancement automatique"""
        self.log("🎯 CORRECTION ET LANCEMENT COMPLET")
        print("=" * 60)

        # 1. Corriger tout
        if not self.corriger_tout_automatiquement():
            return False

        # 2. Lancer JARVIS
        if self.lancer_jarvis_corrige():
            self.log("🎉 JARVIS OPÉRATIONNEL !")
            self.log("🌐 Interface: http://localhost:7860")
            self.log("💬 Vous pouvez maintenant envoyer des messages")
            return True

        return False

if __name__ == "__main__":
    correcteur = SuperCorrecteurJarvis()

    print("🔧 SUPER CORRECTEUR AUTOMATIQUE JARVIS")
    print("Corrige TOUS les problèmes et lance JARVIS automatiquement")
    print("=" * 60)

    if correcteur.correction_et_lancement_complet():
        print("\n🎉 SUCCÈS TOTAL !")
        print("JARVIS est maintenant opérationnel avec toutes les corrections")
        print("🌐 Ouvrez http://localhost:7860 dans votre navigateur")
    else:
        print("\n❌ Erreur lors du processus complet")
