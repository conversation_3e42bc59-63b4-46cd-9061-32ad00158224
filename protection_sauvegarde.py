#!/usr/bin/env python3
"""
🛡️ PROTECTION SAUVEGARDE JARVIS
Système de protection pour éviter de toucher à la sauvegarde pendant le développement
"""

import os
import shutil
import json
import time
from datetime import datetime

def creer_sauvegarde_travail():
    """Crée une copie de travail séparée de la mémoire thermique"""
    try:
        # Fichiers à protéger
        fichiers_critiques = [
            "thermal_memory_persistent.json",
            "jarvis_interface_propre.py",
            "jarvis_systeme_neuronal_etages.py",
            "jarvis_accelerateur_global_unifie.py"
        ]
        
        # Créer dossier de travail
        dossier_travail = f"TRAVAIL_EN_COURS_{int(time.time())}"
        os.makedirs(dossier_travail, exist_ok=True)
        
        # Créer backup de sécurité
        dossier_backup = f"BACKUP_SECURITE_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(dossier_backup, exist_ok=True)
        
        print(f"🛡️ PROTECTION SAUVEGARDE ACTIVÉE")
        print(f"📁 Dossier travail: {dossier_travail}")
        print(f"💾 Backup sécurité: {dossier_backup}")
        
        # Copier fichiers critiques
        for fichier in fichiers_critiques:
            if os.path.exists(fichier):
                # Backup de sécurité
                shutil.copy2(fichier, os.path.join(dossier_backup, fichier))
                
                # Copie de travail
                fichier_travail = fichier.replace(".py", "_TRAVAIL.py").replace(".json", "_TRAVAIL.json")
                shutil.copy2(fichier, os.path.join(dossier_travail, fichier_travail))
                
                print(f"   ✅ {fichier} → Protégé et copié")
        
        # Créer fichier de protection
        protection_info = {
            "timestamp": datetime.now().isoformat(),
            "dossier_travail": dossier_travail,
            "dossier_backup": dossier_backup,
            "fichiers_proteges": fichiers_critiques,
            "status": "PROTECTION_ACTIVE"
        }
        
        with open("PROTECTION_ACTIVE.json", 'w', encoding='utf-8') as f:
            json.dump(protection_info, f, ensure_ascii=False, indent=2)
        
        return f"""
        <div style="background: linear-gradient(45deg, #4caf50, #2e7d32); color: white; padding: 20px; border-radius: 15px;">
            <h3>🛡️ PROTECTION SAUVEGARDE ACTIVÉE</h3>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <h4>✅ Fichiers protégés :</h4>
                <ul style="font-size: 14px; line-height: 1.6;">
                    <li>💾 thermal_memory_persistent.json</li>
                    <li>🖥️ jarvis_interface_propre.py</li>
                    <li>🧠 jarvis_systeme_neuronal_etages.py</li>
                    <li>🚀 jarvis_accelerateur_global_unifie.py</li>
                </ul>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <h4>📁 Dossiers créés :</h4>
                <p>🔧 <strong>Travail :</strong> {dossier_travail}</p>
                <p>💾 <strong>Backup :</strong> {dossier_backup}</p>
            </div>
            
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px; text-align: center;">
                <h4>🎯 MAINTENANT VOUS POUVEZ CODER EN SÉCURITÉ !</h4>
                <p>Vos fichiers originaux sont protégés</p>
                <p>Travaillez sur les copies dans le dossier TRAVAIL</p>
            </div>
        </div>
        """
        
    except Exception as e:
        return f"❌ Erreur protection: {e}"

def restaurer_depuis_backup():
    """Restaure les fichiers depuis le backup en cas de problème"""
    try:
        if not os.path.exists("PROTECTION_ACTIVE.json"):
            return "❌ Aucune protection active trouvée"
        
        with open("PROTECTION_ACTIVE.json", 'r', encoding='utf-8') as f:
            protection_info = json.load(f)
        
        dossier_backup = protection_info["dossier_backup"]
        
        if not os.path.exists(dossier_backup):
            return "❌ Dossier backup non trouvé"
        
        # Restaurer fichiers
        fichiers_restaures = []
        for fichier in protection_info["fichiers_proteges"]:
            backup_path = os.path.join(dossier_backup, fichier)
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, fichier)
                fichiers_restaures.append(fichier)
        
        return f"""
        <div style="background: linear-gradient(45deg, #ff9800, #f57c00); color: white; padding: 20px; border-radius: 15px;">
            <h3>🔄 RESTAURATION BACKUP RÉUSSIE</h3>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                <h4>✅ Fichiers restaurés :</h4>
                <ul>
                    {''.join(f'<li>{f}</li>' for f in fichiers_restaures)}
                </ul>
            </div>
            <p style="text-align: center; margin-top: 15px;">
                <strong>Vos fichiers originaux sont restaurés !</strong>
            </p>
        </div>
        """
        
    except Exception as e:
        return f"❌ Erreur restauration: {e}"

def verifier_protection():
    """Vérifie l'état de la protection"""
    try:
        if os.path.exists("PROTECTION_ACTIVE.json"):
            with open("PROTECTION_ACTIVE.json", 'r', encoding='utf-8') as f:
                protection_info = json.load(f)
            
            return f"""
            <div style="background: linear-gradient(45deg, #2196f3, #1976d2); color: white; padding: 20px; border-radius: 15px;">
                <h3>🔍 ÉTAT PROTECTION</h3>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                    <p>🛡️ <strong>Statut :</strong> {protection_info['status']}</p>
                    <p>⏰ <strong>Créée :</strong> {protection_info['timestamp']}</p>
                    <p>📁 <strong>Dossier travail :</strong> {protection_info['dossier_travail']}</p>
                    <p>💾 <strong>Backup :</strong> {protection_info['dossier_backup']}</p>
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <strong>✅ VOS FICHIERS SONT PROTÉGÉS</strong>
                </div>
            </div>
            """
        else:
            return """
            <div style="background: linear-gradient(45deg, #f44336, #d32f2f); color: white; padding: 20px; border-radius: 15px;">
                <h3>⚠️ AUCUNE PROTECTION ACTIVE</h3>
                <p style="text-align: center;">
                    Cliquez sur "🛡️ Activer Protection" pour sécuriser vos fichiers
                </p>
            </div>
            """
            
    except Exception as e:
        return f"❌ Erreur vérification: {e}"

if __name__ == "__main__":
    print("🛡️ SYSTÈME DE PROTECTION SAUVEGARDE")
    print("=" * 40)
    
    choix = input("1. Activer protection\n2. Vérifier état\n3. Restaurer backup\nChoix: ")
    
    if choix == "1":
        result = creer_sauvegarde_travail()
        print(result)
    elif choix == "2":
        result = verifier_protection()
        print(result)
    elif choix == "3":
        result = restaurer_depuis_backup()
        print(result)
    else:
        print("❌ Choix invalide")
