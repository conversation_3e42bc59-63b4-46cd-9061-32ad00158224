#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR PROBLÈMES RESTANTS - JEAN-LUC PASSAVE
Corrige les problèmes spécifiques identifiés par le scanner
"""

import re
import os
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🔧 [{timestamp}] {message}")

def corriger_fonctions_dupliquees():
    """Supprime les fonctions dupliquées"""
    log("🔧 CORRECTION FONCTIONS DUPLIQUÉES")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Trouver et supprimer les doublons
        fonctions_a_nettoyer = ['generate_complete_memory_summary', 'clear_conversation']
        
        for fonction in fonctions_a_nettoyer:
            # Trouver toutes les occurrences de la fonction
            pattern = rf'^def {fonction}\(.*?\n(?:.*\n)*?(?=^def |\n$|\Z)'
            matches = list(re.finditer(pattern, code, re.MULTILINE))
            
            if len(matches) > 1:
                log(f"🗑️  Suppression doublons de {fonction} ({len(matches)} trouvées)")
                
                # Garder seulement la première occurrence
                for match in reversed(matches[1:]):  # Supprimer de la fin vers le début
                    code = code[:match.start()] + code[match.end():]
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log("✅ Fonctions dupliquées supprimées")
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction fonctions: {e}")
        return False

def corriger_format_messages():
    """Corrige le format des messages de conversation"""
    log("🔧 CORRECTION FORMAT MESSAGES")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Patterns problématiques à corriger
        corrections = [
            # Format incorrect vers format correct
            (r'conversation_history\.append\(\[message, None\]\)', 
             'conversation_history.append([message, ""])'),
            
            (r'conversation_history\.append\(\[user_message, agent_response\]\)', 
             'conversation_history.append([user_message, agent_response])'),
            
            # Correction des retours de fonction
            (r'return conversation_history, ""', 
             'return conversation_history, ""'),
        ]
        
        corrections_appliquees = 0
        
        for pattern, replacement in corrections:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                corrections_appliquees += 1
                log(f"✅ Pattern corrigé: {pattern[:50]}...")
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log(f"✅ {corrections_appliquees} corrections de format appliquées")
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction format: {e}")
        return False

def optimiser_taille_code():
    """Optimise la taille du code en supprimant les redondances"""
    log("🔧 OPTIMISATION TAILLE CODE")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        taille_initiale = len(code.split('\n'))
        
        # Supprimer commentaires excessifs
        code = re.sub(r'^\s*#.*$', '', code, flags=re.MULTILINE)
        
        # Supprimer lignes vides multiples
        code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)
        
        # Supprimer espaces en fin de ligne
        code = re.sub(r' +$', '', code, flags=re.MULTILINE)
        
        # Supprimer imports inutilisés (patterns courants)
        imports_inutiles = [
            r'^import threading.*$',
            r'^import uuid.*$', 
            r'^import subprocess.*$',
            r'^import psutil.*$',
            r'^from urllib3.*$',
            r'^from requests\.adapters.*$'
        ]
        
        for pattern in imports_inutiles:
            if re.search(pattern, code, re.MULTILINE):
                code = re.sub(pattern, '', code, flags=re.MULTILINE)
                log(f"🗑️  Import supprimé: {pattern}")
        
        taille_finale = len(code.split('\n'))
        reduction = taille_initiale - taille_finale
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log(f"✅ Code optimisé: {taille_initiale} → {taille_finale} lignes (-{reduction})")
        return True
        
    except Exception as e:
        log(f"❌ Erreur optimisation: {e}")
        return False

def verifier_syntaxe_python():
    """Vérifie que la syntaxe Python est correcte"""
    log("🧪 VÉRIFICATION SYNTAXE PYTHON")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Test de compilation basique
        try:
            compile(code, "jarvis_interface_propre.py", "exec")
            log("✅ Syntaxe Python valide")
            return True
        except SyntaxError as e:
            log(f"❌ Erreur syntaxe ligne {e.lineno}: {e.msg}")
            return False
        
    except Exception as e:
        log(f"❌ Erreur vérification: {e}")
        return False

def tester_lancement_rapide():
    """Test rapide de lancement de l'interface"""
    log("🚀 TEST LANCEMENT RAPIDE")
    
    try:
        # Import test
        import sys
        sys.path.insert(0, '.')
        
        # Test import du module
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("jarvis_test", "jarvis_interface_propre.py")
            module = importlib.util.module_from_spec(spec)
            
            log("✅ Module importable")
            return True
            
        except Exception as e:
            log(f"❌ Erreur import: {e}")
            return False
        
    except Exception as e:
        log(f"❌ Erreur test: {e}")
        return False

def correction_complete_problemes():
    """Correction complète de tous les problèmes restants"""
    log("🔧 CORRECTION COMPLÈTE PROBLÈMES RESTANTS")
    print("=" * 60)
    
    # Backup avant corrections
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"jarvis_interface_propre_backup_final_{timestamp}.py"
    
    try:
        import shutil
        shutil.copy2("jarvis_interface_propre.py", backup_file)
        log(f"💾 Backup final: {backup_file}")
    except:
        log("⚠️  Impossible de créer backup")
    
    corrections_reussies = 0
    
    # 1. Corriger fonctions dupliquées
    log("ÉTAPE 1: Fonctions dupliquées")
    if corriger_fonctions_dupliquees():
        corrections_reussies += 1
    
    # 2. Corriger format messages
    log("ÉTAPE 2: Format messages")
    if corriger_format_messages():
        corrections_reussies += 1
    
    # 3. Optimiser taille
    log("ÉTAPE 3: Optimisation taille")
    if optimiser_taille_code():
        corrections_reussies += 1
    
    # 4. Vérifier syntaxe
    log("ÉTAPE 4: Vérification syntaxe")
    if verifier_syntaxe_python():
        corrections_reussies += 1
    
    # 5. Test lancement
    log("ÉTAPE 5: Test lancement")
    if tester_lancement_rapide():
        corrections_reussies += 1
    
    # Résultat final
    print("\n" + "=" * 60)
    log("📊 RÉSULTAT FINAL")
    print("=" * 60)
    
    print(f"✅ Corrections réussies: {corrections_reussies}/5")
    
    if corrections_reussies >= 4:
        print("🎉 CORRECTION RÉUSSIE !")
        print("Le code est maintenant corrigé et fonctionnel")
        print("🚀 Vous pouvez lancer JARVIS")
        return True
    else:
        print("⚠️  CORRECTIONS PARTIELLES")
        print("Certains problèmes subsistent")
        return False

if __name__ == "__main__":
    print("🔧 CORRECTEUR PROBLÈMES RESTANTS")
    print("Corrige les problèmes spécifiques identifiés")
    print("=" * 50)
    
    if correction_complete_problemes():
        print("\n🎉 TOUS LES PROBLÈMES CORRIGÉS")
        print("JARVIS est prêt à être lancé")
    else:
        print("\n❌ PROBLÈMES PERSISTANTS")
        print("Vérification manuelle nécessaire")
