#!/usr/bin/env python3
"""
🚀 JARVIS SIMPLE LAUNCH
Interface minimaliste qui se lance IMMÉDIATEMENT
"""

import gradio as gr
import requests
import json
import time
import os

# Configuration simple
DEEPSEEK_URL = "http://localhost:8000/v1/chat/completions"
MEMORY_FILE = "thermal_memory_persistent.json"

def chat_simple(message):
    """Chat simple avec DeepSeek"""
    if not message.strip():
        return "Tapez un message"
    
    try:
        payload = {
            "model": "deepseek-ai/DeepSeek-R1-0528",
            "messages": [
                {"role": "system", "content": "Tu es JARVIS, assistant <PERSON><PERSON> <PERSON>. Réponds en français."},
                {"role": "user", "content": message}
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }
        
        response = requests.post(DEEPSEEK_URL, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            agent_response = data['choices'][0]['message']['content']
            
            # Sauvegarde simple
            save_simple(message, agent_response)
            
            return agent_response
        else:
            return f"❌ Erreur DeepSeek: {response.status_code}"
            
    except Exception as e:
        return f"❌ Erreur: {str(e)}"

def save_simple(user_msg, agent_resp):
    """Sauvegarde ultra-simple"""
    try:
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            data = {"conversations": []}
        
        # Structure simple
        if "conversations" not in data:
            data["conversations"] = []
        
        timestamp = time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        
        data["conversations"].extend([
            {
                "id": int(time.time() * 1000),
                "timestamp": timestamp,
                "sender": "user",
                "content": user_msg,
                "agent": "agent1"
            },
            {
                "id": int(time.time() * 1000) + 1,
                "timestamp": timestamp,
                "sender": "agent1",
                "content": agent_resp,
                "agent": "agent1"
            }
        ])
        
        data["lastUpdate"] = timestamp
        data["totalEntries"] = len(data["conversations"])
        
        with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
            
    except Exception as e:
        print(f"❌ Erreur sauvegarde: {e}")

def get_stats():
    """Stats simples"""
    try:
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            conversations = data.get('conversations', [])
            size_mb = os.path.getsize(MEMORY_FILE) / 1024 / 1024
            
            # QI simple basé sur conversations
            qi = 341 + (len(conversations) * 0.3)
            
            return f"""
            <div style="background: linear-gradient(45deg, #1a237e, #3f51b5); color: white; padding: 15px; border-radius: 10px; text-align: center;">
                <h3>🧠 JARVIS STATS</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px;">
                        <h4>🎯 QI</h4>
                        <p style="font-size: 1.5em; margin: 0;">{qi:.1f}</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px;">
                        <h4>💬 Conversations</h4>
                        <p style="font-size: 1.5em; margin: 0;">{len(conversations)}</p>
                    </div>
                </div>
                <p style="margin: 10px 0 0 0; font-size: 0.9em;">💾 {size_mb:.2f} MB • ⏰ {time.strftime('%H:%M:%S')}</p>
            </div>
            """
        else:
            return "❌ Pas de mémoire trouvée"
    except Exception as e:
        return f"❌ Erreur: {e}"

def test_deepseek():
    """Test DeepSeek"""
    try:
        response = requests.get("http://localhost:8000/v1/models", timeout=5)
        if response.status_code == 200:
            return "✅ DeepSeek R1 8B opérationnel"
        else:
            return f"❌ DeepSeek erreur: {response.status_code}"
    except Exception as e:
        return f"❌ DeepSeek non accessible: {e}"

# Interface ultra-simple
with gr.Blocks(title="🚀 JARVIS SIMPLE", theme=gr.themes.Soft()) as demo:
    
    gr.HTML("""
    <div style="text-align: center; background: linear-gradient(45deg, #1976d2, #42a5f5); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
        <h1>🚀 JARVIS SIMPLE</h1>
        <p>Interface immédiate • Mémoire préservée • Jean-Luc Passave</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=2):
            # Chat
            chatbot = gr.Chatbot(
                label="💬 Conversation JARVIS",
                height=400,
                show_copy_button=True
            )
            
            with gr.Row():
                message_input = gr.Textbox(
                    label="Votre message",
                    placeholder="Tapez votre message...",
                    scale=4
                )
                send_btn = gr.Button("📤", variant="primary")
            
            clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
        
        with gr.Column(scale=1):
            # Stats et contrôles
            stats_display = gr.HTML(value=get_stats())
            
            gr.HTML("<br>")
            test_btn = gr.Button("🔗 Test DeepSeek", variant="secondary")
            refresh_btn = gr.Button("🔄 Actualiser", variant="secondary")
            
            status_output = gr.HTML(
                value="""
                <div style="background: #4caf50; color: white; padding: 10px; border-radius: 5px; text-align: center;">
                    <h4>🟢 JARVIS PRÊT</h4>
                    <p>Interface simple active</p>
                </div>
                """
            )
    
    # Événements
    def chat_response(message, history):
        if not message.strip():
            return history, ""
        
        # Ajouter message utilisateur
        history.append([message, None])
        
        # Obtenir réponse
        response = chat_simple(message)
        
        # Ajouter réponse
        history[-1][1] = response
        
        return history, ""
    
    send_btn.click(
        chat_response,
        inputs=[message_input, chatbot],
        outputs=[chatbot, message_input]
    )
    
    message_input.submit(
        chat_response,
        inputs=[message_input, chatbot],
        outputs=[chatbot, message_input]
    )
    
    clear_btn.click(lambda: [], outputs=[chatbot])
    
    test_btn.click(test_deepseek, outputs=[status_output])
    refresh_btn.click(get_stats, outputs=[stats_display])

if __name__ == "__main__":
    print("🚀 JARVIS SIMPLE LAUNCH")
    print("=" * 30)
    
    # Test DeepSeek
    deepseek_status = test_deepseek()
    print(f"🔗 {deepseek_status}")
    
    # Trouver port libre
    import socket
    def find_free_port():
        for port in range(7865, 7870):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('', port))
                    return port
            except:
                continue
        return 7865
    
    port = find_free_port()
    print(f"🌐 Lancement sur port: {port}")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=port,
        share=False,
        show_error=True,
        quiet=False
    )
