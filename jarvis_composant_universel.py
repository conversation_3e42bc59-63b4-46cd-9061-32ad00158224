#!/usr/bin/env python3
"""
🌐 COMPOSANT UNIVERSEL JARVIS
Éléments qui apparaissent sur TOUTES les fenêtres
Créé pour Jean-Luc Passave
"""

import gradio as gr
import webbrowser
from datetime import datetime

# ============================================================================
# COMPOSANT UNIVERSEL POUR TOUTES LES FENÊTRES
# ============================================================================

def create_universal_jarvis_component():
    """Crée le composant JARVIS universel qui apparaît sur toutes les fenêtres"""
    
    with gr.Column():
        # BARRE DE STATUT JARVIS UNIVERSELLE
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 8px 15px; border-radius: 10px; margin: 10px 0; display: flex; align-items: center; justify-content: space-between;">
            <div style="display: flex; align-items: center;">
                <div style="width: 12px; height: 12px; background: #fff; border-radius: 50%; margin-right: 10px; animation: pulse 2s infinite;"></div>
                <span style="font-weight: bold;">🤖 JARVIS ACTIF</span>
            </div>
            <div style="font-size: 0.9em;">
                <span>💾 Mémoire: 1,247 | 🧠 QI: 89 | ⚡ Neurones: 4,064</span>
            </div>
        </div>
        <style>
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        </style>
        """)
        
        # CONTRÔLES RAPIDES UNIVERSELS
        with gr.Row():
            with gr.Column(scale=3):
                # CHAT RAPIDE AVEC JARVIS
                quick_chat = gr.Chatbot(
                    value=[("🤖 JARVIS", "Je suis disponible dans cette fenêtre pour vous aider.")],
                    height=150,
                    label="💬 Chat Rapide JARVIS",
                    type="messages"
                )
                
                with gr.Row():
                    quick_input = gr.Textbox(
                        placeholder="Question rapide à JARVIS...",
                        label="💬 Message",
                        scale=4
                    )
                    quick_send_btn = gr.Button("📤", variant="primary", scale=1)
            
            with gr.Column(scale=1):
                # CONTRÔLES MULTIMÉDIA UNIVERSELS
                gr.HTML("<h4 style='margin: 0 0 10px 0; color: #333;'>🎛️ Contrôles</h4>")
                
                with gr.Row():
                    universal_mic_btn = gr.Button("🎤", size="sm", variant="secondary")
                    universal_speaker_btn = gr.Button("🔊", size="sm", variant="secondary")
                
                with gr.Row():
                    universal_camera_btn = gr.Button("📹", size="sm", variant="secondary")
                    universal_web_btn = gr.Button("🌐", size="sm", variant="secondary")
                
                # NAVIGATION UNIVERSELLE
                gr.HTML("<h4 style='margin: 15px 0 10px 0; color: #333;'>🚀 Navigation</h4>")
                
                with gr.Column():
                    main_comm_btn = gr.Button("💬 Communication", size="sm", variant="primary")
                    dashboard_btn = gr.Button("🏠 Dashboard", size="sm", variant="secondary")
                    code_btn = gr.Button("💻 Code", size="sm", variant="secondary")
                    thoughts_btn = gr.Button("🧠 Pensées", size="sm", variant="secondary")
        
        # INDICATEUR D'ACTIVITÉ JARVIS UNIVERSEL
        activity_status = gr.HTML("""
        <div style="background: #f0f8ff; padding: 10px; border-radius: 8px; border-left: 4px solid #2196F3; margin: 10px 0;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <strong style="color: #1976d2;">⏳ JARVIS Status:</strong>
                    <span style="color: #4CAF50;">Prêt à vous aider</span>
                </div>
                <div style="font-size: 0.8em; color: #666;">
                    🕐 Dernière activité: Il y a 2s
                </div>
            </div>
        </div>
        """)
        
        # FONCTIONS UNIVERSELLES
        def send_quick_message(message, history):
            """Envoie un message rapide à JARVIS"""
            if message.strip():
                history.append({"role": "user", "content": message})
                
                # Réponse contextuelle selon la fenêtre
                response = f"🤖 JARVIS: J'ai reçu votre message '{message}'. Je suis disponible dans cette fenêtre pour vous aider avec les fonctions spécifiques de ce module."
                
                history.append({"role": "assistant", "content": response})
                return history, ""
            return history, message
        
        def activate_universal_control(control_type):
            """Active un contrôle universel"""
            responses = {
                "mic": "🎤 Microphone activé sur cette fenêtre",
                "speaker": "🔊 Haut-parleur activé - JARVIS peut parler",
                "camera": "📹 Caméra activée - Vision JARVIS opérationnelle",
                "web": "🌐 Recherche web disponible depuis cette fenêtre"
            }
            return f"<div style='background: #e8f5e8; padding: 10px; border-radius: 5px; color: #2e7d32;'>{responses.get(control_type, 'Contrôle activé')}</div>"
        
        def navigate_to_window(window_type):
            """Navigation vers une fenêtre spécifique"""
            ports = {
                "communication": 7865,
                "dashboard": 7867,
                "code": 7868,
                "thoughts": 7869,
                "config": 7870,
                "whatsapp": 7871,
                "security": 7872,
                "monitoring": 7873,
                "memory": 7874,
                "creative": 7875,
                "music": 7876,
                "system": 7877
            }
            
            if window_type in ports:
                url = f"http://localhost:{ports[window_type]}"
                webbrowser.open(url)
                return f"🚀 Ouverture {window_type}"
            return "❌ Fenêtre non trouvée"
        
        # CONNEXIONS UNIVERSELLES
        quick_send_btn.click(
            fn=send_quick_message,
            inputs=[quick_input, quick_chat],
            outputs=[quick_chat, quick_input]
        )
        
        universal_mic_btn.click(
            fn=lambda: activate_universal_control("mic"),
            outputs=[activity_status]
        )
        
        universal_speaker_btn.click(
            fn=lambda: activate_universal_control("speaker"),
            outputs=[activity_status]
        )
        
        universal_camera_btn.click(
            fn=lambda: activate_universal_control("camera"),
            outputs=[activity_status]
        )
        
        universal_web_btn.click(
            fn=lambda: activate_universal_control("web"),
            outputs=[activity_status]
        )
        
        main_comm_btn.click(
            fn=lambda: navigate_to_window("communication"),
            outputs=[]
        )
        
        dashboard_btn.click(
            fn=lambda: navigate_to_window("dashboard"),
            outputs=[]
        )
        
        code_btn.click(
            fn=lambda: navigate_to_window("code"),
            outputs=[]
        )
        
        thoughts_btn.click(
            fn=lambda: navigate_to_window("thoughts"),
            outputs=[]
        )
    
    return {
        "quick_chat": quick_chat,
        "quick_input": quick_input,
        "activity_status": activity_status,
        "buttons": {
            "mic": universal_mic_btn,
            "speaker": universal_speaker_btn,
            "camera": universal_camera_btn,
            "web": universal_web_btn,
            "main_comm": main_comm_btn,
            "dashboard": dashboard_btn,
            "code": code_btn,
            "thoughts": thoughts_btn
        }
    }

# ============================================================================
# COMPOSANT INDICATEUR DE TRAVAIL JARVIS
# ============================================================================

def create_working_indicator():
    """Crée l'indicateur de travail JARVIS qui apparaît partout"""
    
    return gr.HTML("""
    <div id="jarvis-working-indicator" style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        padding: 10px 15px;
        border-radius: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 1000;
        display: flex;
        align-items: center;
        font-weight: bold;
        animation: slideIn 0.5s ease-out;
    ">
        <div style="
            width: 16px;
            height: 16px;
            border: 2px solid white;
            border-top: 2px solid transparent;
            border-radius: 50%;
            margin-right: 10px;
            animation: spin 1s linear infinite;
        "></div>
        <span>🤖 JARVIS travaille...</span>
    </div>
    
    <style>
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    </style>
    """)

# ============================================================================
# COMPOSANT STATUT SYSTÈME UNIVERSEL
# ============================================================================

def create_universal_status():
    """Crée le statut système qui apparaît sur toutes les fenêtres"""
    
    return gr.HTML("""
    <div style="background: #f8f9fa; padding: 10px; border-radius: 8px; border: 1px solid #dee2e6; margin: 10px 0;">
        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9em;">
            <div>
                <span style="color: #4CAF50;">●</span> <strong>Système:</strong> Opérationnel
            </div>
            <div>
                <span style="color: #2196F3;">●</span> <strong>Mémoire:</strong> 1,247 entrées
            </div>
            <div>
                <span style="color: #FF9800;">●</span> <strong>Sync T7:</strong> Auto 30s
            </div>
            <div>
                <span style="color: #9C27B0;">●</span> <strong>QI:</strong> 89
            </div>
        </div>
    </div>
    """)

# ============================================================================
# FONCTION D'INTÉGRATION UNIVERSELLE
# ============================================================================

def integrate_universal_components():
    """Intègre tous les composants universels"""
    
    # Séparateur
    gr.HTML("<hr style='margin: 20px 0; border: 1px solid #e0e0e0;'>")
    
    # Titre section universelle
    gr.HTML("""
    <div style="text-align: center; background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 10px; border-radius: 10px; margin: 10px 0;">
        <h3 style="margin: 0;">🌐 JARVIS UNIVERSEL - Disponible sur toutes les fenêtres</h3>
    </div>
    """)
    
    # Composants universels
    universal_components = create_universal_jarvis_component()
    working_indicator = create_working_indicator()
    universal_status = create_universal_status()
    
    return universal_components, working_indicator, universal_status
