#!/usr/bin/env python3
"""
🧹 NETTOYEUR CONNEXIONS MULTIPLES JARVIS
Supprime les connexions en double qui causent des conflits
Créé pour Jean-Luc Passave
"""

import re
import os
from datetime import datetime

def nettoyer_connexions_multiples():
    """Nettoyer les connexions multiples dans l'interface"""
    print("🧹 ================================")
    print("🤖 NETTOYAGE CONNEXIONS MULTIPLES")
    print("🧹 ================================")
    
    if not os.path.exists("jarvis_interface_propre.py"):
        print("❌ Fichier jarvis_interface_propre.py non trouvé")
        return False
    
    with open("jarvis_interface_propre.py", 'r') as f:
        contenu = f.read()
    
    # Créer une sauvegarde
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"jarvis_interface_propre.py.backup_nettoyage_{timestamp}"
    
    with open(backup_name, 'w') as f:
        f.write(contenu)
    
    print(f"💾 Sauvegarde créée: {backup_name}")
    
    # Rechercher toutes les connexions de boutons
    pattern_connexions = r'(\w+)\.click\(\s*\n?\s*fn=([^,\)]+)[^)]*\)'
    connexions = re.findall(pattern_connexions, contenu, re.MULTILINE | re.DOTALL)
    
    print(f"🔗 Connexions trouvées: {len(connexions)}")
    
    # Compter les connexions par bouton
    connexions_par_bouton = {}
    for nom_bouton, fonction in connexions:
        if nom_bouton not in connexions_par_bouton:
            connexions_par_bouton[nom_bouton] = []
        connexions_par_bouton[nom_bouton].append(fonction)
    
    # Identifier les connexions multiples
    connexions_multiples = {k: v for k, v in connexions_par_bouton.items() if len(v) > 1}
    
    print(f"⚠️ Boutons avec connexions multiples: {len(connexions_multiples)}")
    
    if not connexions_multiples:
        print("✅ Aucune connexion multiple trouvée")
        return True
    
    # Supprimer les connexions en double (garder seulement la première)
    contenu_nettoye = contenu
    
    for nom_bouton, fonctions in connexions_multiples.items():
        print(f"🧹 Nettoyage {nom_bouton}: {len(fonctions)} connexions → 1")
        
        # Trouver toutes les occurrences de connexions pour ce bouton
        pattern_bouton = rf'{re.escape(nom_bouton)}\.click\(\s*\n?\s*fn=[^)]*\)'
        matches = list(re.finditer(pattern_bouton, contenu_nettoye, re.MULTILINE | re.DOTALL))
        
        # Supprimer toutes les connexions sauf la première
        for match in reversed(matches[1:]):  # Commencer par la fin pour ne pas décaler les indices
            debut, fin = match.span()
            contenu_nettoye = contenu_nettoye[:debut] + contenu_nettoye[fin:]
    
    # Sauvegarder le fichier nettoyé
    with open("jarvis_interface_propre.py", 'w') as f:
        f.write(contenu_nettoye)
    
    print(f"✅ Nettoyage terminé")
    print(f"🔄 Redémarrez JARVIS pour voir les changements")
    
    return True

def verifier_nettoyage():
    """Vérifier que le nettoyage a fonctionné"""
    print(f"\n🔍 VÉRIFICATION DU NETTOYAGE...")
    
    if not os.path.exists("jarvis_interface_propre.py"):
        print("❌ Fichier non trouvé")
        return False
    
    with open("jarvis_interface_propre.py", 'r') as f:
        contenu = f.read()
    
    # Rechercher les connexions restantes
    pattern_connexions = r'(\w+)\.click\('
    connexions = re.findall(pattern_connexions, contenu)
    
    # Compter les connexions par bouton
    connexions_par_bouton = {}
    for nom_bouton in connexions:
        connexions_par_bouton[nom_bouton] = connexions_par_bouton.get(nom_bouton, 0) + 1
    
    # Identifier les connexions multiples restantes
    connexions_multiples = {k: v for k, v in connexions_par_bouton.items() if v > 1}
    
    print(f"🔗 Connexions totales après nettoyage: {len(connexions)}")
    print(f"⚠️ Connexions multiples restantes: {len(connexions_multiples)}")
    
    if connexions_multiples:
        print("🚨 CONNEXIONS MULTIPLES RESTANTES:")
        for bouton, nb in list(connexions_multiples.items())[:10]:
            print(f"  • {bouton}: {nb} connexions")
        return False
    else:
        print("✅ AUCUNE CONNEXION MULTIPLE RESTANTE")
        return True

def main():
    """Fonction principale"""
    print("🧹 NETTOYEUR CONNEXIONS MULTIPLES JARVIS")
    print("=" * 50)
    
    # Nettoyer les connexions multiples
    if nettoyer_connexions_multiples():
        # Vérifier le nettoyage
        if verifier_nettoyage():
            print("\n🎉 NETTOYAGE RÉUSSI !")
            print("✅ Toutes les connexions multiples ont été supprimées")
            print("🔄 Redémarrez JARVIS pour que les boutons fonctionnent correctement")
        else:
            print("\n⚠️ NETTOYAGE PARTIEL")
            print("Certaines connexions multiples persistent")
    else:
        print("\n❌ ÉCHEC DU NETTOYAGE")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
