#!/usr/bin/env python3
"""
🔍 TEST GRADIO SIMPLE
Test si Gradio fonctionne correctement
"""

import gradio as gr
import time

def test_function(message):
    """Fonction de test simple"""
    return f"✅ Test réussi ! Vous avez dit: {message}"

def create_simple_interface():
    """Crée une interface Gradio simple pour test"""
    
    with gr.<PERSON>s(
        title="🔍 Test JARVIS Simple",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 800px !important;
            margin: 0 auto !important;
        }
        """
    ) as interface:
        
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(45deg, #4caf50, #8bc34a); 
                    color: white; border-radius: 15px; margin-bottom: 20px;">
            <h1>🔍 TEST GRADIO SIMPLE</h1>
            <p>Interface de test pour diagnostiquer JARVIS</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column():
                message_input = gr.Textbox(
                    label="💬 Message de test",
                    placeholder="Tapez votre message ici...",
                    lines=2
                )
                
                test_button = gr.But<PERSON>("🧪 Tester", variant="primary")
                
            with gr.Column():
                result_output = gr.HTML(
                    label="📋 Résultat",
                    value="<p>En attente de test...</p>"
                )
        
        # Événements
        test_button.click(
            fn=test_function,
            inputs=[message_input],
            outputs=[result_output]
        )
        
        message_input.submit(
            fn=test_function,
            inputs=[message_input],
            outputs=[result_output]
        )
    
    return interface

if __name__ == "__main__":
    print("🔍 DÉMARRAGE TEST GRADIO SIMPLE")
    print("=" * 40)
    print("🌐 Interface: http://localhost:7868")
    print("🧪 Test de fonctionnement Gradio")
    print("=" * 40)
    
    try:
        interface = create_simple_interface()
        interface.launch(
            server_name="0.0.0.0",
            server_port=7868,
            share=False,
            debug=True,
            show_error=True
        )
    except Exception as e:
        print(f"❌ Erreur lancement interface: {e}")
        import traceback
        traceback.print_exc()
