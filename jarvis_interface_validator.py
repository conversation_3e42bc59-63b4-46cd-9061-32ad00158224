#!/usr/bin/env python3
"""
🔍✅ JARVIS INTERFACE VALIDATOR
Système de validation automatique complet de l'interface JARVIS

Créé pour <PERSON>ave - JARVIS Vision
"""

import os
import json
import time
import inspect
import importlib.util
import ast
import re
from datetime import datetime
from typing import Dict, List, Any

class JarvisInterfaceValidator:
    def __init__(self):
        self.interface_file = "jarvis_interface_propre.py"
        self.validation_results = {
            "buttons": {},
            "functions": {},
            "modules": {},
            "connections": {},
            "errors": [],
            "warnings": [],
            "score": 0,
            "timestamp": None
        }
        
        # LISTE COMPLÈTE DES BOUTONS ATTENDUS
        self.expected_buttons = {
            # Boutons principaux
            "send_btn": "📤 Envoyer",
            "clear_btn": "🗑️ Effacer", 
            "stop_btn": "🛑 Stop",
            "mic_btn": "🎤",
            "speaker_btn": "🔊",
            "camera_btn": "📹",
            
            # Contrôles agents
            "switch_agent_btn": "🤖 Changer Agent",
            "agent2_toggle_btn": "Agent 2",
            "deep_reflection_btn": "🧠 Réflexion Profonde",
            
            # Fonctions avancées
            "voice_interface_btn": "🎤 Activer Interface Vocale",
            "speech_to_text_btn": "🗣️ Reconnaissance Vocale",
            "text_to_speech_btn": "🔊 Synthèse Vocale",
            "read_thoughts_btn": "🧠 Lire Pensées",
            "voice_status_btn": "📊 État Vocal",
            
            # Créativité
            "creative_btn": "🎨 Créativité",
            "music_btn": "🎵 Musique",
            "project_btn": "📋 Projets",
            "inspiration_btn": "💡 Inspiration",
            
            # Sécurité
            "security_btn": "🔐 Sécurité",
            "biometric_btn": "👤 Biométrie",
            "vpn_check_btn": "🔍 Vérifier VPN",
            "vpn_connect_btn": "🔐 Connecter VPN",
            "vpn_report_btn": "📊 Rapport Sécurité",
            
            # T7 Sync
            "t7_check_btn": "🔍 Vérifier T7",
            "t7_sync_btn": "💾 Sync Forcée",
            "t7_toggle_btn": "⏸️ Arrêter Auto-Sync",
            
            # WhatsApp
            "whatsapp_btn": "📱 WhatsApp",
            "whatsapp_send_btn": "📤 Envoyer Message",
            "whatsapp_status_btn": "📊 Statut WhatsApp",
            
            # Monitoring
            "monitoring_btn": "📊 Monitoring",
            "system_info_btn": "💻 Info Système",
            "memory_stats_btn": "🧠 Stats Mémoire",
            
            # Code Assistant
            "code_btn": "💻 Code",
            "execute_btn": "▶️ Exécuter",
            "workspace_btn": "📁 Workspace"
        }
        
        # FONCTIONS ATTENDUES POUR CHAQUE BOUTON
        self.expected_functions = {
            "mic_btn": "quick_voice_input",
            "speaker_btn": "speak_last_response", 
            "camera_btn": "activate_camera_vision",
            "send_btn": "send_message_to_agent",
            "clear_btn": "clear_conversation",
            "stop_btn": "stop_current_generation",
            "switch_agent_btn": "switch_agent",
            "voice_interface_btn": "voice_interface",
            "speech_to_text_btn": "speech_to_text",
            "text_to_speech_btn": "text_to_speech",
            "camera_btn": "camera_interface",
            "read_thoughts_btn": "read_agent_thoughts",
            "voice_status_btn": "voice_status",
            "creative_btn": "creative_interface",
            "music_btn": "music_interface",
            "security_btn": "security_interface",
            "biometric_btn": "biometric_interface",
            "vpn_check_btn": "check_vpn_status_interface",
            "vpn_connect_btn": "connect_vpn_interface",
            "vpn_report_btn": "get_security_report_interface",
            "t7_check_btn": "check_t7_status_interface",
            "t7_sync_btn": "force_t7_sync_interface",
            "t7_toggle_btn": "toggle_t7_auto_sync",
            "whatsapp_btn": "whatsapp_interface",
            "monitoring_btn": "monitoring_interface",
            "code_btn": "code_interface"
        }
        
    def validate_interface_file(self):
        """VALIDATION COMPLÈTE DU FICHIER INTERFACE"""
        print("🔍 Démarrage validation complète de l'interface JARVIS...")
        
        if not os.path.exists(self.interface_file):
            self.validation_results["errors"].append(f"❌ Fichier interface manquant: {self.interface_file}")
            return self.validation_results
        
        try:
            with open(self.interface_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parser le code Python
            tree = ast.parse(content)
            
            # Valider les boutons
            self.validate_buttons(content)
            
            # Valider les fonctions
            self.validate_functions(content, tree)
            
            # Valider les connexions bouton->fonction
            self.validate_connections(content)
            
            # Valider les modules importés
            self.validate_modules(content)
            
            # Calculer le score
            self.calculate_score()
            
            self.validation_results["timestamp"] = datetime.now().isoformat()
            
            print(f"✅ Validation terminée - Score: {self.validation_results['score']}/100")
            
        except Exception as e:
            self.validation_results["errors"].append(f"❌ Erreur parsing: {str(e)}")
        
        return self.validation_results
    
    def validate_buttons(self, content):
        """VALIDER TOUS LES BOUTONS"""
        print("🔍 Validation des boutons...")
        
        for btn_name, btn_text in self.expected_buttons.items():
            # Chercher la définition du bouton
            pattern = rf'{btn_name}\s*=\s*gr\.Button\('
            if re.search(pattern, content):
                self.validation_results["buttons"][btn_name] = {
                    "status": "✅ Défini",
                    "text": btn_text,
                    "found": True
                }
            else:
                self.validation_results["buttons"][btn_name] = {
                    "status": "❌ Manquant",
                    "text": btn_text,
                    "found": False
                }
                self.validation_results["errors"].append(f"❌ Bouton manquant: {btn_name}")
    
    def validate_functions(self, content, tree):
        """VALIDER TOUTES LES FONCTIONS"""
        print("🔍 Validation des fonctions...")
        
        # Extraire toutes les fonctions définies
        defined_functions = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                defined_functions.append(node.name)
        
        for btn_name, func_name in self.expected_functions.items():
            if func_name in defined_functions:
                # Vérifier que la fonction n'est pas vide
                func_pattern = rf'def\s+{func_name}\s*\([^)]*\):.*?return'
                if re.search(func_pattern, content, re.DOTALL):
                    self.validation_results["functions"][func_name] = {
                        "status": "✅ Implémentée",
                        "button": btn_name,
                        "implemented": True
                    }
                else:
                    self.validation_results["functions"][func_name] = {
                        "status": "⚠️ Vide ou incomplète",
                        "button": btn_name,
                        "implemented": False
                    }
                    self.validation_results["warnings"].append(f"⚠️ Fonction incomplète: {func_name}")
            else:
                self.validation_results["functions"][func_name] = {
                    "status": "❌ Manquante",
                    "button": btn_name,
                    "implemented": False
                }
                self.validation_results["errors"].append(f"❌ Fonction manquante: {func_name}")
    
    def validate_connections(self, content):
        """VALIDER LES CONNEXIONS BOUTON->FONCTION"""
        print("🔍 Validation des connexions...")
        
        for btn_name, func_name in self.expected_functions.items():
            # Chercher la connexion .click()
            pattern = rf'{btn_name}\.click\s*\(\s*fn\s*=\s*{func_name}'
            if re.search(pattern, content):
                self.validation_results["connections"][btn_name] = {
                    "status": "✅ Connecté",
                    "function": func_name,
                    "connected": True
                }
            else:
                self.validation_results["connections"][btn_name] = {
                    "status": "❌ Non connecté",
                    "function": func_name,
                    "connected": False
                }
                self.validation_results["errors"].append(f"❌ Connexion manquante: {btn_name} -> {func_name}")
    
    def validate_modules(self, content):
        """VALIDER LES MODULES IMPORTÉS"""
        print("🔍 Validation des modules...")
        
        required_modules = [
            "gradio",
            "requests", 
            "json",
            "time",
            "os",
            "threading",
            "datetime"
        ]
        
        for module in required_modules:
            if f"import {module}" in content or f"from {module}" in content:
                self.validation_results["modules"][module] = {
                    "status": "✅ Importé",
                    "available": True
                }
            else:
                self.validation_results["modules"][module] = {
                    "status": "❌ Manquant",
                    "available": False
                }
                self.validation_results["errors"].append(f"❌ Module manquant: {module}")
    
    def calculate_score(self):
        """CALCULER LE SCORE DE VALIDATION"""
        total_items = 0
        valid_items = 0
        
        # Score boutons
        for btn_data in self.validation_results["buttons"].values():
            total_items += 1
            if btn_data["found"]:
                valid_items += 1
        
        # Score fonctions
        for func_data in self.validation_results["functions"].values():
            total_items += 1
            if func_data["implemented"]:
                valid_items += 1
        
        # Score connexions
        for conn_data in self.validation_results["connections"].values():
            total_items += 1
            if conn_data["connected"]:
                valid_items += 1
        
        # Score modules
        for mod_data in self.validation_results["modules"].values():
            total_items += 1
            if mod_data["available"]:
                valid_items += 1
        
        if total_items > 0:
            self.validation_results["score"] = int((valid_items / total_items) * 100)
        else:
            self.validation_results["score"] = 0
    
    def generate_report(self):
        """GÉNÉRER UN RAPPORT DÉTAILLÉ"""
        results = self.validation_results
        
        report = f"""
🔍✅ RAPPORT DE VALIDATION INTERFACE JARVIS
═══════════════════════════════════════════

📊 SCORE GLOBAL: {results['score']}/100

🔴 ERREURS CRITIQUES: {len(results['errors'])}
⚠️ AVERTISSEMENTS: {len(results['warnings'])}

📱 BOUTONS ({len([b for b in results['buttons'].values() if b['found']])}/{len(results['buttons'])}):
"""
        
        for btn_name, btn_data in results["buttons"].items():
            report += f"  {btn_data['status']} {btn_name}: {btn_data['text']}\n"
        
        report += f"\n🔧 FONCTIONS ({len([f for f in results['functions'].values() if f['implemented']])}/{len(results['functions'])}):\n"
        
        for func_name, func_data in results["functions"].items():
            report += f"  {func_data['status']} {func_name} (bouton: {func_data['button']})\n"
        
        report += f"\n🔗 CONNEXIONS ({len([c for c in results['connections'].values() if c['connected']])}/{len(results['connections'])}):\n"
        
        for btn_name, conn_data in results["connections"].items():
            report += f"  {conn_data['status']} {btn_name} -> {conn_data['function']}\n"
        
        if results["errors"]:
            report += "\n❌ ERREURS À CORRIGER:\n"
            for error in results["errors"]:
                report += f"  {error}\n"
        
        if results["warnings"]:
            report += "\n⚠️ AVERTISSEMENTS:\n"
            for warning in results["warnings"]:
                report += f"  {warning}\n"
        
        report += f"\n📅 Validation effectuée: {results['timestamp']}\n"
        
        return report
    
    def save_report(self, filename="jarvis_validation_report.txt"):
        """SAUVEGARDER LE RAPPORT"""
        report = self.generate_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Sauvegarder aussi en JSON
        json_filename = filename.replace('.txt', '.json')
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Rapport sauvegardé: {filename}")
        print(f"📄 Données JSON: {json_filename}")
        
        return filename

def validate_jarvis_interface():
    """FONCTION PRINCIPALE DE VALIDATION"""
    validator = JarvisInterfaceValidator()
    results = validator.validate_interface_file()
    report_file = validator.save_report()
    
    print("\n" + validator.generate_report())
    
    return results, report_file

if __name__ == "__main__":
    print("🔍✅ JARVIS INTERFACE VALIDATOR")
    print("═" * 50)
    
    results, report_file = validate_jarvis_interface()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"Score: {results['score']}/100")
    print(f"Erreurs: {len(results['errors'])}")
    print(f"Avertissements: {len(results['warnings'])}")
    print(f"Rapport: {report_file}")
