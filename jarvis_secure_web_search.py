#!/usr/bin/env python3
"""
🔍 JARVIS SECURE WEB SEARCH MODULE
Recherches web sécurisées et anonymisées avec protection VPN

Créé pour <PERSON>
"""

import requests
import json
import time
import urllib.parse
from datetime import datetime
import hashlib
import random

# Import du module VPN
try:
    from jarvis_vpn_security import secure_request, get_vpn_status, jarvis_vpn
    VPN_AVAILABLE = True
except ImportError:
    VPN_AVAILABLE = False
    def secure_request(url, method="GET", data=None, headers=None):
        return requests.request(method, url, json=data, headers=headers)

class JarvisSecureWebSearch:
    def __init__(self):
        self.search_engines = {
            "duckduckgo": {
                "url": "https://api.duckduckgo.com/",
                "params": {"format": "json", "no_html": "1", "skip_disambig": "1"},
                "privacy_level": "high"
            },
            "searx": {
                "url": "https://searx.be/search",
                "params": {"format": "json", "safesearch": "1"},
                "privacy_level": "high"
            },
            "startpage": {
                "url": "https://www.startpage.com/sp/search",
                "params": {"format": "json", "prfh": "disable_family_filterEEE1N1N"},
                "privacy_level": "high"
            }
        }
        
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        self.search_history = []
        
    def anonymize_query(self, query):
        """ANONYMISER LA REQUÊTE DE RECHERCHE"""
        # Supprimer les informations personnelles potentielles
        personal_terms = ["Jean-Luc", "Passave", "JARVIS", "localhost", "127.0.0.1"]
        
        anonymized = query
        for term in personal_terms:
            anonymized = anonymized.replace(term, "[REDACTED]")
        
        return anonymized
    
    def get_random_headers(self):
        """GÉNÉRER DES HEADERS ALÉATOIRES POUR L'ANONYMAT"""
        return {
            "User-Agent": random.choice(self.user_agents),
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9,fr;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",  # Do Not Track
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "cross-site",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        }
    
    def secure_duckduckgo_search(self, query, max_results=5):
        """RECHERCHE SÉCURISÉE AVEC DUCKDUCKGO"""
        try:
            # Vérifier le VPN
            if VPN_AVAILABLE:
                vpn_status = get_vpn_status()
                if not vpn_status.get("protected", False):
                    print("⚠️ Recherche sans protection VPN")
            
            # Anonymiser la requête
            safe_query = self.anonymize_query(query)
            
            # Préparer les paramètres
            params = {
                "q": safe_query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1",
                "safesearch": "moderate"
            }
            
            # Headers anonymes
            headers = self.get_random_headers()
            
            # Effectuer la recherche sécurisée
            if VPN_AVAILABLE:
                response = secure_request(
                    "https://api.duckduckgo.com/",
                    method="GET",
                    headers=headers
                )
                # Ajouter les paramètres à l'URL
                url_with_params = f"https://api.duckduckgo.com/?{urllib.parse.urlencode(params)}"
                response = secure_request(url_with_params, headers=headers)
            else:
                response = requests.get(
                    "https://api.duckduckgo.com/",
                    params=params,
                    headers=headers,
                    timeout=30
                )
            
            if response.status_code == 200:
                data = response.json()
                
                # Extraire les résultats
                results = []
                
                # Résultats instantanés
                if data.get("AbstractText"):
                    results.append({
                        "title": data.get("Heading", "Information"),
                        "snippet": data.get("AbstractText", ""),
                        "url": data.get("AbstractURL", ""),
                        "source": "DuckDuckGo Instant"
                    })
                
                # Résultats de topics
                for topic in data.get("RelatedTopics", [])[:max_results]:
                    if isinstance(topic, dict) and topic.get("Text"):
                        results.append({
                            "title": topic.get("FirstURL", "").split("/")[-1].replace("_", " "),
                            "snippet": topic.get("Text", ""),
                            "url": topic.get("FirstURL", ""),
                            "source": "DuckDuckGo"
                        })
                
                # Log sécurisé
                self.log_search(safe_query, len(results), "DuckDuckGo")
                
                return results[:max_results]
            
            return []
            
        except Exception as e:
            print(f"❌ Erreur recherche DuckDuckGo: {e}")
            return []
    
    def secure_web_search(self, query, max_results=5):
        """RECHERCHE WEB SÉCURISÉE MULTI-MOTEURS"""
        try:
            print(f"🔍 Recherche sécurisée: {query}")
            
            # Vérifier la protection VPN
            if VPN_AVAILABLE:
                vpn_status = get_vpn_status()
                protection_status = "🔐 Protégé" if vpn_status.get("protected") else "⚠️ Non protégé"
                print(f"🛡️ Statut VPN: {protection_status}")
            
            # Essayer DuckDuckGo en premier (plus privé)
            results = self.secure_duckduckgo_search(query, max_results)
            
            if results:
                return {
                    "query": self.anonymize_query(query),
                    "results": results,
                    "source": "DuckDuckGo",
                    "protected": VPN_AVAILABLE and get_vpn_status().get("protected", False),
                    "timestamp": datetime.now().isoformat()
                }
            
            # Fallback vers recherche locale si pas de résultats
            return {
                "query": self.anonymize_query(query),
                "results": [
                    {
                        "title": "Recherche locale recommandée",
                        "snippet": "Pour cette requête, JARVIS recommande d'utiliser sa mémoire thermique ou ses capacités locales.",
                        "url": "local://jarvis",
                        "source": "JARVIS Local"
                    }
                ],
                "source": "Local Fallback",
                "protected": True,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Erreur recherche web sécurisée: {e}")
            return {
                "query": query,
                "results": [],
                "error": str(e),
                "protected": False,
                "timestamp": datetime.now().isoformat()
            }
    
    def log_search(self, query, results_count, engine):
        """ENREGISTRER LA RECHERCHE DE MANIÈRE SÉCURISÉE"""
        # Hash de la requête pour la confidentialité
        query_hash = hashlib.sha256(query.encode()).hexdigest()[:16]
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "query_hash": query_hash,
            "results_count": results_count,
            "engine": engine,
            "vpn_protected": VPN_AVAILABLE and get_vpn_status().get("protected", False)
        }
        
        self.search_history.append(log_entry)
        
        # Garder seulement les 50 dernières recherches
        if len(self.search_history) > 50:
            self.search_history = self.search_history[-50:]
        
        # Sauvegarder de manière sécurisée
        try:
            with open("jarvis_search_log.json", "w") as f:
                json.dump(self.search_history, f, indent=2)
        except:
            pass
    
    def get_search_stats(self):
        """STATISTIQUES DE RECHERCHE SÉCURISÉES"""
        total_searches = len(self.search_history)
        protected_searches = sum(1 for s in self.search_history if s.get("vpn_protected", False))
        
        return {
            "total_searches": total_searches,
            "protected_searches": protected_searches,
            "protection_rate": (protected_searches / total_searches * 100) if total_searches > 0 else 0,
            "engines_used": list(set(s.get("engine", "Unknown") for s in self.search_history)),
            "last_search": self.search_history[-1] if self.search_history else None
        }

# Instance globale
jarvis_search = JarvisSecureWebSearch()

def secure_web_search(query, max_results=5):
    """FONCTION GLOBALE POUR RECHERCHE WEB SÉCURISÉE"""
    return jarvis_search.secure_web_search(query, max_results)

def get_search_stats():
    """FONCTION GLOBALE POUR STATISTIQUES DE RECHERCHE"""
    return jarvis_search.get_search_stats()

if __name__ == "__main__":
    # Test du module
    print("🔍 Test du module de recherche web sécurisée JARVIS")
    
    # Test de recherche
    results = secure_web_search("intelligence artificielle", 3)
    print(f"Résultats: {json.dumps(results, indent=2)}")
    
    # Statistiques
    stats = get_search_stats()
    print(f"Statistiques: {json.dumps(stats, indent=2)}")
