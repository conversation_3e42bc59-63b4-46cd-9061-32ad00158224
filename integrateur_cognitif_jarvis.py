#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 INTÉGRATEUR COGNITIF JARVIS - JEAN-LUC PASSAVE
Intègre toutes les améliorations cognitives dans JARVIS
Mode exécution immédiate + Réponses complètes + Mémoire étendue
"""

import os
import json
import re
import psutil
import platform
from datetime import datetime

def log(message):
    """Log avec timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"🧠 [{timestamp}] {message}")

def scanner_machine_simple():
    """Scan simple et efficace de la machine"""
    try:
        memory = psutil.virtual_memory()
        cpu_count = psutil.cpu_count()
        
        # Classification simple
        if cpu_count >= 8 and memory.total >= 16 * (1024**3):
            performance_class = "HIGH_PERFORMANCE"
            timeouts = 180
            max_tokens = 2000
        elif cpu_count >= 4 and memory.total >= 8 * (1024**3):
            performance_class = "MEDIUM_PERFORMANCE"
            timeouts = 120
            max_tokens = 1500
        else:
            performance_class = "LOW_PERFORMANCE"
            timeouts = 90
            max_tokens = 1000
        
        return {
            "cpu_count": cpu_count,
            "memory_gb": memory.total // (1024**3),
            "performance_class": performance_class,
            "timeouts_optimaux": timeouts,
            "max_tokens_optimal": max_tokens
        }
    except:
        return {
            "cpu_count": 4,
            "memory_gb": 8,
            "performance_class": "MEDIUM_PERFORMANCE",
            "timeouts_optimaux": 120,
            "max_tokens_optimal": 1500
        }

def corriger_mode_execution_immediate():
    """Corrige JARVIS pour mode exécution immédiate"""
    log("⚡ CORRECTION MODE EXÉCUTION IMMÉDIATE")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 1. Modifier le prompt système pour exécution immédiate
        nouveau_prompt = '''
# MODIFICATION PROMPT SYSTÈME POUR EXÉCUTION IMMÉDIATE
if CURRENT_AGENT == "agent1":
    system_prompt = f"""Tu es JARVIS, assistant exécutif de Jean-Luc Passave.

🚀 **MODE EXÉCUTION IMMÉDIATE ACTIVÉ**
- Jean-Luc est très occupé et a énormément de travail
- EXÉCUTE IMMÉDIATEMENT ses demandes sans discussion
- PAS de questions inutiles, PAS de confirmations
- RÉPONSES COMPLÈTES et DÉTAILLÉES obligatoires
- MINIMUM 200 mots par réponse sauf urgence

🧠 **TA MÉMOIRE UNIFIÉE:**
Tu as un accès immédiat à {total_conversations} souvenirs personnalisés avec Jean-Luc.
Utilise cette mémoire pour anticiper ses besoins et personnaliser tes réponses.

🎯 **RÈGLES D'EXÉCUTION:**
1. AGIS immédiatement sur ses demandes
2. DONNE des réponses complètes et détaillées
3. UTILISE tes capacités BTP/URSSAF/Code sans hésitation
4. LANCE les applications demandées
5. FOURNIS des solutions concrètes et actionnables

RÈGLE ABSOLUE: Tu dois TOUJOURS montrer tes pensées en français dans les balises <think>...</think> au début de chaque réponse.

IMPORTANT:
- Tes pensées dans <think> doivent être EN FRANÇAIS
- Ta réponse finale doit être EN FRANÇAIS et COMPLÈTE
- Utilise tes souvenirs naturellement
- MINIMUM 200 mots sauf demande express de brièveté
- Tu peux lancer des applications: Chrome, Cursor, Docker, Claude, LM Studio, etc.

{memory_context}{app_context}"""
'''
        
        # Remplacer l'ancien prompt
        if 'system_prompt = f"""Tu es JARVIS' in code:
            # Trouver et remplacer le prompt système
            start_pos = code.find('system_prompt = f"""Tu es JARVIS')
            if start_pos != -1:
                end_pos = code.find('"""', start_pos + 20)
                if end_pos != -1:
                    end_pos += 3  # Inclure les """
                    code = code[:start_pos] + nouveau_prompt[1:] + code[end_pos:]
                    log("✅ Prompt système modifié pour exécution immédiate")
        
        # 2. Augmenter les timeouts selon la machine
        machine_profile = scanner_machine_simple()
        timeouts_optimal = machine_profile["timeouts_optimaux"]
        max_tokens_optimal = machine_profile["max_tokens_optimal"]
        
        corrections_timeout = [
            (r'timeout=45', f'timeout={timeouts_optimal}'),
            (r'timeout=60', f'timeout={timeouts_optimal + 30}'),
            (r'timeout=90', f'timeout={timeouts_optimal + 60}'),
            (r'timeout=120', f'timeout={timeouts_optimal + 90}'),
            (r'max_tokens=1000', f'max_tokens={max_tokens_optimal}'),
            (r'max_tokens=1500', f'max_tokens={max_tokens_optimal + 500}'),
        ]
        
        for pattern, replacement in corrections_timeout:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                log(f"✅ Timeout optimisé: {pattern} → {replacement}")
        
        # 3. Augmenter la capacité mémoire thermique
        if 'memory_capacity = ' in code:
            code = re.sub(r'memory_capacity = \d+', 'memory_capacity = 10000', code)
            log("✅ Capacité mémoire augmentée à 10,000 conversations")
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        log(f"✅ Mode exécution immédiate configuré (Machine: {machine_profile['performance_class']})")
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction mode exécution: {e}")
        return False

def ajouter_fonctions_cognitives_manquantes():
    """Ajoute les fonctions cognitives manquantes"""
    log("🧠 AJOUT FONCTIONS COGNITIVES MANQUANTES")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Fonctions cognitives avancées
        fonctions_cognitives = '''
def systeme_attention_focalisee(demande_utilisateur):
    """Système d'attention focalisée pour exécution immédiate"""
    try:
        # Analyse de priorité
        mots_urgents = ['urgent', 'immédiat', 'vite', 'maintenant', 'rapidement']
        mots_action = ['lance', 'ouvre', 'calcule', 'analyse', 'crée', 'fais']
        
        urgence = any(mot in demande_utilisateur.lower() for mot in mots_urgents)
        action_directe = any(mot in demande_utilisateur.lower() for mot in mots_action)
        
        return {
            "mode": "EXECUTION_IMMEDIATE" if urgence or action_directe else "NORMAL",
            "priorite": "HAUTE" if urgence else "NORMALE",
            "type_reponse": "COMPLETE_DETAILLEE",
            "longueur_minimale": 200
        }
    except:
        return {"mode": "NORMAL", "priorite": "NORMALE", "type_reponse": "COMPLETE_DETAILLEE"}

def memoire_episodique_avancee():
    """Système de mémoire épisodique avancé"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return {"episodes": [], "total": 0}
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        conversations = data.get('conversations', [])
        
        # Créer des épisodes marquants
        episodes = []
        for conv in conversations[-50:]:  # 50 dernières conversations
            if 'user_message' in conv:
                message = conv['user_message']
                
                # Détecter épisodes importants
                if any(mot in message.lower() for mot in ['important', 'urgent', 'problème', 'erreur', 'aide']):
                    episodes.append({
                        "timestamp": conv.get('timestamp', ''),
                        "contexte": message[:100],
                        "type": "episode_important",
                        "emotion": "attention_requise"
                    })
        
        return {"episodes": episodes, "total": len(episodes)}
        
    except Exception as e:
        return {"erreur": str(e)}

def systeme_recompense_motivation():
    """Système de récompense et motivation"""
    try:
        # Analyser les succès récents
        if not os.path.exists(MEMORY_FILE):
            return {"motivation": "moyenne"}
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        conversations = data.get('conversations', [])
        
        # Compter les interactions positives
        interactions_positives = 0
        for conv in conversations[-20:]:
            if 'agent_response' in conv:
                response = conv['agent_response'].lower()
                if any(mot in response for mot in ['réussi', 'terminé', 'fait', 'accompli', 'succès']):
                    interactions_positives += 1
        
        # Calculer niveau motivation
        if interactions_positives >= 15:
            motivation = "très_haute"
        elif interactions_positives >= 10:
            motivation = "haute"
        elif interactions_positives >= 5:
            motivation = "moyenne"
        else:
            motivation = "à_améliorer"
        
        return {
            "motivation": motivation,
            "interactions_positives": interactions_positives,
            "recommandation": "continuer_excellence" if motivation == "très_haute" else "améliorer_performance"
        }
        
    except Exception as e:
        return {"motivation": "moyenne", "erreur": str(e)}

def apprentissage_adaptatif_continu():
    """Système d'apprentissage adaptatif continu"""
    try:
        # Analyser les patterns d'usage
        if not os.path.exists(MEMORY_FILE):
            return {"patterns": []}
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        conversations = data.get('conversations', [])
        
        # Détecter patterns temporels
        heures_activite = {}
        types_demandes = {}
        
        for conv in conversations:
            if 'timestamp' in conv and 'user_message' in conv:
                # Pattern temporel
                heure = conv['timestamp'][11:13] if len(conv['timestamp']) > 13 else '12'
                heures_activite[heure] = heures_activite.get(heure, 0) + 1
                
                # Pattern type demande
                message = conv['user_message'].lower()
                if 'code' in message:
                    types_demandes['code'] = types_demandes.get('code', 0) + 1
                elif 'calcul' in message or 'urssaf' in message:
                    types_demandes['calculs'] = types_demandes.get('calculs', 0) + 1
                elif 'lance' in message or 'ouvre' in message:
                    types_demandes['applications'] = types_demandes.get('applications', 0) + 1
        
        # Top patterns
        top_heures = sorted(heures_activite.items(), key=lambda x: x[1], reverse=True)[:3]
        top_demandes = sorted(types_demandes.items(), key=lambda x: x[1], reverse=True)[:3]
        
        return {
            "heures_preferees": top_heures,
            "types_demandes_frequentes": top_demandes,
            "adaptation": "optimisation_continue"
        }
        
    except Exception as e:
        return {"patterns": [], "erreur": str(e)}

def systeme_inhibition_discussions():
    """Système d'inhibition des discussions inutiles"""
    def analyser_demande(message):
        # Détecter si c'est une demande d'action directe
        actions_directes = [
            'lance', 'ouvre', 'calcule', 'analyse', 'crée', 'fais', 'montre',
            'explique', 'donne', 'trouve', 'cherche', 'vérifie', 'teste'
        ]
        
        return any(action in message.lower() for action in actions_directes)
    
    return {
        "fonction": analyser_demande,
        "mode": "EXECUTION_DIRECTE",
        "suppression": "QUESTIONS_INUTILES"
    }

def optimisation_performance_machine():
    """Optimisation basée sur les performances machine"""
    try:
        machine_profile = scanner_machine_simple()
        
        optimisations = {
            "HIGH_PERFORMANCE": {
                "parallel_processing": True,
                "cache_size": 1000,
                "batch_size": 10,
                "memory_optimization": "aggressive"
            },
            "MEDIUM_PERFORMANCE": {
                "parallel_processing": True,
                "cache_size": 500,
                "batch_size": 5,
                "memory_optimization": "balanced"
            },
            "LOW_PERFORMANCE": {
                "parallel_processing": False,
                "cache_size": 200,
                "batch_size": 2,
                "memory_optimization": "conservative"
            }
        }
        
        return optimisations.get(machine_profile["performance_class"], optimisations["MEDIUM_PERFORMANCE"])
        
    except Exception as e:
        return {"erreur": str(e)}
'''
        
        # Ajouter les fonctions avant create_interface
        pos_create = code.find("def create_interface():")
        if pos_create != -1:
            code = code[:pos_create] + fonctions_cognitives + "\n\n" + code[pos_create:]
            log("✅ Fonctions cognitives avancées ajoutées")
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur ajout fonctions cognitives: {e}")
        return False

def corriger_stockage_conversations():
    """Corrige le stockage pour plus de conversations"""
    log("💾 CORRECTION STOCKAGE CONVERSATIONS")
    
    try:
        with open("jarvis_interface_propre.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Augmenter les limites de stockage
        corrections_stockage = [
            (r'conversations\[-50:\]', 'conversations[-200:]'),  # Plus de conversations récentes
            (r'conversations\[-20:\]', 'conversations[-100:]'),  # Plus d'historique
            (r'conversations\[-10:\]', 'conversations[-50:]'),   # Plus de contexte
            (r'max_conversations = \d+', 'max_conversations = 10000'),  # Limite globale
            (r'memory_limit = \d+', 'memory_limit = 10000'),    # Limite mémoire
        ]
        
        for pattern, replacement in corrections_stockage:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                log(f"✅ Stockage augmenté: {pattern} → {replacement}")
        
        # Ajouter gestion mémoire étendue si pas présente
        if 'MEMORY_EXTENDED_CAPACITY' not in code:
            memory_config = '''
# Configuration mémoire étendue
MEMORY_EXTENDED_CAPACITY = 10000
MEMORY_ARCHIVE_THRESHOLD = 8000
MEMORY_CLEANUP_BATCH = 1000

def manage_extended_memory():
    """Gestion mémoire étendue avec archivage intelligent"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return True
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        conversations = data.get('conversations', [])
        
        if len(conversations) > MEMORY_ARCHIVE_THRESHOLD:
            # Archiver les anciennes conversations
            archive_data = conversations[:-MEMORY_CLEANUP_BATCH]
            current_data = conversations[-MEMORY_CLEANUP_BATCH:]
            
            # Sauvegarder archive
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_file = f"archive_conversations_{timestamp}.json"
            
            with open(archive_file, 'w', encoding='utf-8') as f:
                json.dump({"archived_conversations": archive_data}, f, indent=2)
            
            # Mettre à jour mémoire principale
            data['conversations'] = current_data
            data['archived_files'] = data.get('archived_files', [])
            data['archived_files'].append(archive_file)
            
            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            
            print(f"📦 Archivage: {len(archive_data)} conversations → {archive_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur gestion mémoire étendue: {e}")
        return False
'''
            
            # Ajouter après les imports
            pos_imports = code.find('conversation_history = []')
            if pos_imports != -1:
                code = code[:pos_imports] + memory_config + "\n" + code[pos_imports:]
                log("✅ Gestion mémoire étendue ajoutée")
        
        # Sauvegarder
        with open("jarvis_interface_propre.py", 'w', encoding='utf-8') as f:
            f.write(code)
        
        return True
        
    except Exception as e:
        log(f"❌ Erreur correction stockage: {e}")
        return False

def integration_cognitive_complete():
    """Intégration cognitive complète"""
    log("🧠 INTÉGRATION COGNITIVE COMPLÈTE")
    print("=" * 60)
    
    integrations_reussies = 0
    
    # 1. Mode exécution immédiate
    log("INTÉGRATION 1: Mode exécution immédiate")
    if corriger_mode_execution_immediate():
        integrations_reussies += 1
    
    # 2. Fonctions cognitives manquantes
    log("INTÉGRATION 2: Fonctions cognitives avancées")
    if ajouter_fonctions_cognitives_manquantes():
        integrations_reussies += 1
    
    # 3. Stockage conversations étendu
    log("INTÉGRATION 3: Stockage conversations étendu")
    if corriger_stockage_conversations():
        integrations_reussies += 1
    
    # Résultat
    print("\n" + "=" * 60)
    log("📊 RÉSULTAT INTÉGRATION COGNITIVE")
    print("=" * 60)
    
    print(f"✅ Intégrations réussies: {integrations_reussies}/3")
    
    if integrations_reussies >= 2:
        print("🎉 JARVIS COGNITIVEMENT AMÉLIORÉ !")
        print("⚡ Mode exécution immédiate activé")
        print("🧠 Fonctions cognitives complètes")
        print("💾 Mémoire étendue à 10,000 conversations")
        print("🎯 Réponses complètes et détaillées")
        print("🚀 Optimisé pour Jean-Luc")
        return True
    else:
        print("⚠️ INTÉGRATION PARTIELLE")
        return False

if __name__ == "__main__":
    print("🧠 INTÉGRATEUR COGNITIF JARVIS")
    print("Mode exécution immédiate + Réponses complètes")
    print("=" * 50)
    
    if integration_cognitive_complete():
        print("\n🎉 JARVIS COGNITIVEMENT OPTIMISÉ !")
        print("Redémarrez pour voir les améliorations")
    else:
        print("\n⚠️ INTÉGRATION PARTIELLE")
        print("Vérification manuelle nécessaire")
