#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TESTEUR JARVIS CORRIGÉ - JEAN-LUC PASSAVE
Teste rapidement que JARVIS fonctionne après correction
"""

import subprocess
import time
import requests
import os

def verifier_deepseek():
    """Vérifie si DeepSeek fonctionne"""
    try:
        response = requests.get("http://localhost:8000/v1/models", timeout=5)
        return response.status_code == 200
    except:
        return False

def lancer_test_jarvis():
    """Lance JARVIS en mode test"""
    print("🧪 TEST JARVIS CORRIGÉ")
    print("=" * 30)
    
    # Vérifier DeepSeek
    if verifier_deepseek():
        print("✅ DeepSeek R1 8B détecté sur localhost:8000")
    else:
        print("⚠️  DeepSeek non détecté - JARVIS fonctionnera en mode dégradé")
    
    # Vérifier le fichier corrigé
    if not os.path.exists("jarvis_interface_propre.py"):
        print("❌ jarvis_interface_propre.py non trouvé")
        return False
    
    print("✅ Fichier JARVIS trouvé")
    
    # Vérifier l'environnement virtuel
    if os.path.exists("venv_deepseek/bin/activate"):
        print("✅ Environnement virtuel trouvé")
        cmd = "source venv_deepseek/bin/activate && python jarvis_interface_propre.py"
    else:
        print("⚠️  Environnement virtuel non trouvé - utilisation Python système")
        cmd = "python3 jarvis_interface_propre.py"
    
    print(f"\n🚀 LANCEMENT JARVIS...")
    print(f"📱 Interface sera sur: http://localhost:7863")
    print(f"💻 Commande: {cmd}")
    print("\n" + "="*50)
    
    # Lancer JARVIS
    try:
        process = subprocess.Popen(
            cmd,
            shell=True,
            cwd="/Volumes/seagate/Louna_Electron_Latest"
        )
        
        print("✅ JARVIS lancé avec succès !")
        print("🌐 Ouvrez http://localhost:7863 dans votre navigateur")
        print("\n💡 CORRECTIONS APPLIQUÉES :")
        print("   ✅ Affichage horizontal corrigé")
        print("   ✅ CSS anti-horizontal ajouté")
        print("   ✅ Imports inutiles supprimés")
        print("   ✅ Configuration optimisée")
        
        print("\n🔧 Pour arrêter JARVIS : Ctrl+C dans le terminal")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lancement: {e}")
        return False

if __name__ == "__main__":
    lancer_test_jarvis()
