#!/usr/bin/env python3
"""
📊 JARVIS MONITORING DASHBOARD
Tableau de bord pour surveiller l'activité créative et cognitive de JARVIS en temps réel

Créé pour Jean<PERSON>ave
"""

import gradio as gr
import json
import time
from datetime import datetime, timedelta
import os
import threading

class JarvisMonitoringDashboard:
    def __init__(self):
        self.refresh_interval = 5  # Rafraîchissement toutes les 5 secondes
        self.is_monitoring = True
        
        # Fichiers à surveiller
        self.files_to_monitor = {
            "creative_projects": "jarvis_creative_projects.json",
            "cognitive_state": "jarvis_cognitive_state.json",
            "autonomous_thoughts": "jarvis_autonomous_thoughts.json",
            "deep_reflections": "jarvis_deep_reflections.json",
            "whatsapp_sent": "jarvis_whatsapp_sent.json",
            "creative_notifications": "jarvis_creative_notifications.json"
        }

    def get_system_status(self):
        """STATUT GÉNÉRAL DU SYSTÈME"""
        try:
            status = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "modules": {},
                "overall_health": "🟢 Excellent"
            }
            
            # Vérifier chaque module
            modules_status = {
                "Créativité Autonome": self.check_creative_module(),
                "Moteur Cognitif": self.check_cognitive_module(),
                "WhatsApp API": self.check_whatsapp_module(),
                "Mémoire Thermique": self.check_thermal_memory(),
                "Interface Principale": self.check_main_interface(),
                "Synchronisation T7": self.check_t7_sync_module()
            }
            
            status["modules"] = modules_status
            
            # Calculer la santé globale
            active_modules = sum(1 for s in modules_status.values() if "🟢" in s)
            total_modules = len(modules_status)
            
            if active_modules == total_modules:
                status["overall_health"] = "🟢 Excellent"
            elif active_modules >= total_modules * 0.8:
                status["overall_health"] = "🟡 Bon"
            else:
                status["overall_health"] = "🔴 Attention"
            
            return status
            
        except Exception as e:
            return {"error": f"Erreur statut système: {e}"}

    def check_creative_module(self):
        """VÉRIFIE LE MODULE CRÉATIF"""
        try:
            if os.path.exists("jarvis_creative_projects.json"):
                with open("jarvis_creative_projects.json", 'r') as f:
                    projects = json.load(f)
                return f"🟢 Actif ({len(projects)} projets)"
            else:
                return "🟡 Inactif (pas de projets)"
        except:
            return "🔴 Erreur"

    def check_cognitive_module(self):
        """VÉRIFIE LE MOTEUR COGNITIF"""
        try:
            if os.path.exists("jarvis_cognitive_state.json"):
                with open("jarvis_cognitive_state.json", 'r') as f:
                    state = json.load(f)
                current_state = state.get("current_state", "inconnu")
                total_thoughts = state.get("total_thoughts", 0)
                return f"🟢 Actif ({current_state}, {total_thoughts} pensées)"
            else:
                return "🟡 Inactif"
        except:
            return "🔴 Erreur"

    def check_whatsapp_module(self):
        """VÉRIFIE LE MODULE WHATSAPP"""
        try:
            if os.path.exists("jarvis_whatsapp_config.json"):
                with open("jarvis_whatsapp_config.json", 'r') as f:
                    config = json.load(f)
                if config.get("twilio_account_sid", "").startswith("YOUR_"):
                    return "🟡 Non configuré"
                else:
                    return "🟢 Configuré"
            else:
                return "🔴 Non installé"
        except:
            return "🔴 Erreur"

    def check_thermal_memory(self):
        """VÉRIFIE LA MÉMOIRE THERMIQUE"""
        try:
            if os.path.exists("thermal_memory_persistent.json"):
                with open("thermal_memory_persistent.json", 'r') as f:
                    memory = json.load(f)
                conversations = memory.get("conversations", [])
                return f"🟢 Actif ({len(conversations)} conversations)"
            else:
                return "🔴 Inactif"
        except:
            return "🔴 Erreur"

    def check_main_interface(self):
        """VÉRIFIE L'INTERFACE PRINCIPALE"""
        try:
            if os.path.exists("jarvis_interface_propre.py"):
                return "🟢 Disponible"
            else:
                return "🔴 Manquant"
        except:
            return "🔴 Erreur"

    def check_t7_sync_module(self):
        """VÉRIFIE LE MODULE DE SYNCHRONISATION T7"""
        try:
            # Vérifier que le module T7 existe
            if not os.path.exists('jarvis_t7_sync_engine.py'):
                return "🔴 Module manquant"

            # Essayer d'importer et vérifier le statut
            try:
                import sys
                import importlib.util

                spec = importlib.util.spec_from_file_location("jarvis_t7_sync_engine", "jarvis_t7_sync_engine.py")
                t7_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(t7_module)

                status = t7_module.get_t7_status()

                if status.get("t7_available", False):
                    auto_sync = "✅" if status.get("auto_sync", False) else "⏸️"
                    return f"🟢 T7 Connecté {auto_sync}"
                else:
                    return "🟡 T7 Déconnecté"

            except Exception as e:
                return f"🔴 Erreur: {str(e)[:30]}"
        except:
            return "🔴 Erreur module"

    def get_creative_activity(self):
        """ACTIVITÉ CRÉATIVE RÉCENTE"""
        try:
            if not os.path.exists("jarvis_creative_projects.json"):
                return "Aucun projet créatif trouvé"
            
            with open("jarvis_creative_projects.json", 'r') as f:
                projects = json.load(f)
            
            if not projects:
                return "Aucun projet créatif"
            
            # Derniers projets (5 max)
            recent_projects = projects[-5:]
            
            activity_html = "<h3>🎨 Activité Créative Récente</h3>"
            
            for project in reversed(recent_projects):
                timestamp = project.get("timestamp", "")
                project_type = project.get("type", "inconnu")
                topic = project.get("topic", "")
                
                # Formatage de la date
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime("%d/%m %H:%M")
                except:
                    time_str = timestamp[:16] if timestamp else "Date inconnue"
                
                activity_html += f"""
                <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;">
                    <strong>📅 {time_str}</strong> - 
                    <span style="color: #e91e63;">🎨 {project_type.upper()}</span><br>
                    <small>🎯 Thème: {topic}</small>
                </div>
                """
            
            return activity_html
            
        except Exception as e:
            return f"Erreur activité créative: {e}"

    def get_cognitive_activity(self):
        """ACTIVITÉ COGNITIVE RÉCENTE"""
        try:
            cognitive_html = "<h3>🧠 État Cognitif Actuel</h3>"
            
            # État cognitif
            if os.path.exists("jarvis_cognitive_state.json"):
                with open("jarvis_cognitive_state.json", 'r') as f:
                    state = json.load(f)
                
                current_state = state.get("current_state", "inconnu")
                state_duration = state.get("state_duration", 0)
                total_thoughts = state.get("total_thoughts", 0)
                
                cognitive_html += f"""
                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f8f9fa;">
                    <strong>🧠 État actuel:</strong> <span style="color: #2196f3;">{current_state}</span><br>
                    <strong>⏱️ Durée:</strong> {state_duration} cycles<br>
                    <strong>💭 Total pensées:</strong> {total_thoughts}
                </div>
                """
            
            # Pensées récentes
            if os.path.exists("jarvis_autonomous_thoughts.json"):
                with open("jarvis_autonomous_thoughts.json", 'r') as f:
                    thoughts = json.load(f)
                
                if thoughts:
                    cognitive_html += "<h4>💭 Pensées Récentes</h4>"
                    recent_thoughts = thoughts[-3:]  # 3 dernières pensées
                    
                    for thought in reversed(recent_thoughts):
                        timestamp = thought.get("timestamp", "")
                        cognitive_state = thought.get("cognitive_state", "")
                        
                        try:
                            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            time_str = dt.strftime("%d/%m %H:%M")
                        except:
                            time_str = timestamp[:16] if timestamp else "Date inconnue"
                        
                        cognitive_html += f"""
                        <div style="border-left: 3px solid #9c27b0; padding: 8px; margin: 5px 0; background: #f3e5f5;">
                            <strong>📅 {time_str}</strong> - 
                            <span style="color: #9c27b0;">🧠 {cognitive_state}</span>
                        </div>
                        """
            
            return cognitive_html
            
        except Exception as e:
            return f"Erreur activité cognitive: {e}"

    def get_communication_activity(self):
        """ACTIVITÉ DE COMMUNICATION"""
        try:
            comm_html = "<h3>📱 Communication WhatsApp</h3>"
            
            # Messages envoyés
            if os.path.exists("jarvis_whatsapp_sent.json"):
                with open("jarvis_whatsapp_sent.json", 'r') as f:
                    sent_messages = json.load(f)
                
                comm_html += f"""
                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #e8f5e8;">
                    <strong>📤 Messages envoyés:</strong> {len(sent_messages)}<br>
                    <strong>📅 Dernier envoi:</strong> {sent_messages[-1].get('timestamp', 'Jamais')[:16] if sent_messages else 'Jamais'}
                </div>
                """
            else:
                comm_html += """
                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #fff3e0;">
                    <strong>📤 Messages envoyés:</strong> 0<br>
                    <strong>📅 Dernier envoi:</strong> Jamais
                </div>
                """
            
            # Notifications créatives
            if os.path.exists("jarvis_creative_notifications.json"):
                with open("jarvis_creative_notifications.json", 'r') as f:
                    content = f.read().strip()
                    if content:
                        notifications = [json.loads(line) for line in content.split('\n') if line.strip()]
                        comm_html += f"""
                        <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #fce4ec;">
                            <strong>🎨 Notifications créatives:</strong> {len(notifications)}<br>
                            <strong>📅 Dernière notification:</strong> {notifications[-1].get('timestamp', 'Jamais')[:16] if notifications else 'Jamais'}
                        </div>
                        """
            
            return comm_html
            
        except Exception as e:
            return f"Erreur activité communication: {e}"

    def get_performance_metrics(self):
        """MÉTRIQUES DE PERFORMANCE"""
        try:
            metrics_html = "<h3>📊 Métriques de Performance</h3>"
            
            # Calculs de performance
            now = datetime.now()
            last_24h = now - timedelta(hours=24)
            
            # Projets créés dans les dernières 24h
            creative_count_24h = 0
            if os.path.exists("jarvis_creative_projects.json"):
                with open("jarvis_creative_projects.json", 'r') as f:
                    projects = json.load(f)
                
                for project in projects:
                    try:
                        project_time = datetime.fromisoformat(project.get("timestamp", ""))
                        if project_time > last_24h:
                            creative_count_24h += 1
                    except:
                        continue
            
            # Pensées générées dans les dernières 24h
            thoughts_count_24h = 0
            if os.path.exists("jarvis_autonomous_thoughts.json"):
                with open("jarvis_autonomous_thoughts.json", 'r') as f:
                    thoughts = json.load(f)
                
                for thought in thoughts:
                    try:
                        thought_time = datetime.fromisoformat(thought.get("timestamp", ""))
                        if thought_time > last_24h:
                            thoughts_count_24h += 1
                    except:
                        continue
            
            metrics_html += f"""
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 15px 0;">
                <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; background: #e3f2fd;">
                    <h4 style="margin: 0; color: #1976d2;">🎨 Créativité 24h</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #1976d2;">{creative_count_24h}</div>
                    <small>projets créés</small>
                </div>
                <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; background: #f3e5f5;">
                    <h4 style="margin: 0; color: #7b1fa2;">🧠 Cognition 24h</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #7b1fa2;">{thoughts_count_24h}</div>
                    <small>pensées générées</small>
                </div>
            </div>
            """
            
            # Taille des fichiers de données
            file_sizes = {}
            for name, filename in self.files_to_monitor.items():
                if os.path.exists(filename):
                    size = os.path.getsize(filename)
                    file_sizes[name] = f"{size / 1024:.1f} KB"
                else:
                    file_sizes[name] = "0 KB"
            
            metrics_html += "<h4>💾 Taille des Données</h4>"
            for name, size in file_sizes.items():
                metrics_html += f"<div><strong>{name}:</strong> {size}</div>"
            
            return metrics_html
            
        except Exception as e:
            return f"Erreur métriques: {e}"

    def create_dashboard_interface(self):
        """CRÉE L'INTERFACE DU TABLEAU DE BORD"""
        
        def refresh_dashboard():
            """RAFRAÎCHIT TOUTES LES DONNÉES"""
            status = self.get_system_status()
            creative_activity = self.get_creative_activity()
            cognitive_activity = self.get_cognitive_activity()
            communication_activity = self.get_communication_activity()
            performance_metrics = self.get_performance_metrics()
            
            # Formatage du statut système
            status_html = f"""
            <div style="background: linear-gradient(45deg, #4caf50, #45a049); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h2>🤖 JARVIS - Tableau de Bord Monitoring</h2>
                <div style="font-size: 18px;">
                    <strong>📅 Dernière mise à jour:</strong> {status['timestamp']}<br>
                    <strong>🏥 Santé globale:</strong> {status['overall_health']}
                </div>
            </div>
            
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                <h3>📋 Statut des Modules</h3>
                {chr(10).join([f"<div><strong>{module}:</strong> {status}</div>" for module, status in status['modules'].items()])}
            </div>
            """
            
            return (
                status_html,
                creative_activity,
                cognitive_activity,
                communication_activity,
                performance_metrics
            )
        
        # Interface Gradio
        with gr.Blocks(title="JARVIS Monitoring Dashboard", theme=gr.themes.Soft()) as dashboard:
            gr.HTML("<h1 style='text-align: center; color: #2196f3;'>📊 JARVIS MONITORING DASHBOARD</h1>")
            
            # Bouton de rafraîchissement
            refresh_btn = gr.Button("🔄 Rafraîchir", variant="primary")
            
            # Zones d'affichage
            with gr.Row():
                with gr.Column(scale=2):
                    system_status = gr.HTML()
                    performance_metrics = gr.HTML()
                
                with gr.Column(scale=1):
                    creative_activity = gr.HTML()
            
            with gr.Row():
                with gr.Column():
                    cognitive_activity = gr.HTML()
                with gr.Column():
                    communication_activity = gr.HTML()
            
            # Connexion du bouton de rafraîchissement
            refresh_btn.click(
                fn=refresh_dashboard,
                outputs=[
                    system_status,
                    creative_activity,
                    cognitive_activity,
                    communication_activity,
                    performance_metrics
                ]
            )
            
            # Rafraîchissement initial
            dashboard.load(
                fn=refresh_dashboard,
                outputs=[
                    system_status,
                    creative_activity,
                    cognitive_activity,
                    communication_activity,
                    performance_metrics
                ]
            )
        
        return dashboard

def launch_monitoring_dashboard():
    """LANCE LE TABLEAU DE BORD"""
    print("📊 Lancement du tableau de bord JARVIS...")
    
    dashboard = JarvisMonitoringDashboard()
    interface = dashboard.create_dashboard_interface()
    
    # Lancer sur un port différent pour éviter les conflits
    interface.launch(
        server_name="0.0.0.0",
        server_port=7865,  # Port modifié pour éviter conflit
        share=False,
        show_error=True,
        quiet=False
    )

if __name__ == "__main__":
    launch_monitoring_dashboard()
