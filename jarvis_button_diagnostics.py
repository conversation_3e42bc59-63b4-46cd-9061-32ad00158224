#!/usr/bin/env python3
"""
🔧🔍 JARVIS BUTTON DIAGNOSTICS
Système de diagnostic en temps réel pour tous les boutons JARVIS

Créé pour Jean-Luc Passave - JARVIS Vision
"""

import os
import sys
import json
import time
import traceback
import importlib
import subprocess
from datetime import datetime
from typing import Dict, Any, Callable

class JarvisButtonDiagnostics:
    def __init__(self):
        self.diagnostic_results = {}
        self.error_solutions = {
            # Erreurs modules
            "ModuleNotFoundError": "📦 Module manquant - Installer avec pip install",
            "ImportError": "🔗 Erreur d'import - Vérifier les dépendances",
            
            # Erreurs fichiers
            "FileNotFoundError": "📁 Fichier manquant - Vérifier le chemin",
            "PermissionError": "🔒 Permission refusée - Vérifier les droits",
            
            # Erreurs réseau
            "ConnectionError": "🌐 Connexion échouée - Vérifier Internet/VPN",
            "TimeoutError": "⏱️ Timeout - Serveur trop lent",
            "requests.exceptions.ConnectionError": "🌐 Erreur réseau - Vérifier la connexion",
            
            # Erreurs audio/vidéo
            "OSError": "🎤 Périphérique indisponible - Vérifier micro/caméra",
            "AttributeError": "⚙️ Fonction manquante - Code incomplet",
            
            # Erreurs génériques
            "ValueError": "📊 Valeur incorrecte - Paramètres invalides",
            "TypeError": "🔧 Type incorrect - Erreur de programmation",
            "KeyError": "🗝️ Clé manquante - Configuration incomplète"
        }
    
    def diagnose_function(self, func_name: str, func: Callable, *args, **kwargs):
        """DIAGNOSTIQUER UNE FONCTION AVANT EXÉCUTION"""
        diagnosis = {
            "function": func_name,
            "status": "unknown",
            "error": None,
            "solution": None,
            "details": {},
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 1. VÉRIFIER QUE LA FONCTION EXISTE
            if not callable(func):
                diagnosis["status"] = "error"
                diagnosis["error"] = "Fonction non callable"
                diagnosis["solution"] = "🔧 Vérifier que la fonction est correctement définie"
                return diagnosis
            
            # 2. VÉRIFIER LES DÉPENDANCES SPÉCIFIQUES
            dependency_check = self._check_dependencies(func_name)
            if not dependency_check["success"]:
                diagnosis["status"] = "error"
                diagnosis["error"] = dependency_check["error"]
                diagnosis["solution"] = dependency_check["solution"]
                diagnosis["details"] = dependency_check["details"]
                return diagnosis
            
            # 3. TESTER L'EXÉCUTION
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            diagnosis["status"] = "success"
            diagnosis["details"] = {
                "execution_time": f"{execution_time:.3f}s",
                "result_type": type(result).__name__,
                "result_length": len(str(result)) if result else 0
            }
            
            return diagnosis
            
        except Exception as e:
            diagnosis["status"] = "error"
            diagnosis["error"] = f"{type(e).__name__}: {str(e)}"
            diagnosis["solution"] = self._get_solution(type(e).__name__, str(e))
            diagnosis["details"] = {
                "traceback": traceback.format_exc(),
                "error_type": type(e).__name__
            }
            
            return diagnosis
    
    def _check_dependencies(self, func_name: str):
        """VÉRIFIER LES DÉPENDANCES SPÉCIFIQUES PAR FONCTION"""
        checks = {
            "success": True,
            "error": None,
            "solution": None,
            "details": {}
        }
        
        # DÉPENDANCES PAR FONCTION
        dependencies = {
            "quick_voice_input": ["speech_recognition", "pyaudio"],
            "speak_last_response": ["pyttsx3", "gTTS"],
            "camera_interface": ["cv2", "face_recognition"],
            "activate_camera_vision": ["cv2", "PIL"],
            "secure_web_search": ["requests", "urllib3"],
            "connect_vpn_interface": ["subprocess"],
            "whatsapp_interface": ["twilio"],
            "biometric_interface": ["face_recognition", "resemblyzer"],
            "music_interface": ["pygame", "librosa"],
            "force_t7_sync": ["psutil", "shutil"]
        }
        
        if func_name in dependencies:
            missing_modules = []
            for module in dependencies[func_name]:
                try:
                    importlib.import_module(module)
                    checks["details"][module] = "✅ Disponible"
                except ImportError:
                    missing_modules.append(module)
                    checks["details"][module] = "❌ Manquant"
            
            if missing_modules:
                checks["success"] = False
                checks["error"] = f"Modules manquants: {', '.join(missing_modules)}"
                checks["solution"] = f"📦 Installer: pip install {' '.join(missing_modules)}"
        
        # VÉRIFICATIONS SPÉCIFIQUES
        if func_name in ["quick_voice_input", "speak_last_response"]:
            # Vérifier périphériques audio
            try:
                import pyaudio
                p = pyaudio.PyAudio()
                device_count = p.get_device_count()
                p.terminate()
                checks["details"]["audio_devices"] = f"✅ {device_count} périphériques"
            except:
                checks["details"]["audio_devices"] = "❌ Aucun périphérique audio"
                if checks["success"]:
                    checks["success"] = False
                    checks["error"] = "Périphériques audio non disponibles"
                    checks["solution"] = "🎤 Vérifier que micro/haut-parleurs sont connectés"
        
        if func_name in ["camera_interface", "activate_camera_vision"]:
            # Vérifier caméra
            try:
                import cv2
                cap = cv2.VideoCapture(0)
                if cap.isOpened():
                    checks["details"]["camera"] = "✅ Caméra disponible"
                    cap.release()
                else:
                    checks["details"]["camera"] = "❌ Caméra inaccessible"
                    if checks["success"]:
                        checks["success"] = False
                        checks["error"] = "Caméra non accessible"
                        checks["solution"] = "📹 Vérifier permissions caméra et connexion"
            except:
                checks["details"]["camera"] = "❌ OpenCV non disponible"
        
        if func_name in ["secure_web_search", "connect_vpn_interface"]:
            # Vérifier connexion Internet
            try:
                import requests
                response = requests.get("https://httpbin.org/ip", timeout=5)
                if response.status_code == 200:
                    checks["details"]["internet"] = "✅ Connexion OK"
                else:
                    checks["details"]["internet"] = "⚠️ Connexion lente"
            except:
                checks["details"]["internet"] = "❌ Pas de connexion"
                if checks["success"]:
                    checks["success"] = False
                    checks["error"] = "Connexion Internet indisponible"
                    checks["solution"] = "🌐 Vérifier connexion Internet/VPN"
        
        if func_name in ["force_t7_sync", "check_t7_status_interface"]:
            # Vérifier disque T7
            t7_paths = ["/Volumes/T7", "/Volumes/T7 Shield", "/media/t7"]
            t7_found = False
            for path in t7_paths:
                if os.path.exists(path):
                    checks["details"]["t7_drive"] = f"✅ T7 trouvé: {path}"
                    t7_found = True
                    break
            
            if not t7_found:
                checks["details"]["t7_drive"] = "❌ T7 non connecté"
                if checks["success"]:
                    checks["success"] = False
                    checks["error"] = "Disque T7 non trouvé"
                    checks["solution"] = "💾 Connecter le disque dur T7"
        
        return checks
    
    def _get_solution(self, error_type: str, error_message: str):
        """OBTENIR UNE SOLUTION POUR L'ERREUR"""
        # Solution spécifique par type d'erreur
        if error_type in self.error_solutions:
            base_solution = self.error_solutions[error_type]
        else:
            base_solution = "🔧 Erreur inconnue - Vérifier les logs"
        
        # Solutions spécifiques par message
        if "No module named" in error_message:
            module_name = error_message.split("'")[1] if "'" in error_message else "module"
            return f"📦 Installer le module: pip install {module_name}"
        
        if "Permission denied" in error_message:
            return "🔒 Exécuter avec sudo ou vérifier les permissions fichiers"
        
        if "Connection refused" in error_message:
            return "🌐 Vérifier que le serveur est démarré (localhost:8000)"
        
        if "No such file" in error_message:
            return "📁 Créer le fichier manquant ou vérifier le chemin"
        
        if "device" in error_message.lower() and "audio" in error_message.lower():
            return "🎤 Vérifier que le microphone est connecté et autorisé"
        
        if "camera" in error_message.lower() or "video" in error_message.lower():
            return "📹 Vérifier que la caméra est connectée et autorisée"
        
        return base_solution
    
    def create_diagnostic_wrapper(self, func_name: str, original_func: Callable):
        """CRÉER UN WRAPPER DIAGNOSTIQUE POUR UNE FONCTION"""
        def diagnostic_wrapper(*args, **kwargs):
            try:
                # Diagnostiquer avant exécution
                diagnosis = self.diagnose_function(func_name, original_func, *args, **kwargs)
                
                if diagnosis["status"] == "success":
                    # Fonction OK, retourner le résultat normal
                    return original_func(*args, **kwargs)
                else:
                    # Fonction en erreur, retourner diagnostic
                    return self._format_error_response(diagnosis)
                    
            except Exception as e:
                # Erreur inattendue
                return self._format_error_response({
                    "function": func_name,
                    "status": "error",
                    "error": f"{type(e).__name__}: {str(e)}",
                    "solution": self._get_solution(type(e).__name__, str(e)),
                    "timestamp": datetime.now().isoformat()
                })
        
        return diagnostic_wrapper
    
    def _format_error_response(self, diagnosis: Dict[str, Any]):
        """FORMATER LA RÉPONSE D'ERREUR POUR L'INTERFACE"""
        error_color = "#f44336"  # Rouge
        
        details_html = ""
        if diagnosis.get("details"):
            details_html = "<h4>🔍 Détails du diagnostic:</h4><ul>"
            for key, value in diagnosis["details"].items():
                details_html += f"<li><strong>{key}:</strong> {value}</li>"
            details_html += "</ul>"
        
        return f"""
        <div style="background: linear-gradient(45deg, {error_color}, #d32f2f); color: white; padding: 20px; border-radius: 15px; border: 2px solid #ff1744;">
            <h3>🚨 DIAGNOSTIC BOUTON: {diagnosis['function']}</h3>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <h4>❌ PROBLÈME DÉTECTÉ</h4>
                <p><strong>Erreur:</strong> {diagnosis.get('error', 'Erreur inconnue')}</p>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <h4>💡 SOLUTION RECOMMANDÉE</h4>
                <p><strong>{diagnosis.get('solution', 'Contacter le support')}</strong></p>
            </div>
            
            {details_html}
            
            <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-top: 15px;">
                <p><strong>📅 Diagnostic effectué:</strong> {diagnosis.get('timestamp', 'Inconnu')}</p>
                <p><strong>🔧 Statut:</strong> {diagnosis.get('status', 'Inconnu').upper()}</p>
            </div>
            
            <p style="margin-top: 15px; font-weight: bold;">
                🎯 Ce bouton sera réparé automatiquement dès que le problème sera résolu !
            </p>
        </div>
        """

# Instance globale
jarvis_diagnostics = JarvisButtonDiagnostics()

def diagnose_button(func_name: str, original_func: Callable):
    """FONCTION HELPER POUR DIAGNOSTIQUER UN BOUTON"""
    return jarvis_diagnostics.create_diagnostic_wrapper(func_name, original_func)

def test_all_functions():
    """TESTER TOUTES LES FONCTIONS PRINCIPALES"""
    functions_to_test = [
        "quick_voice_input",
        "speak_last_response", 
        "camera_interface",
        "secure_web_search",
        "force_t7_sync",
        "whatsapp_interface",
        "biometric_interface"
    ]
    
    results = {}
    for func_name in functions_to_test:
        try:
            # Simuler un test de la fonction
            diagnosis = jarvis_diagnostics._check_dependencies(func_name)
            results[func_name] = diagnosis
        except Exception as e:
            results[func_name] = {
                "success": False,
                "error": str(e),
                "solution": "🔧 Erreur de test"
            }
    
    return results

if __name__ == "__main__":
    print("🔧🔍 JARVIS BUTTON DIAGNOSTICS")
    print("=" * 50)
    
    # Test du système
    results = test_all_functions()
    
    for func_name, result in results.items():
        status = "✅" if result["success"] else "❌"
        print(f"{status} {func_name}: {result.get('error', 'OK')}")
    
    print("\n🎯 Système de diagnostic prêt !")
