#!/usr/bin/env python3
"""
🔧 DIAGNOSTIC COMPLET ELECTRON + JARVIS
Vérification de la connexion et résolution des problèmes
Créé pour Jean-Luc Passave
"""

import requests
import subprocess
import time
import json
import os
from datetime import datetime

def print_status(message, status="info"):
    """Affichage coloré des statuts"""
    colors = {
        "info": "\033[0;34m",      # Bleu
        "success": "\033[0;32m",   # Vert
        "warning": "\033[1;33m",   # J<PERSON>ne
        "error": "\033[0;31m",     # Rouge
        "reset": "\033[0m"         # Reset
    }
    
    icons = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }
    
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"{colors[status]}[{timestamp}] {icons[status]} {message}{colors['reset']}")

def check_jarvis_status():
    """Vérifier le statut de JARVIS"""
    print_status("Vérification du statut JARVIS...", "info")
    
    try:
        # Test connexion HTTP
        response = requests.get("http://127.0.0.1:7867", timeout=5)
        if response.status_code == 200:
            print_status("JARVIS accessible sur http://127.0.0.1:7867", "success")
            
            # Vérifier le contenu
            if "gradio" in response.text.lower():
                print_status("Interface Gradio détectée", "success")
                return True
            else:
                print_status("Réponse inattendue de JARVIS", "warning")
                return False
        else:
            print_status(f"JARVIS répond avec code {response.status_code}", "warning")
            return False
            
    except requests.exceptions.ConnectionError:
        print_status("JARVIS non accessible - Connexion refusée", "error")
        return False
    except requests.exceptions.Timeout:
        print_status("JARVIS non accessible - Timeout", "error")
        return False
    except Exception as e:
        print_status(f"Erreur vérification JARVIS: {e}", "error")
        return False

def check_jarvis_process():
    """Vérifier si le processus JARVIS est actif"""
    print_status("Vérification du processus JARVIS...", "info")
    
    try:
        result = subprocess.run(
            ["pgrep", "-f", "jarvis_interface_propre.py"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print_status(f"Processus JARVIS trouvé (PID: {', '.join(pids)})", "success")
            return True
        else:
            print_status("Aucun processus JARVIS trouvé", "error")
            return False
            
    except Exception as e:
        print_status(f"Erreur vérification processus: {e}", "error")
        return False

def check_electron_process():
    """Vérifier si Electron est actif"""
    print_status("Vérification du processus Electron...", "info")
    
    try:
        result = subprocess.run(
            ["pgrep", "-f", "jarvis_electron_force.js"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print_status(f"Processus Electron trouvé (PID: {', '.join(pids)})", "success")
            return True
        else:
            print_status("Aucun processus Electron trouvé", "error")
            return False
            
    except Exception as e:
        print_status(f"Erreur vérification Electron: {e}", "error")
        return False

def check_ports():
    """Vérifier les ports utilisés"""
    print_status("Vérification des ports...", "info")
    
    try:
        result = subprocess.run(
            ["lsof", "-i", ":7867"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print_status("Port 7867 utilisé:", "success")
            for line in result.stdout.strip().split('\n')[1:]:  # Skip header
                print(f"    {line}")
            return True
        else:
            print_status("Port 7867 libre", "warning")
            return False
            
    except Exception as e:
        print_status(f"Erreur vérification ports: {e}", "error")
        return False

def check_files():
    """Vérifier les fichiers nécessaires"""
    print_status("Vérification des fichiers...", "info")
    
    files_to_check = [
        "jarvis_interface_propre.py",
        "jarvis_electron_force.js",
        "package.json",
        "venv_deepseek/bin/activate"
    ]
    
    all_good = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print_status(f"✓ {file_path}", "success")
        else:
            print_status(f"✗ {file_path} manquant", "error")
            all_good = False
    
    return all_good

def suggest_fixes():
    """Suggérer des corrections"""
    print_status("Suggestions de correction:", "info")
    
    print("\n🔧 SOLUTIONS POSSIBLES :")
    print("1. Redémarrer JARVIS :")
    print("   cd /Volumes/seagate/Louna_Electron_Latest")
    print("   source venv_deepseek/bin/activate")
    print("   python jarvis_interface_propre.py")
    
    print("\n2. Redémarrer Electron :")
    print("   npm start")
    
    print("\n3. Script automatique :")
    print("   ./lancer_jarvis_electron_complet.sh")
    
    print("\n4. Vérifier les logs :")
    print("   tail -f jarvis.log")
    print("   tail -f electron.log")

def test_electron_connection():
    """Tester la connexion Electron vers JARVIS"""
    print_status("Test de connexion Electron → JARVIS...", "info")
    
    # Simuler la requête d'Electron
    try:
        response = requests.get("http://127.0.0.1:7867", timeout=3)
        if response.status_code == 200:
            print_status("Connexion Electron → JARVIS : OK", "success")
            return True
        else:
            print_status(f"Connexion échouée (code {response.status_code})", "error")
            return False
    except Exception as e:
        print_status(f"Connexion échouée: {e}", "error")
        return False

def main():
    """Diagnostic principal"""
    print("🔧 ================================")
    print("🤖 DIAGNOSTIC ELECTRON + JARVIS")
    print("🔧 ================================")
    print()
    
    # Tests de base
    jarvis_running = check_jarvis_process()
    jarvis_accessible = check_jarvis_status()
    electron_running = check_electron_process()
    ports_ok = check_ports()
    files_ok = check_files()
    connection_ok = test_electron_connection()
    
    print("\n📊 RÉSUMÉ DU DIAGNOSTIC :")
    print(f"  Processus JARVIS     : {'✅' if jarvis_running else '❌'}")
    print(f"  JARVIS accessible    : {'✅' if jarvis_accessible else '❌'}")
    print(f"  Processus Electron   : {'✅' if electron_running else '❌'}")
    print(f"  Port 7867           : {'✅' if ports_ok else '❌'}")
    print(f"  Fichiers présents   : {'✅' if files_ok else '❌'}")
    print(f"  Connexion E→J       : {'✅' if connection_ok else '❌'}")
    
    # Statut global
    if all([jarvis_running, jarvis_accessible, connection_ok]):
        print_status("🎉 TOUT FONCTIONNE CORRECTEMENT !", "success")
        print("\n🚀 Votre application Electron devrait afficher l'interface JARVIS")
        print("📱 Si l'écran est blanc, essayez Cmd+R pour recharger")
    else:
        print_status("🚨 PROBLÈMES DÉTECTÉS", "error")
        suggest_fixes()
    
    print("\n" + "="*50)

if __name__ == "__main__":
    main()
