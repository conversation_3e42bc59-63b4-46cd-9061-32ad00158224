#!/usr/bin/env python3
"""
🧪 TEST COMPLET SYSTÈME DE SUIVI JARVIS
Validation complète de toutes les fonctionnalités
Créé pour Jean-Luc Passave
"""

import json
import os
import time
import requests
import subprocess
from datetime import datetime

def print_test_header(test_name):
    """Afficher l'en-tête d'un test"""
    print(f"\n🧪 ================================")
    print(f"🤖 TEST: {test_name}")
    print(f"🧪 ================================")

def print_result(test_name, success, details=""):
    """Afficher le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   {details}")

def test_fichier_suivi_existe():
    """Test 1: Vérifier que le fichier de suivi existe"""
    print_test_header("FICHIER DE SUIVI")
    
    fichier_existe = os.path.exists("jarvis_feuille_suivi_24h.json")
    print_result("Fichier de suivi existe", fichier_existe)
    
    if fichier_existe:
        try:
            with open("jarvis_feuille_suivi_24h.json", 'r') as f:
                data = json.load(f)
            
            # Vérifier la structure
            structure_ok = all(key in data for key in [
                "date_creation", "derniere_mise_a_jour", "mode_sommeil_actif",
                "statistiques_globales", "suivi_par_jour", "activites_temps_reel"
            ])
            print_result("Structure JSON valide", structure_ok)
            
            # Vérifier les données
            nb_activites = len(data.get("activites_temps_reel", []))
            print_result("Activités enregistrées", nb_activites > 0, f"{nb_activites} activités")
            
            return True
        except Exception as e:
            print_result("Lecture fichier JSON", False, str(e))
            return False
    
    return False

def test_interface_jarvis_accessible():
    """Test 2: Vérifier que l'interface JARVIS est accessible"""
    print_test_header("INTERFACE JARVIS")
    
    try:
        response = requests.get("http://127.0.0.1:7867", timeout=5)
        accessible = response.status_code == 200
        print_result("Interface accessible", accessible, f"Code: {response.status_code}")
        
        if accessible:
            contenu_ok = "gradio" in response.text.lower()
            print_result("Interface Gradio détectée", contenu_ok)
            return True
        
    except Exception as e:
        print_result("Connexion interface", False, str(e))
    
    return False

def test_processus_actifs():
    """Test 3: Vérifier que les processus sont actifs"""
    print_test_header("PROCESSUS SYSTÈME")
    
    # Test processus JARVIS
    try:
        result = subprocess.run(["pgrep", "-f", "jarvis_interface_propre.py"], 
                              capture_output=True, text=True)
        jarvis_actif = result.returncode == 0
        if jarvis_actif:
            pids = result.stdout.strip().split('\n')
            print_result("Processus JARVIS", True, f"PID: {', '.join(pids)}")
        else:
            print_result("Processus JARVIS", False)
    except Exception as e:
        print_result("Vérification JARVIS", False, str(e))
        jarvis_actif = False
    
    # Test processus Electron
    try:
        result = subprocess.run(["pgrep", "-f", "jarvis_electron_force.js"], 
                              capture_output=True, text=True)
        electron_actif = result.returncode == 0
        if electron_actif:
            pids = result.stdout.strip().split('\n')
            print_result("Processus Electron", True, f"PID: {', '.join(pids)}")
        else:
            print_result("Processus Electron", False, "Non trouvé")
    except Exception as e:
        print_result("Vérification Electron", False, str(e))
        electron_actif = False
    
    return jarvis_actif

def test_visualiseur_suivi():
    """Test 4: Tester le visualiseur de suivi"""
    print_test_header("VISUALISEUR SUIVI")
    
    # Test résumé global
    try:
        result = subprocess.run([
            "python", "jarvis_visualiseur_suivi.py", "--resume"
        ], capture_output=True, text=True, timeout=10)
        
        resume_ok = result.returncode == 0 and "RÉSUMÉ GLOBAL JARVIS" in result.stdout
        print_result("Résumé global", resume_ok)
        
    except Exception as e:
        print_result("Test résumé", False, str(e))
        resume_ok = False
    
    # Test activités récentes
    try:
        result = subprocess.run([
            "python", "jarvis_visualiseur_suivi.py", "--recent", "3"
        ], capture_output=True, text=True, timeout=10)
        
        recent_ok = result.returncode == 0 and "ACTIVITÉS RÉCENTES" in result.stdout
        print_result("Activités récentes", recent_ok)
        
    except Exception as e:
        print_result("Test activités récentes", False, str(e))
        recent_ok = False
    
    return resume_ok and recent_ok

def test_donnees_temps_reel():
    """Test 5: Vérifier que les données sont mises à jour en temps réel"""
    print_test_header("DONNÉES TEMPS RÉEL")
    
    try:
        # Lire les données initiales
        with open("jarvis_feuille_suivi_24h.json", 'r') as f:
            data_initiale = json.load(f)
        
        nb_activites_initial = len(data_initiale.get("activites_temps_reel", []))
        derniere_maj_initiale = data_initiale.get("derniere_mise_a_jour", "")
        
        print(f"   📊 Activités initiales: {nb_activites_initial}")
        print(f"   🕐 Dernière MAJ: {derniere_maj_initiale.split('T')[1][:8] if 'T' in derniere_maj_initiale else derniere_maj_initiale}")
        
        # Attendre 65 secondes pour voir une nouvelle entrée
        print("   ⏳ Attente de 65 secondes pour nouvelle activité...")
        time.sleep(65)
        
        # Relire les données
        with open("jarvis_feuille_suivi_24h.json", 'r') as f:
            data_finale = json.load(f)
        
        nb_activites_final = len(data_finale.get("activites_temps_reel", []))
        derniere_maj_finale = data_finale.get("derniere_mise_a_jour", "")
        
        print(f"   📊 Activités finales: {nb_activites_final}")
        print(f"   🕐 Dernière MAJ: {derniere_maj_finale.split('T')[1][:8] if 'T' in derniere_maj_finale else derniere_maj_finale}")
        
        # Vérifier qu'il y a eu une mise à jour
        mise_a_jour_ok = (nb_activites_final > nb_activites_initial or 
                         derniere_maj_finale != derniere_maj_initiale)
        
        print_result("Mise à jour temps réel", mise_a_jour_ok, 
                    f"Nouvelles activités: {nb_activites_final - nb_activites_initial}")
        
        return mise_a_jour_ok
        
    except Exception as e:
        print_result("Test temps réel", False, str(e))
        return False

def test_fonctions_interface():
    """Test 6: Tester les fonctions de l'interface (simulation)"""
    print_test_header("FONCTIONS INTERFACE")
    
    try:
        # Importer les fonctions de l'interface
        import sys
        sys.path.append('.')
        
        # Test des fonctions (simulation car pas d'interface Gradio active)
        print("   🔍 Test des fonctions de suivi...")
        
        # Simuler l'appel des fonctions
        functions_exist = True
        
        # Vérifier que les fichiers Python existent
        files_to_check = [
            "jarvis_feuille_suivi_complet.py",
            "jarvis_visualiseur_suivi.py"
        ]
        
        for file_name in files_to_check:
            if os.path.exists(file_name):
                print_result(f"Fichier {file_name}", True)
            else:
                print_result(f"Fichier {file_name}", False)
                functions_exist = False
        
        return functions_exist
        
    except Exception as e:
        print_result("Test fonctions", False, str(e))
        return False

def test_mode_sommeil():
    """Test 7: Tester le mode sommeil (simulation)"""
    print_test_header("MODE SOMMEIL")
    
    try:
        # Lire l'état actuel
        with open("jarvis_feuille_suivi_24h.json", 'r') as f:
            data = json.load(f)
        
        mode_sommeil_actuel = data.get("mode_sommeil_actif", False)
        print_result("État mode sommeil lu", True, f"Actif: {mode_sommeil_actuel}")
        
        # Vérifier les statistiques de sommeil
        stats = data.get("statistiques_globales", {})
        nb_reveils = stats.get("nombre_reveils", 0)
        nb_endormissements = stats.get("nombre_endormissements", 0)
        
        print_result("Statistiques sommeil", True, 
                    f"Réveils: {nb_reveils}, Endormissements: {nb_endormissements}")
        
        return True
        
    except Exception as e:
        print_result("Test mode sommeil", False, str(e))
        return False

def generer_rapport_final(resultats):
    """Générer le rapport final des tests"""
    print(f"\n🏁 ================================")
    print(f"📊 RAPPORT FINAL DES TESTS")
    print(f"🏁 ================================")
    
    total_tests = len(resultats)
    tests_reussis = sum(resultats.values())
    pourcentage = (tests_reussis / total_tests) * 100
    
    print(f"📊 Tests réussis: {tests_reussis}/{total_tests} ({pourcentage:.1f}%)")
    print(f"🎯 Statut global: {'✅ SUCCÈS' if pourcentage >= 80 else '⚠️ PARTIEL' if pourcentage >= 60 else '❌ ÉCHEC'}")
    
    print(f"\n📋 DÉTAIL DES TESTS:")
    for test_name, success in resultats.items():
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}")
    
    if pourcentage >= 80:
        print(f"\n🎉 FÉLICITATIONS !")
        print(f"Le système de suivi JARVIS 24h/24 fonctionne parfaitement !")
        print(f"📊 Surveillance active, mode sommeil opérationnel")
        print(f"🔍 Interface accessible, données en temps réel")
    elif pourcentage >= 60:
        print(f"\n⚠️ SYSTÈME PARTIELLEMENT FONCTIONNEL")
        print(f"Quelques ajustements peuvent être nécessaires")
    else:
        print(f"\n❌ PROBLÈMES DÉTECTÉS")
        print(f"Le système nécessite des corrections")
    
    print(f"\n" + "="*50)

def main():
    """Fonction principale de test"""
    print("🧪 ================================")
    print("🤖 TEST COMPLET SYSTÈME SUIVI JARVIS")
    print("🧪 ================================")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Exécuter tous les tests
    resultats = {}
    
    resultats["Fichier de suivi"] = test_fichier_suivi_existe()
    resultats["Interface JARVIS"] = test_interface_jarvis_accessible()
    resultats["Processus système"] = test_processus_actifs()
    resultats["Visualiseur suivi"] = test_visualiseur_suivi()
    resultats["Données temps réel"] = test_donnees_temps_reel()
    resultats["Fonctions interface"] = test_fonctions_interface()
    resultats["Mode sommeil"] = test_mode_sommeil()
    
    # Générer le rapport final
    generer_rapport_final(resultats)

if __name__ == "__main__":
    main()
