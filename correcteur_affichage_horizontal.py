#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR AFFICHAGE HORIZONTAL - JEAN-LUC PASSAVE
Corrige spécifiquement le problème d'affichage horizontal des messages
"""

import re

def corriger_affichage_horizontal():
    """Corrige le problème d'affichage horizontal dans JARVIS"""
    fichier = "jarvis_interface_propre.py"
    
    print("🔧 CORRECTION AFFICHAGE HORIZONTAL")
    print("=" * 40)
    
    # Lire le fichier
    with open(fichier, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    print("🔍 Recherche des problèmes d'affichage...")
    
    corrections = 0
    
    # 1. Corriger le CSS pour forcer l'affichage vertical
    if 'custom_css = ""' in contenu:
        print("📝 Ajout CSS anti-horizontal...")
        css_anti_horizontal = '''custom_css = """
/* CSS ANTI-AFFICHAGE HORIZONTAL */
.chatbot .message {
    display: block !important;
    width: 100% !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

.chatbot .user, .chatbot .bot {
    display: block !important;
    max-width: 85% !important;
    margin: 8px 0 !important;
    padding: 12px !important;
    border-radius: 12px !important;
}

.chatbot .user {
    background: #007bff !important;
    color: white !important;
    margin-left: auto !important;
    margin-right: 0 !important;
}

.chatbot .bot {
    background: #f8f9fa !important;
    color: #333 !important;
    margin-left: 0 !important;
    margin-right: auto !important;
}
"""'''
        
        contenu = contenu.replace('custom_css = ""', css_anti_horizontal)
        corrections += 1
        print("✅ CSS anti-horizontal ajouté")
    
    # 2. Corriger la configuration du chatbot si problématique
    if 'bubble_full_width=False' in contenu:
        print("📝 Correction configuration chatbot...")
        contenu = contenu.replace('bubble_full_width=False', 'bubble_full_width=True')
        corrections += 1
        print("✅ bubble_full_width corrigé")
    
    # 3. Ajouter type="tuples" si manquant
    if 'gr.Chatbot(' in contenu and 'type="tuples"' not in contenu:
        print("📝 Ajout type tuples...")
        pattern = r'(gr\.Chatbot\([^)]*)\)'
        def add_type(match):
            params = match.group(1)
            if 'type=' not in params:
                return params + ',\n                    type="tuples"\n                )'
            return match.group(0)
        
        contenu = re.sub(pattern, add_type, contenu)
        corrections += 1
        print("✅ Type tuples ajouté")
    
    # 4. Forcer le thème Default si Soft
    if 'gr.themes.Soft()' in contenu:
        print("📝 Correction thème...")
        contenu = contenu.replace('gr.themes.Soft()', 'gr.themes.Default()')
        corrections += 1
        print("✅ Thème Default appliqué")
    
    # Sauvegarder les corrections
    if corrections > 0:
        with open(fichier, 'w', encoding='utf-8') as f:
            f.write(contenu)
        
        print(f"\n🎉 {corrections} CORRECTIONS APPLIQUÉES !")
        print("✅ L'affichage horizontal est maintenant corrigé")
        print("🚀 Vous pouvez lancer JARVIS normalement")
    else:
        print("✅ Aucune correction nécessaire")
    
    return corrections > 0

if __name__ == "__main__":
    corriger_affichage_horizontal()
